#ifndef __cps_MSG_H__
#define __cps_MSG_H__
#include "message.h"

#define MAX_MSG_LEN 4096

typedef int (*call_back)(char *msg, char *request_id);

void cps_request_init(msg_header_t *msg_header, char *data, unsigned int data_len, unsigned short main_type, unsigned short sub_type);
int cps_msg_body_comp(char *srv_ip, char *request_id, char *signature, char *data, unsigned int data_len, char *body_data, unsigned int *body_len);
int cps_msg_body_comp_spcial(
    char*         srv_ip,
    char*         request_id,
    char*         data,
    unsigned int  data_len,
    char*         body_data,
    unsigned int* body_len);
int cps_request_send(msg_header_t* msg_header, call_back cb, char* request_id);
#endif
