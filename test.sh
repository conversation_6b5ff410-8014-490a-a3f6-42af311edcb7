#!/bin/bash
# cursor-clean-auto.sh
# 自动清理 Cursor Remote-SSH 问题并修复 .bashrc/.profile

set -e

echo "=== 1. 备份配置文件 ==="
for file in /root/.bashrc /root/.profile; do
    if [ -f "$file" ]; then
        cp "$file" "$file.bak.$(date +%F-%H%M%S)"
        echo "已备份 $file -> $file.bak.$(date +%F-%H%M%S)"
    fi
done

echo "=== 2. 修复 .bashrc 和 .profile 输出污染 ==="
for file in /root/.bashrc /root/.profile; do
    if [ -f "$file" ]; then
        # 删除非注释行里的 echo / printf / fortune 等会输出的命令
        sed -i '/^[^#].*\b\(echo\|printf\|fortune\|cowsay\)\b/d' "$file"
    fi
done

echo "=== 3. 停止远程 Cursor 相关进程 ==="
pkill -f cursor-server 2>/dev/null || true
pkill -f multiplex-server 2>/dev/null || true

echo "=== 4. 删除坏的 token 文件 ==="
rm -f /run/user/0/cursor-remote-*.token* 2>/dev/null || true

echo "=== 5. 删除远程 Cursor 安装目录 ==="
rm -rf /root/.cursor-server || true

echo "=== 6. 清理完成 ==="
echo "✅ 已清理旧进程、坏 token、安装目录，并修复了 .bashrc / .profile"
echo "⚠️ 现在可以回到 Cursor 重新 Remote-SSH 连接"

create table IF NOT EXISTS srv_interface_ip
(
    srv_id          VARCHAR(16) PRIMARY KEY NOT NULL,   --服务id
    cluster_id      VARCHAR(16) DEFAULT '',     --集群id
    interface_name  VARCHAR(16) DEFAULT '',    --网卡名称
    cluster_ipv4    VARCHAR(64) DEFAULT '',     --集群虚ipv4
    cluster_ipv6    VARCHAR(64) DEFAULT '',     --集群虚ipv6
    tenant_name     VARCHAR(128) DEFAULT ''   --所属租户名称
);