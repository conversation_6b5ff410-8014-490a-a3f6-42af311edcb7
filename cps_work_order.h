/*
 * Project: base
 * Moudle: cps
 * File: cps_work_order.h
 * Created Date: 2023-08-29 14:57:56
 * Author: --
 * Description: --
 * -----
 * todo: modified
 * -----
 * Copyright (c)  Inc
 */
#ifndef _cps_WORK_ORDER_H
#define _cps_WORK_ORDER_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */
#define WORK_ORDER_TABLE "work_order_info"
#define TENANT_NAME_MAX_LEN 64
/* ======================================================================================
 * types
 */

enum {
    WORK_ORDER_TYPE_SOURCE,
    WORK_ORDER_TYPE_SPEC
};
/* ======================================================================================
 * declaration
 */
int cps_work_order_check_vsm(int argc,char* argv[]);
int cps_work_order_approval(int argc,char* argv[]);
int cps_work_order_create(int argc, char *argv[]);
#endif // _cps_WORK_ORDER_H
