/* 
c语言实现map
*/
#pragma once
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stddef.h>
#include <assert.h>
 
 /* ================= 用户可见 API ================= */
 
 typedef size_t (*map_hash_fn)(const void *key);
 typedef int    (*map_eq_fn)(const void *a, const void *b);          /* 相等：相等返回非零 */
 typedef void * (*map_dup_fn)(const void *p);                         /* 深拷贝；若返回 NULL 视为失败 */
 typedef void   (*map_free_fn)(void *p);
 
 typedef struct map map_t;
 
 /* 创建/销毁；传入 key/value 的 hash/相等 以及 可选的拷贝/释放回调（可为 NULL 表示浅拷贝/不释放） */
 map_t *map_create(map_hash_fn khash, map_eq_fn keq,
                   map_dup_fn kdup, map_free_fn kfree,
                   map_dup_fn vdup, map_free_fn vfree);
 void   map_destroy(map_t *m);
 
 /* 基本操作：insert 覆盖旧值；返回 1 表示插入新 key，0 表示覆盖旧值，-1 表示失败（如 OOM） */
 int    map_insert(map_t *m, const void *key, const void *value);
 /* 删除：成功返回 1，失败(不存在)返回 0 */
 int    map_erase(map_t *m, const void *key);
 /* 查找：存在返回 value 指针（内部所有权属于 map），否则返回 NULL */
 void  *map_find(const map_t *m, const void *key);
 /* 是否包含：返回 1/0 */
 int    map_contains(const map_t *m, const void *key);
 /* 尺寸/清空 */
 size_t map_size(const map_t *m);
 void   map_clear(map_t *m);
 
 /* 迭代器 */
 typedef struct {
     const map_t *m;
     size_t bucket_idx;
     void  *node; /* 内部用，不要依赖其类型 */
 } map_iter_t;
 /* 初始化迭代器，返回首个元素，若空表返回 0 */
 int map_iter_begin(const map_t *m, map_iter_t *it, const void **out_key, void **out_val);
 /* 获得下一个元素，返回 1 有效 / 0 结束 */
 int map_iter_next(map_iter_t *it, const void **out_key, void **out_val);
 
 /* ================= 内部实现 ================= */
 
 typedef struct map_entry {
     void *key;
     void *val;
     size_t h; /* 预存 hash，减少重复计算 */
     struct map_entry *next;
 } map_entry_t;
 
 struct map {
     map_hash_fn khash;
     map_eq_fn   keq;
     map_dup_fn  kdup;
     map_free_fn kfree;
     map_dup_fn  vdup;
     map_free_fn vfree;
 
     map_entry_t **buckets;
     size_t cap;     /* 桶数：始终为 2^n */
     size_t size;    /* 键值对数量 */
 };
 
 static size_t next_pow2(size_t x) {
     if (x < 8) return 8;
     x--;
     for (size_t i = 1; i < sizeof(size_t)*8; i <<= 1) x |= x >> i;
     return x + 1;
 }
 
 static map_entry_t *entry_new(map_t *m, const void *key, const void *val, size_t h) {
     map_entry_t *e = (map_entry_t*)malloc(sizeof(*e));
     if (!e) return NULL;
     if (m->kdup) {
         e->key = m->kdup(key);
         if (!e->key) { free(e); return NULL; }
     } else {
         /* 浅拷贝指针 */
         e->key = (void*)key;
     }
     if (m->vdup) {
         e->val = m->vdup(val);
         if (!e->val) {
             if (m->kfree && e->key) m->kfree(e->key);
             free(e);
             return NULL;
         }
     } else {
         e->val = (void*)val;
     }
     e->h = h;
     e->next = NULL;
     return e;
 }
 
 static void entry_free(map_t *m, map_entry_t *e) {
     if (m->kfree && e->key) m->kfree(e->key);
     if (m->vfree && e->val) m->vfree(e->val);
     free(e);
 }
 
 static int map_resize(map_t *m, size_t newcap) {
     newcap = next_pow2(newcap);
     map_entry_t **nb = (map_entry_t**)calloc(newcap, sizeof(*nb));
     if (!nb) return -1;
 
     /* 重新分布 */
     for (size_t i = 0; i < m->cap; ++i) {
         map_entry_t *cur = m->buckets[i];
         while (cur) {
             map_entry_t *nxt = cur->next;
             size_t idx = cur->h & (newcap - 1);
             cur->next = nb[idx];
             nb[idx] = cur;
             cur = nxt;
         }
     }
 
     free(m->buckets);
     m->buckets = nb;
     m->cap = newcap;
     return 0;
 }
 
 map_t *map_create(map_hash_fn khash, map_eq_fn keq,
                   map_dup_fn kdup, map_free_fn kfree,
                   map_dup_fn vdup, map_free_fn vfree) {
     if (!khash || !keq) return NULL;
     map_t *m = (map_t*)calloc(1, sizeof(*m));
     if (!m) return NULL;
     m->khash = khash;
     m->keq   = keq;
     m->kdup  = kdup;
     m->kfree = kfree;
     m->vdup  = vdup;
     m->vfree = vfree;
     m->cap   = 16;
     m->buckets = (map_entry_t**)calloc(m->cap, sizeof(*m->buckets));
     if (!m->buckets) { free(m); return NULL; }
     m->size  = 0;
     return m;
 }
 
 void map_destroy(map_t *m) {
     if (!m) return;
     map_clear(m);
     free(m->buckets);
     free(m);
 }
 
 size_t map_size(const map_t *m) { return m ? m->size : 0; }
 
 void map_clear(map_t *m) {
     if (!m) return;
     for (size_t i = 0; i < m->cap; ++i) {
         map_entry_t *cur = m->buckets[i];
         while (cur) {
             map_entry_t *nxt = cur->next;
             entry_free(m, cur);
             cur = nxt;
         }
         m->buckets[i] = NULL;
     }
     m->size = 0;
 }
 
 static map_entry_t *find_entry(const map_t *m, const void *key, size_t h, size_t *out_idx, map_entry_t **out_prev) {
     size_t idx = h & (m->cap - 1);
     if (out_idx) *out_idx = idx;
     map_entry_t *prev = NULL;
     map_entry_t *cur  = m->buckets[idx];
     while (cur) {
         if (cur->h == h && m->keq(cur->key, key)) {
             if (out_prev) *out_prev = prev;
             return cur;
         }
         prev = cur;
         cur = cur->next;
     }
     if (out_prev) *out_prev = NULL;
     return NULL;
 }
 
 int map_insert(map_t *m, const void *key, const void *val) {
     if (!m) return -1;
     /* 扩容：负载因子 > 0.75 */
     if ((m->size + 1.0) / (double)m->cap > 0.75) {
         if (map_resize(m, m->cap << 1) != 0) return -1;
     }
 
     size_t h = m->khash(key);
     size_t idx;
     map_entry_t *prev;
     map_entry_t *exist = find_entry(m, key, h, &idx, &prev);
     if (exist) {
         /* 覆盖旧值 */
         if (m->vfree && exist->val) m->vfree(exist->val);
         if (m->vdup) {
             exist->val = m->vdup(val);
             if (!exist->val) return -1;
         } else {
             exist->val = (void*)val;
         }
         return 0; /* 覆盖 */
     } else {
         map_entry_t *e = entry_new(m, key, val, h);
         if (!e) return -1;
         e->next = m->buckets[idx];
         m->buckets[idx] = e;
         m->size++;
         return 1; /* 新插入 */
     }
 }
 
 int map_erase(map_t *m, const void *key) {
     if (!m || m->size == 0) return 0;
     size_t h = m->khash(key);
     size_t idx;
     map_entry_t *prev;
     map_entry_t *e = find_entry(m, key, h, &idx, &prev);
     if (!e) return 0;
     if (prev) prev->next = e->next;
     else      m->buckets[idx] = e->next;
     entry_free(m, e);
     m->size--;
     return 1;
 }
 
 void *map_find(const map_t *m, const void *key) {
     if (!m || m->size == 0) return NULL;
     size_t h = m->khash(key);
     map_entry_t *e = find_entry(m, key, h, NULL, NULL);
     return e ? e->val : NULL;
 }
 
 int map_contains(const map_t *m, const void *key) {
     return map_find(m, key) != NULL;
 }
 
 /* 迭代器实现 */
 int map_iter_begin(const map_t *m, map_iter_t *it, const void **out_key, void **out_val) {
     if (!m || !it) return 0;
     it->m = m;
     it->bucket_idx = 0;
     it->node = NULL;
 
     /* 找到首个非空桶 */
     while (it->bucket_idx < m->cap) {
         map_entry_t *e = m->buckets[it->bucket_idx];
         if (e) {
             it->node = e;
             if (out_key) *out_key = e->key;
             if (out_val) *out_val = e->val;
             return 1;
         }
         it->bucket_idx++;
     }
     return 0;
 }
 
 int map_iter_next(map_iter_t *it, const void **out_key, void **out_val) {
     if (!it || !it->m || !it->node) return 0;
     map_entry_t *e = (map_entry_t*)it->node;
     if (e->next) {
         e = e->next;
         it->node = e;
         if (out_key) *out_key = e->key;
         if (out_val) *out_val = e->val;
         return 1;
     }
     /* 跳到下一个桶 */
     it->bucket_idx++;
     while (it->bucket_idx < it->m->cap) {
         map_entry_t *n = it->m->buckets[it->bucket_idx];
         if (n) {
             it->node = n;
             if (out_key) *out_key = n->key;
             if (out_val) *out_val = n->val;
             return 1;
         }
         it->bucket_idx++;
     }
     /* 结束 */
     it->node = NULL;
     return 0;
 }
 
 /* ================= 常用工具回调：字符串键 & 整型值 ================= */
 
 /* FNV-1a 64-bit */
 static size_t hash_cstr(const void *p) {
     const unsigned char *s = (const unsigned char*)p;
     uint64_t h = 1469598103934665603ULL;
     while (*s) {
         h ^= (uint64_t)(*s++);
         h *= 1099511628211ULL;
     }
     return (size_t)h;
 }
 static int eq_cstr(const void *a, const void *b) { return strcmp((const char*)a, (const char*)b) == 0; }
 static void *dup_cstr(const void *p) {
     const char *s = (const char*)p;
     size_t n = strlen(s) + 1;
     char *q = (char*)malloc(n);
     if (!q) return NULL;
     memcpy(q, s, n);
     return q;
 }
 static void free_std(void *p) { free(p); }
 
 /* int 的装箱/拷贝 */
 typedef struct { int v; } boxed_int;
 static void *dup_int_box(const void *p) {
     const int *iv = (const int*)p;
     boxed_int *b = (boxed_int*)malloc(sizeof(boxed_int));
     if (!b) return NULL;
     b->v = *iv;
     return b;
 }
 
 map_t *map_create_default(void) {
    return map_create(hash_cstr, eq_cstr, dup_cstr, free_std, dup_int_box, free_std);
 }

 /* ================= 示例 ================= */
 #ifdef MAP_DEMO
 int main(void) {
     map_t *m = map_create(hash_cstr, eq_cstr, dup_cstr, free_std, dup_int_box, free_std);
     if (!m) {
         fprintf(stderr, "map_create failed\n");
         return 1;
     }
 
     /* 插入一些数据 */
     int v;
     v = 10; map_insert(m, "apple", &v);
     v = 20; map_insert(m, "banana", &v);
     v = 30; map_insert(m, "cherry", &v);
 
     /* 覆盖已有键 */
     v = 40; map_insert(m, "banana", &v);
 
     /* 查找 */
     boxed_int *pv = (boxed_int*)map_find(m, "banana");
     if (pv) printf("banana = %d\n", pv->v);
 
     /* 遍历 */
     printf("iterate all (size=%zu):\n", map_size(m));
     map_iter_t it;
     const void *k;
     void *val;
     for (int ok = map_iter_begin(m, &it, &k, &val); ok; ok = map_iter_next(&it, &k, &val)) {
         printf("  %s -> %d\n", (const char*)k, ((boxed_int*)val)->v);
     }
 
     /* 删除 */
     map_erase(m, "apple");
     printf("after erase apple, contains(apple)=%d, size=%zu\n",
            map_contains(m, "apple"), map_size(m));
 
     map_destroy(m);
     return 0;
 }
 #endif
 