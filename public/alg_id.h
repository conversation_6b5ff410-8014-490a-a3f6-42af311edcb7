#ifndef _ALG_ID_H_
#define _ALG_ID_H_
#define HSM_SGD_SM1         0x00000100
#define HSM_SGD_SM1_ECB     0x00000101
#define HSM_SGD_SM1_CBC     0x00000102
#define HSM_SGD_SM1_CFB     0x00000104
#define HSM_SGD_SM1_OFB     0x00000108
#define HSM_SGD_SM1_MAC     0x00000110
#define HSM_SGD_SM1_CTR     0x00000120

#define HSM_SGD_SSF33       0x00000200
#define HSM_SGD_SSF33_ECB   0x00000201
#define HSM_SGD_SSF33_CBC   0x00000202
#define HSM_SGD_SSF33_CFB   0x00000204
#define HSM_SGD_SSF33_OFB   0x00000208
#define HSM_SGD_SSF33_MAC   0x00000210
#define HSM_SGD_SSF33_CTR   0x00000220

#define HSM_SGD_SM4         0x00000400
#define HSM_SGD_SM4_ECB     0x00000401
#define HSM_SGD_SM4_CBC     0x00000402
#define HSM_SGD_SM4_CFB     0x00000404
#define HSM_SGD_SM4_OFB     0x00000408
#define HSM_SGD_SM4_MAC     0x00000410
#define HSM_SGD_SM4_CTR     0x00000420

#define HSM_SGD_AES         0x00008000
#define HSM_SGD_AES_ECB     0x00008001
#define HSM_SGD_AES_CBC     0x00008002
#define HSM_SGD_AES_CFB     0x00008004
#define HSM_SGD_AES_OFB     0x00008008
#define HSM_SGD_AES_MAC     0x00008010
#define HSM_SGD_AES_CTR     0x00008020

#define HSM_SGD_DES         0x00002000
#define HSM_SGD_DES_ECB     0x00002001
#define HSM_SGD_DES_CBC     0x00002002
#define HSM_SGD_DES_CFB     0x00002004
#define HSM_SGD_DES_OFB     0x00002008
#define HSM_SGD_DES_MAC     0x00002010

#define HSM_SGD_3DES        0x00004000
#define HSM_SGD_3DES_ECB    0x00004001
#define HSM_SGD_3DES_CBC    0x00004002
#define HSM_SGD_3DES_CFB    0x00004004
#define HSM_SGD_3DES_OFB    0x00004008
#define HSM_SGD_3DES_MAC    0x00004010

#define HSM_SGD_SM7         0x00001000
#define HSM_SGD_SM7_ECB     0x00001001
#define HSM_SGD_SM7_CBC     0x00001002   
#define HSM_SGD_SM7_CFB     0x00001004      
#define HSM_SGD_SM7_OFB     0x00001008       
#define HSM_SGD_SM7_MAC     0x00001010
#define HSM_SGD_SM7_CTR     0x00001020

/**
*非对称密码算法标识
*/
#define HSM_SGD_RSA			0x00010000		//RSA算法
#define HSM_SGD_SM2			0x00020000		//SM2椭圆曲线密码算法
#define HSM_SGD_SM2_1		0x00020200		//SM2椭圆曲线签名算法
#define HSM_SGD_SM2_2		0x00020400		//SM2椭圆曲线密钥交换协议
#define HSM_SGD_SM2_3		0x00020800		//SM2椭圆曲线加密算法

#define HSM_SGD_ECDSA       0x00040000
#define HSM_SGD_ECDSA_1     0x00040200
#define HSM_SGD_EDDSA       0x00100000
#define HSM_SGD_EDDSA_1     0x00100200
#define HSM_SGD_DSA         0x00200200
#define HSM_SGD_DSA_1       0x00200200

/**
*密码杂凑算法标识
*/
#define HSM_SGD_SM3         0x00000001
#define HSM_SGD_SHA1        0x00000002
#define HSM_SGD_SHA256      0x00000004
#define HSM_SGD_SHA512      0x00000008
#define HSM_SGD_SHA224      0x00000020
#define HSM_SGD_SHA384      0x00000040

#define HSM_SGD_SM9         0x00080000
#define HSM_SGD_SM9_1       0x00080200
#define HSM_SGD_SM9_2       0x00080400
#define HSM_SGD_SM9_3       0x00080800
#define HSM_SGD_SM9_8_ECB	0x00080801
#define HSM_SGD_SM9_8_CBC	0x00080802
#define HSM_SGD_SM9_8_CFB	0x00080804
#define HSM_SGD_SM9_8_OFB	0x00080808
#define HSM_SGD_SM9_SM3		0x00080800

#endif
