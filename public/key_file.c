
#include "../cps_common.h"
#include "../database_api.h"
#include "alg_id.h"
#include "key_file.h"
#include "key_operate.h"
#include "key_store.h"

int kf_get_user_pub_key(int manager_id, void* key, int key_flag) {
    int              ret = 0;
    InterkeyStore    key_info;
    ECCrefPublicKey  pub_key;
    ECCrefPrivateKey pri_key;

    int alg_id = HSM_SGD_SM2_1;
    // db_get_internal_algid(HSM_SGD_SM2_1);
    // if (alg_id == -1) {
    //     DEBUG_CLI(ERR_DEBUG, "db_get_internal_algid failed!");
    //     ret = -1;
    //     goto out;
    // }

    key_info.key_node[0].iKeyType      = 0;
    key_info.key_node[0].iKeyIndex     = manager_id;
    key_info.key_node[0].iAlgId        = alg_id;
    key_info.key_node[0].iKeyFlag      = SIGN_KEY;
    key_info.key_node[0].iKeyBits      = 256;
    key_info.key_node[0].pKeyPriInfo   = &pri_key;
    key_info.key_node[0].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[0].iKeyPubLength = sizeof(ECCrefPublicKey);

    key_info.key_node[1].iKeyType      = 0;
    key_info.key_node[1].iKeyIndex     = manager_id;
    key_info.key_node[1].iAlgId        = alg_id;
    key_info.key_node[1].iKeyFlag      = ENC_KEY;
    key_info.key_node[1].iKeyBits      = 256;
    key_info.key_node[1].pKeyPriInfo   = &pri_key;
    key_info.key_node[1].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[1].iKeyPubLength = sizeof(ECCrefPublicKey);

    if (key_flag == 0) {
        key_info.key_node[0].pKeyPubInfo = key;
        key_info.key_node[1].pKeyPubInfo = &pub_key;
    } else {
        key_info.key_node[1].pKeyPubInfo = key;
        key_info.key_node[0].pKeyPubInfo = &pub_key;
    }

    // 从文件中获取对应的公钥信息
    ret = GetUsrKey(manager_id, key_flag + 1, &key_info, decrypt_with_kek);
    API_CHECK_FUNC(ret, "GetUsrKey");
out:
    return ret;
}

int kf_add_user_pub_key(int manager_id, void* sign_key, void* enc_key) {
    if (sign_key == NULL || enc_key == NULL) {
        return -1;
    }
    InterkeyStore key_info = {0};
    int           ret      = -1;

    int alg_id = HSM_SGD_SM2_1;
    // db_get_internal_algid(HSM_SGD_SM2_1);
    // if (alg_id == -1) {
    //     DEBUG_CLI(ERR_DEBUG, "db_get_internal_algid failed!");
    //     ret = -1;
    //     goto out;
    // }

    key_info.key_node[0].iKeyType      = 0;
    key_info.key_node[0].iKeyIndex     = manager_id;
    key_info.key_node[0].iAlgId        = alg_id;
    key_info.key_node[0].iKeyFlag      = SIGN_KEY;
    key_info.key_node[0].iKeyBits      = 256;
    key_info.key_node[0].pKeyPriInfo   = NULL;
    key_info.key_node[0].iKeyPriLength = 0;
    key_info.key_node[0].pKeyPubInfo   = sign_key;
    key_info.key_node[0].iKeyPubLength = sizeof(ECCrefPublicKey);

    key_info.key_node[1].iKeyType      = 0;
    key_info.key_node[1].iKeyIndex     = manager_id;
    key_info.key_node[1].iAlgId        = alg_id;
    key_info.key_node[1].iKeyFlag      = ENC_KEY;
    key_info.key_node[1].iKeyBits      = 256;
    key_info.key_node[1].pKeyPriInfo   = NULL;
    key_info.key_node[1].iKeyPriLength = 0;
    key_info.key_node[1].pKeyPubInfo   = enc_key;
    key_info.key_node[1].iKeyPubLength = sizeof(ECCrefPublicKey);

    ret = SetUsrKey(manager_id, SIGN_KEY, &key_info, encrypt_with_kek, decrypt_with_kek);
    API_CHECK_FUNC(ret, "SetKey2File");

    ret = SetUsrKey(manager_id, ENC_KEY, &key_info, encrypt_with_kek, decrypt_with_kek);
    API_CHECK_FUNC(ret, "SetKey2File");

out:
    return ret;
}