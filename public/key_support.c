/*
 * Project: KeyManagement
 * Moudle: module_name
 * File: database_api.c
 * Created Date: 2023-04-19 11:36:31
 * Author: CaoHongFa
 * Description:
 */

/* ======================================================================================
 * includes
 */
#include "key_support.h"
#include "sqlite3.h"
// #include "sdf_type.h"
#include <string.h>
#include "stdio.h"
#include "stdlib.h"


#define MAX_ACCOUNT_COUNT 690
#define MAX_DEVICE_COUNT  10
#define MAX_LMK_COUNT 128
void paddingKeyStructure(KeyCapBility *pCapbility,unsigned int iAlgFlag, unsigned int iKeyCount,unsigned int iKeyStatus,unsigned int iMaxBits){
    pCapbility->iActiveFlag = iKeyStatus;
    pCapbility->iKeyCount = iKeyCount;
    pCapbility->iKeyId = iAlgFlag;
    pCapbility->iMaxKeyBits = iMaxBits;
}

int Db_GetManagerSupport(KeyCapBility *pCapbility, unsigned int count){
    if( pCapbility == NULL || count != MAX_KEY_TYPE_SIZE )
        return -1;
#if 0

    sqlite3 *dbHandle = NULL;
    if (sqlite3_open(DB_FILE_PATH, &dbHandle)) {
        return -1;
    }
    memset( pCapbility, 0x00, count*sizeof(KeyCapBility));
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    unsigned int manager_count = 0;
    rc = sqlite3_get_table_printf(
        dbHandle,
        "select manager_count from ukey_manager_count",
        &result,
        &row,
        &col,
        &errmsg);
    if( rc != SQLITE_OK){
        if (errmsg) sqlite3_free(errmsg);
        sqlite3_close(dbHandle);
        printf("执行数据库查询操作失败\n");
        return -1;
    }
    if( row > 0 ){
         for (int i = 1; i <= row; i++)
            manager_count += atoi(result[col*i ]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    sqlite3_close(dbHandle);
#endif
    pCapbility[SM2_KEY].iActiveFlag =KEY_ACTIVE_STATUS;
    pCapbility[SM2_KEY].iKeyCount = MAX_ACCOUNT_COUNT;
    pCapbility[SM2_KEY].iMaxKeyBits = INTERNEL_ECCref_MAX_BITS;
    pCapbility[SM2_KEY].iKeyId = SM2_KEY;
    return 0;
}

int Db_GetDeviceSupport(KeyCapBility *pCapbility,unsigned int count){
    if( pCapbility == NULL || count != MAX_KEY_TYPE_SIZE )
        return -1;
    memset( pCapbility, 0x00, count*sizeof(KeyCapBility));
    pCapbility[SM2_KEY].iActiveFlag =KEY_ACTIVE_STATUS;
    pCapbility[SM2_KEY].iKeyCount = MAX_DEVICE_COUNT;
    pCapbility[SM2_KEY].iMaxKeyBits = INTERNEL_ECCref_MAX_BITS;
    pCapbility[SM2_KEY].iKeyId = SM2_KEY;
    return 0;
}

int GetKeyTypeFromAlgName(unsigned char* pName){
    if( strcmp(pName,"RSA")  == 0 )
        return RSA_KEY;
    if( strcmp(pName,"ECC")  == 0 )
        return ECC_KEY;
    if( strcmp(pName,"SM2")  == 0 )
        return SM2_KEY;
    if( strcmp(pName,"DSA")  == 0 )
        return DSA_KEY;
    if( strcmp(pName,"EcDSA")  == 0 )
        return ECDSA_KEY;
    if( strcmp(pName,"EdDSA")  == 0 )
        return EDDSA_KEY;
    if( strcmp(pName,"SM9_M")  == 0 )
        return SM9_MASTER_KEY;
    if( strcmp(pName,"SM9_U")  == 0 )
        return SM9_USER_KEY;
    return -1;
}

int Db_GetKeySupport( KeyCapBility *pCapbility, unsigned int count){
    sqlite3* dbHandle = NULL;
    if( pCapbility == NULL || count != MAX_KEY_TYPE_SIZE )
        return 1;
    memset( pCapbility, 0x00, count*sizeof(KeyCapBility));
    if (sqlite3_open(DB_FILE_PATH, &dbHandle))
        return -1;

    int    rc     = 0;
    char** result = NULL;
    int    row, col;
    unsigned int manager_count = 0;
    rc = sqlite3_get_table(
        dbHandle,
        "select symm_storage_max,rsa_storage_max,sm2_storage_max,ecc_storage_max,dsa_storage_max,ecdsa_storage_max,\
        eddsa_storage_max,sm9_m_storage_max,sm9_u_storage_max from hsm_key_support where storage_use_flag=1",
        &result,
        &row,
        &col,
        NULL);
    if( rc != SQLITE_OK){
        sqlite3_close(dbHandle);
        printf("执行数据库查询操作失败\n");
        return -1;
    }
    if( row > 0)
    {
        paddingKeyStructure(&pCapbility[SYMM_KEY],SYMM_KEY,atoi(result[col]),KEY_ACTIVE_STATUS,KEK_LENGTH*8);
        paddingKeyStructure(&pCapbility[RSA_KEY],RSA_KEY, atoi(result[col+1]),KEY_NO_ACTIVE_STATUS,INTERNEL_RSAref_MAX_BITS);
        paddingKeyStructure(&pCapbility[SM2_KEY],SM2_KEY,atoi(result[col + 2]),KEY_NO_ACTIVE_STATUS,INTERNEL_ECCref_MAX_BITS);
        paddingKeyStructure(&pCapbility[ECC_KEY],ECC_KEY,atoi(result[col + 3]),KEY_NO_ACTIVE_STATUS,INTERNEL_ECCref_MAX_BITS_ECDSA);
        paddingKeyStructure(&pCapbility[DSA_KEY],DSA_KEY, atoi(result[col + 4]),KEY_NO_ACTIVE_STATUS,INTERNEL_LiteDSAref_MAX_BITS);
        paddingKeyStructure(&pCapbility[ECDSA_KEY],ECDSA_KEY,atoi(result[col + 5]),KEY_NO_ACTIVE_STATUS,INTERNEL_EdDSAref_MAX_BITS);
        paddingKeyStructure(&pCapbility[EDDSA_KEY],EDDSA_KEY,atoi(result[col + 6]),KEY_NO_ACTIVE_STATUS,INTERNEL_EdDSAref_MAX_BITS);
        paddingKeyStructure(&pCapbility[SM9_MASTER_KEY],SM9_MASTER_KEY, atoi(result[col + 7]),KEY_NO_ACTIVE_STATUS,INTERNEL_SM9ref_MAX_BITS);
        paddingKeyStructure(&pCapbility[SM9_USER_KEY],SM9_USER_KEY, atoi(result[col + 8]),KEY_NO_ACTIVE_STATUS,INTERNEL_SM9ref_MAX_BITS);
        paddingKeyStructure(&pCapbility[LMK_KEY],LMK_KEY,MAX_LMK_COUNT,KEY_ACTIVE_STATUS,KEK_LENGTH*8);
    }
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    char** result1 = NULL;
       rc = sqlite3_get_table(
        dbHandle,
        "select alg_name from asymm_alg_support where use_flag=1",
        &result1,
        &row,
        &col,
        NULL);
    if( rc != SQLITE_OK){
        sqlite3_close(dbHandle);
        printf("执行数据库查询操作失败\n");
        return -1;
    }
    int key_type = 0;
    if( row > 0 ){
         for (int i = 1; i <= row; i++){
            if( (key_type = GetKeyTypeFromAlgName(result1[i*col ])) != -1) {
                pCapbility[key_type].iActiveFlag = KEY_ACTIVE_STATUS;
            }
         }
    }
    if (result1) {
        sqlite3_free_table(result1);
        result1 = NULL;
    }
    sqlite3_close(dbHandle);
    return 0;
}
