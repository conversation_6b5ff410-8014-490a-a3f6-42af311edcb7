
现在数据库里面有以下三张表，cps_vsm_manage.c文件中的pull_vsminfo_callback函数是从服务器拉取过来的json数据需要解析后存入这几个表中，其中golang服务器发送过来的ResultVSMInfo结构信息，包含的json数据格式如下：
现在要将这些获取的数据解析完后插入到对应的表中，目前pull_vsminfo_callback这个函数是之前实现的，请重新设计及实现，只在cps_vsm_manage.c文件中修改，不要动其他文件

create table IF NOT EXISTS vsm_manage						--虚拟机管理表
(
    id              INTEGER PRIMARY KEY AUTOINCREMENT,  --序号
    vsm_id          VARCHAR(64) NOT NULL,   --虚拟机标识
    request_id      VARCHAR(64) NOT NULL,   --request id
    tenant_name     VARCHAR(128) DEFAULT '',  --所属租户
    host_name       VARCHAR(128) DEFAULT '',--所属宿主机名称
    host_ip         VARCHAR(64) DEFAULT '', --宿主机ip
    vsm_type        INTEGER DEFAULT 0,      --虚拟机类型 1：密码机 2：签名验签服务器 3：时间戳系统
    vsm_name        VARCHAR(128) DEFAULT '', --虚拟机名称
    run_state       INTEGER DEFAULT 1,      --运行状态 0：就绪状态 1：初始状态 2：错误状态 3：关机状态 4：重启状态 5:离线状态
    opt_state       INTEGER DEFAULT 0,      --操作状态 0：创建中 1：已启动 2：关闭中 3：已关闭 4：重置中 5：重启中 6：删除中 7：影像导出中 8：启动中 9：影像导入中 10：配置修改中 11:升级中 12:升级成功，待重启  13:未启动
    cpu_model       INTEGER DEFAULT 0,      --cpu模式 0：独享 1：共享
    create_time     date DEFAULT '',        --创建时间
    create_spec     VARCHAR(64) DEFAULT 1,  --创建方案
    image_ver       VARCHAR(64) DEFAULT '', --镜像版本
    image_dig       VARCHAR(128) DEFAULT '',--镜像摘要
    ref_flag        INTEGER DEFAULT 0,      --应用标识 0：未被引用  1：已被引用
    cluster_status  VARCHAR(64) DEFAULT '', --集群信息
    update_flag     INTEGER,                --配置更新标识,0无需更新,1需要更新
    is_drift        INTEGER DEFAULT 0,      --是否为漂移虚拟机 0-否 1-是
    remark          VARCHAR(256) DEFAULT '' --备注信息
);

CREATE TABLE IF NOT EXISTS vsm_ip_info (
    id              INTEGER PRIMARY KEY AUTOINCREMENT,  -- 序号
    vsm_id          TEXT NOT NULL DEFAULT '',-- 虚拟机id，外键关联vsm_manage
    interface_name  TEXT NOT NULL DEFAULT '', -- 网卡名称：eth1, eth2等
    vsm_ipv4        TEXT DEFAULT '',          -- 虚拟机v4ip
    vsm_ipv6        TEXT DEFAULT '',          -- 虚拟机v6ip
    vsm_maskv4      TEXT DEFAULT '',         -- 虚拟机子网v4掩码
    vsm_maskv6      TEXT DEFAULT '',         -- 虚拟机子网v6掩码
    vsm_gatewayv4   TEXT DEFAULT '',         -- 虚拟机v4网关
    vsm_gatewayv6   TEXT DEFAULT '',        -- 虚拟机v6网关
    bindType        INTEGER DEFAULT 0,  -- 网络绑定类型0:桥口模式;1:OVS模式
    is_enabled      INTEGER DEFAULT 1,      -- 网卡是否启用
    manage          INTEGER DEFAULT 1,      -- 是否用于管理
    bridge_interface TEXT DEFAULT '',  -- 桥接口或OVS实例名称
    vlan_id         INTEGER DEFAULT NULL,   -- VLAN ID
    ping            INTEGER DEFAULT 1,      -- 网卡是否运行ping
    ext1            TEXT DEFAULT NULL,      -- 扩展字段1
    ext2            TEXT DEFAULT NULL,      -- 扩展字段2
    ext3            TEXT DEFAULT NULL,      -- 扩展字段3
    remark          TEXT DEFAULT ''         -- 备注信息
);


CREATE TABLE IF NOT EXISTS "tb_vsm_route" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT, -- 主键ID
    "vsmId" TEXT NOT NULL,              -- 虚拟机ID，外键关联tb_vsm_infos
    "interface_name" TEXT NOT NULL,     -- 网卡名称：如eth1, eth2等
    "route_type" INTEGER DEFAULT 1,     -- 静态路由类型1:IPV4路由2:IPV6路由
    "dest" TEXT DEFAULT NULL,     		-- 静态路由目标地址 IP/MASK
    "next" TEXT DEFAULT NULL    		-- 静态路由下一跳地址
);



// 获取VSM 详细信息
type ResultVSMInfo struct {
	ID            string `json:"id"`
	Version       string `json:"version"`
	IP            string `json:"ip"`
	Token         string `json:"token"`
	Mask          string `json:"mask"`
	Gateway       string `json:"gateway"`
	Digest        string `json:"digest"`
	Communication int    `json:"communication"`
	//2023/12/7 添加验签服务器和时间戳服务相关信息
	Extensions VsmExtensionInfo `json:"extensions,omitempty"`
}
//静态路由信息
type StaticRoute struct {
	Dest string `json:"dest,omitempty"` //目的地址 IP/MASK
	Next string `json:"next,omitempty"` //下一条
}
// 扩展网卡配置信息

type InterfaceInfo struct {
	BindType      int           `json:"bindType,omitempty"`      //0:桥口模式;1:OVS模式
	InterfaceName string        `json:"interfaceName,omitempty"` //网卡名字
	OvsInstance   string        `json:"ovsInstance,omitempty"`   //ovs实例名称
	BrigeName     string        `json:"brg,omitempty"`           //桥接口名称
	Vlan          int           `json:"vlan,omitempty"`          //虚机VlanID(BindType为1时有用)
	IsManage      int           `json:"manage,omitempty"`        //是否可用于管理网,1:仅用于管理；2:仅用于业务；3：尽可以管理业可以业务
	IsEnable      int           `json:"isEnable,omitempty"`      //网卡是否启用
	IpV4          string        `json:"ipv4,omitempty"`          //虚拟机IPV4
	MaskV4        string        `json:"maskv4,omitempty"`        //虚拟机掩码V4
	GatewayV4     string        `json:"gatewayv4,omitempty"`     //虚拟机网关V4
	StaticRouteV4 []StaticRoute `json:"staticRouteV4,omitempty"` //虚拟机静态路由V4
	IpV6          string        `json:"ipv6,omitempty"`          //虚拟机IPV6
	MaskV6        string        `json:"maskv6,omitempty"`        //虚拟机掩码V6
	GatewayV6     string        `json:"gatewayv6,omitempty"`     //虚拟机网关V6
	StaticRouteV6 []StaticRoute `json:"staticRouteV6,omitempty"` //虚拟机静态路由V6
	Ping          int           `json:"ping,omitempty"`          //ping是否启用
}
//扩展信息
type VsmExtensionInfo struct {
	SvsStatus     string          `json:"svsStatus,omitempty"` //签名验签服务器密钥存储状态
	TsaStatus     string          `json:"tsaStatus,omitempty"` //时间戳服务器状态
	HsmStatus     string          `json:"hsmStatus,omitempty"` //hsm服务器状态
	IpV6          string          `json:"ipV6,omitempty"`      //IPV6地址
	MaskV6        string          `json:"maskV6,omitempty"`    //IPV6掩码
	GatewayV6     string          `json:"gatewayV6,omitempty"` //IPV6网关
	CpuModel      int             `json:"cpuModel"`            //虚拟机cpu模式
	VsmType       int             `json:"vsmType"`             //虚拟机类型
	CreateSpec    int             `json:"createSpec"`          //虚拟机规格
	ImageVer      string          `json:"imageVer"`            //虚拟机镜像版本
	ImageDig      string          `json:"imageDig"`            //虚拟机镜像摘要
	CreateTime    string          `json:"createTime"`          //虚拟机创建时间
	VsmName       string          `json:"vsmName"`             //虚拟机名字
	OptStatus     int             `json:"optStatus"`           //虚拟机操作状态
	RunStatus     int             `json:"runStatus"`           //虚拟机运行状态
	InterfaceInfo []InterfaceInfo `json:"interfaceInfo,omitempty"`
}

