/*
 * Project: base
 * Moudle: cps
 * File: cps_rights.c
 * Created Date: 2023-08-29 14:48:26
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include "SKFAPI.h"
#include "cjson.h"
#include "cps_common.h"
#include "cps_rights.h"
#include "database_api.h"
#include "key_file.h"
#include "public/key_operate.h"
#include "public/md5.h"
#include "public/sm2_create_key_pair.h"

/* ======================================================================================
 * macros
 */
// #define UKEY_USE_DEFAULT_APP 1  //开关控制是否使用第一个应用或者使用默认应用
#define UKEY_APP_NAME       "DEFAULT_APP"
#define UKEY_APP_NAME1      "ENTERSAFE-ESPK"  // 插件有概率默认应用名为这个,适配,后续插件更新后可删除
#define UKEY_USER_FILE_NAME "user"
#define UKEY_KEY_FILE_NAME  "key"

#define UKEY_PIN_RETRY_TIME 6
#define UKEY_USER_FILE_SIZE 128

#define HASH_DATA_LEN   32
#define RANDOM_LEN    32
#define DB_FILE_PATH    "/usr/local/conf/cps.db"
#define FW_AC_FILE_PATH "/usr/local/conf/.fw_ac"  // 后端控制权限是否打开
static sqlite3* db_privileage = NULL;
static sqlite3* db_admin      = NULL;

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */
static int g_tenant_id = 0;

/* ======================================================================================
 * helper
 */

/**
 * @brief 获取特定格式的时间字符串
 *
 * @param time_str out
 * @return int 0成功
 */
int _get_time_str(char* time_str) {
    if (time_str == NULL) {
        return -1;
    }

    time_t    now = time(NULL);
    struct tm tm_now;
    localtime_r(&now, &tm_now);

    strftime(time_str, 64, "%Y-%m-%d %H:%M:%S", &tm_now);
    return 0;
}

void _save_config(void) {
    system("backpkg config >/dev/null 2>&1 &");
}
/**
 * @brief 通过sn号子获取设备句柄
 *
 * @param sn 序列号
 * @param phDev 设备句柄
 * @return int 成功返回0
 */
int _get_device_by_sn(char* sn, DEVHANDLE* phDev) {
    DEVINFO pDevInfo      = {0};
    char    NameList[512] = {0};
    int     len           = 512;

    // 1.枚举设备名称
    int ret = SKF_EnumDev(1, NameList, &len);

    // 2.通过设备名称建立连接,获取SN信息与参数对比
    char* name = strtok(NameList, "\0");
    while (name) {
        ret = SKF_ConnectDev(name, phDev);
        if (ret == SAR_OK) {
            ret = SKF_GetDevInfo(*phDev, &pDevInfo);
            if (ret == SAR_OK) {
                if (!strcmp(sn, pDevInfo.SerialNumber)) {
                    return 0;
                } else {
                    SKF_DisConnectDev(*phDev);
                }
            }
        }
        name = name + strlen(name) + 1;
        name = strtok(name, "\0");
    }

    return -1;
}

/**
 * @brief 设备用户认证
 *
 * @param phDev
 * @return int
 */
int _ukey_device_authentication(DEVHANDLE phDev) {
    int              ret                 = 0;
    DEVINFO          pDevInfo            = {0};
    char             pbRandom[32]        = {0};
    char             pbEncryptedData[32] = {0};
    int              pulEncryptedLen     = 32;
    HANDLE           phKey               = {0};
    BLOCKCIPHERPARAM EncryptParam        = {0};

    // 厂家默认的设备认证密钥
    char pbKey[16] = {0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38, 0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38};

    // 设备认证
    ret = SKF_GetDevInfo(phDev, &pDevInfo);
    API_CHECK_FUNC(ret, "SKF_GetDevInfo");

    ret = SKF_SetSymmKey(phDev, pbKey, pDevInfo.DevAuthAlgId, &phKey);
    API_CHECK_FUNC(ret, "SKF_SetSymmKey");

    memset((char*)&EncryptParam, 0x00, sizeof(BLOCKCIPHERPARAM));

    ret = SKF_GenRandom(phDev, pbRandom, 8);
    API_CHECK_FUNC(ret, "SKF_GenRandom");

    ret = SKF_EncryptInit(phKey, EncryptParam);
    API_CHECK_FUNC(ret, "SKF_EncryptInit");

    ret = SKF_Encrypt(phKey, pbRandom, 16, pbEncryptedData, &pulEncryptedLen);
    API_CHECK_FUNC(ret, "SKF_Encrypt");

    ret = SKF_DevAuth(phDev, pbEncryptedData, pulEncryptedLen);
    API_CHECK_FUNC(ret, "SKF_DevAuth");

out:
    if (phDev) SKF_DisConnectDev(phDev);

    return ret;
}
/* ======================================================================================
 * private implementation
 */

/**
 * @brief 填充login_info,加密并编码
 *
 * @param name 用户名 in
 * @param type 用户类型 in
 * @param last_login 登录时间 in
 * @param login_ip 登录ip in
 * @param login_status 登录状态 in
 * @param log_info 登录信息 out
 * @return int 0成功
 */
int fill_login_info(
    char*          name,
    int            type,
    char*          last_login,
    char*          login_ip,
    char*          session_id,
    int            login_status,
    unsigned char* log_info) {
    int           ret                                = 0;
    unsigned char tmp[DEFAULT_LOG_INFO_LEN / 2]      = {0};
    unsigned char tm2[DEFAULT_LOG_INFO_LEN / 2]      = {0};
    unsigned char tm3[DEFAULT_LOG_INFO_LEN / 2 + 32] = {0};
    unsigned char md5[32]                            = {0};
    unsigned int  out_len                            = DEFAULT_LOG_INFO_LEN / 2;
    sprintf(tmp, "%d+%s+%s+%s+%s+%d", type, name, last_login, login_ip, session_id, login_status);

    ret = sm4_encrypt_data(tmp, DEFAULT_LOG_INFO_LEN / 2, tm2, &out_len);
    API_CHECK_FUNC(ret, "sm4_encrypt_data");

    // 获取md5值
    cps_md5(tmp, strlen(tmp), md5);

    memcpy(tm3, md5, 32);
    memcpy(tm3 + 32, tm2, DEFAULT_LOG_INFO_LEN / 2);
    base64_encode(tm3, log_info, DEFAULT_LOG_INFO_LEN / 2 + 32);
out:
    return ret;
}

/**
 * @brief 从login_info信息中获取详细内容
 *
 * @param name 用户名 out
 * @param type 用户类型 out
 * @param last_login 最后登录时间 out
 * @param login_ip 登录ip out
 * @param login_status 登录状态 out
 * @param log_info 加密了的登录信息 in
 * @return int 成功返回0
 */
int decom_log_info(
    char*          name,
    int*           type,
    char*          last_login,
    char*          login_ip,
    char*          session_id,
    int*           login_status,
    unsigned char* log_info) {
    int           ret                                = 0;
    unsigned char tmp[DEFAULT_LOG_INFO_LEN / 2]      = {0};
    unsigned char tm2[DEFAULT_LOG_INFO_LEN / 2]      = {0};
    unsigned char tm3[DEFAULT_LOG_INFO_LEN / 2 + 32] = {0};
    unsigned char md5_data[32]                       = {0};
    unsigned int  out_len                            = DEFAULT_LOG_INFO_LEN / 2;

    base64_decode(log_info, tm3, strlen(log_info));
    memcpy(tmp, tm3 + 32, DEFAULT_LOG_INFO_LEN / 2);

    ret = sm4_decrypt_data(tmp, DEFAULT_LOG_INFO_LEN / 2, tm2, &out_len);
    API_CHECK_FUNC(ret, "sm4_decrypt_data");

    // 计算数据hash值对比
    cps_md5(tm2, strlen(tm2), md5_data);
    if (memcmp(md5_data, tm3, 32) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "user info has been externally modified!");
        return -1;
    }

    char* p = strtok(tm2, "+");
    if (p) {
        *type = atoi(p);
    }

    p = strtok(NULL, "+");
    if (p) {
        memcpy(name, p, strlen(p));
    }

    p = strtok(NULL, "+");
    if (p) {
        memcpy(last_login, p, strlen(p));
    }

    p = strtok(NULL, "+");
    if (p) {
        memcpy(login_ip, p, strlen(p));
    }

    p = strtok(NULL, "+");
    if (p) {
        memcpy(session_id, p, strlen(p));
    }

    p = strtok(NULL, "+");
    if (p) {
        *login_status = atoi(p);
    }
out:
    return ret;
}

/**
 * @brief 登出时修改登录信息,登录状态改为0,登录ip改为空
 *
 * @param log_info
 * @return int 0成功
 */
int mod_loginfo(unsigned char* log_info) {
    char name[USER_NAME_MAX_LEN]                = {0};
    int  type                                   = 0;
    char last_login[DEFAULT_TIME_STR_LEN]       = {0};
    char login_ip[DEFAULT_IP_STR_LEN]           = {0};
    char session_id[DEFAULT_SESSION_ID_STR_LEN] = {0};
    int  login_status                           = 0;
    int  ret                                    = 0;
    ret = decom_log_info(name, &type, last_login, login_ip, session_id, &login_status, log_info);
    API_CHECK_FUNC(ret, "decom_log_info");

    login_status = 0;

    ret = fill_login_info(name, type, last_login, "null", "null", login_status, log_info);
    API_CHECK_FUNC(ret, "fill_login_info");

out:
    return ret;
}

/**
 * @brief 生成sm2密钥对
 *
 * @param pubk 公钥 out
 * @param privk 私钥 out
 * @return int 成功返回0
 */
int create_sm2_key_pair(void* pubk, void* privk) {
    int                ret      = 0;
    ECCPUBLICKEYBLOB*  puk      = (ECCPUBLICKEYBLOB*)pubk;
    ECCPRIVATEKEYBLOB* prk      = (ECCPRIVATEKEYBLOB*)privk;
    SM2_KEY_PAIR       key_pair = {0};

    puk->BitLen = 256;
    prk->BitLen = 256;

    ret = sm2_create_key_pair(&key_pair);
    API_CHECK_FUNC(ret, "SDF_GenerateKeyPair_ECC");

    memcpy(puk->XCoordinate + 32, key_pair.pub_key + 1, 32);
    memcpy(puk->YCoordinate + 32, key_pair.pub_key + 33, 32);
    memcpy(prk->PrivateKey + 32, key_pair.pri_key, 32);

out:
    return ret;
}

/**
 * @brief 导入ecc密钥队
 *
 * @param phContainer 容器句柄
 * @param pub 公钥
 * @param pri 私钥
 * @return int 成功返回0
 */
int _UKeyMgr_ImportEccKeyPair(HCONTAINER phContainer, ECCPUBLICKEYBLOB pub, ECCPRIVATEKEYBLOB pri) {
    int              ret = 0;
    ULONG            rLen;
    ECCPUBLICKEYBLOB pEccSignKey;
    ULONG            ulEccPubKeyLen = sizeof(ECCPUBLICKEYBLOB);
    ECCCIPHERBLOB*   pEccCipherBlob = NULL;
    HANDLE           hSessionKey;

    PENVELOPEDKEYBLOB pEnvelopedKeyBlob;
    unsigned char     pbWrappedKey[32] = {0}, pbTmpData[1024] = {0}, pbEncryptedData[1024] = {0}, pbData[1024] = {0};
    ULONG             ulWrappedKeyLen = 32, ulTmpDataLen = 1024, ulEncryptedDataLen = 1024;
    BLOCKCIPHERPARAM  EncryptParam;
    int               offset = 0;

    ret = SKF_ExportPublicKey(phContainer, TRUE, (unsigned char*)&pEccSignKey, &ulEccPubKeyLen);
    API_CHECK_FUNC(ret, "SKF_ExportPublicKey");

    pEccCipherBlob            = (ECCCIPHERBLOB*)malloc(sizeof(ECCCIPHERBLOB) + 16 - 1);
    pEccCipherBlob->CipherLen = 16;
    ret = SKF_ECCExportSessionKey(phContainer, SGD_SM1_ECB, &pEccSignKey, pEccCipherBlob, &hSessionKey);
    API_CHECK_FUNC(ret, "SKF_ECCExportSessionKey");

    memcpy(pbTmpData, (char*)&pri.PrivateKey, pri.BitLen / 4);
    ulTmpDataLen = pri.BitLen / 4;

    EncryptParam.IVLen       = 0;
    EncryptParam.PaddingType = 0;
    ret                      = SKF_EncryptInit(hSessionKey, EncryptParam);
    API_CHECK_FUNC(ret, "SKF_EncryptInit");

    ret = SKF_EncryptUpdate(hSessionKey, pbTmpData, ulTmpDataLen, pbEncryptedData, &ulEncryptedDataLen);
    API_CHECK_FUNC(ret, "SKF_EncryptUpdate");

    ret = SKF_EncryptFinal(hSessionKey, pbEncryptedData + ulEncryptedDataLen, &rLen);
    API_CHECK_FUNC(ret, "SKF_EncryptFinal");

    ulEncryptedDataLen += rLen;

    pEnvelopedKeyBlob = (PENVELOPEDKEYBLOB)malloc(sizeof(ENVELOPEDKEYBLOB) + 16 - 1);
    if (pEccCipherBlob == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "malloc failed!");
        goto out;
    }

    pEnvelopedKeyBlob->Version     = 1;
    pEnvelopedKeyBlob->ulSymmAlgID = SGD_SM1_ECB;
    pEnvelopedKeyBlob->ulBits      = 256;
    pEnvelopedKeyBlob->PubKey      = pub;

    memset(pbEncryptedData, 0x00, 32);
    memcpy((char*)&(pEnvelopedKeyBlob->ECCCipherBlob), pEccCipherBlob, sizeof(ECCCIPHERBLOB) + 16 - 1);
    memcpy((char*)&(pEnvelopedKeyBlob->cbEncryptedPriKey), &pbEncryptedData, ulEncryptedDataLen);

    ret = SKF_ImportECCKeyPair(phContainer, pEnvelopedKeyBlob);
    API_CHECK_FUNC(ret, "SKF_ImportECCKeyPair");

    if (pEnvelopedKeyBlob) {
        free(pEnvelopedKeyBlob);
    }
    if (pEccCipherBlob) {
        free(pEccCipherBlob);
    }

out:
    return 0;
}

int _get_name_type(DEVHANDLE phDev, char* name, int* type) {
    int ret = 0;

    HAPPLICATION  phApplication = {0};
    FILEATTRIBUTE pFileInfo     = {0};

    memset(name, 0, 64);
    *type = 0;

#ifdef UKEY_USE_DEFAULT_APP
    ret = SKF_OpenApplication(phDev, UKEY_APP_NAME, &phApplication);
    if (ret == SAR_APPLICATION_NOT_EXISTS) {
        ret = SKF_OpenApplication(phDev, UKEY_APP_NAME1, &phApplication);
    }
#else
    char AppName[1024] = {0};
    int  len           = 1024;

    SKF_EnumApplication(phDev, AppName, &len);

    ret = SKF_OpenApplication(phDev, AppName, &phApplication);
#endif
    API_CHECK_FUNC(ret, "SKF_OpenApplication");

    ret = SKF_GetFileInfo(phApplication, "user", &pFileInfo);
    if (ret == SAR_OK) {
        // 存在用户信息
        char UserInfo[128] = {0};
        int  UserInfoLen   = 0;
        ret                = SKF_ReadFile(phApplication, "user", 0, 128, UserInfo, &UserInfoLen);
        if (!ret) {
            sscanf(UserInfo, "name:%[^;];type:%d", name, type);
        }
    }

out:
    if (phApplication) SKF_CloseApplication(phApplication);
    return ret;
}

/**
 * @brief 获取当前设备的sn列表
 *
 * @param InfoList 列表
 * @param ListSize 列表大小
 * @return int 成功返回0
 */
int _UKeyMgr_GetUkeyListInfo(char* InfoList, unsigned int ListSize) {
    DEVINFO   pDevInfo       = {0};
    DEVHANDLE phDev          = {0};
    char      NameList[512]  = {0};
    int       len            = 512;
    int       offset         = 0;
    char      admin_name[64] = {0};
    int       type           = 0;
    char      ukey_info[128] = {0};

    // 1.枚举设备名称
    int ret = SKF_EnumDev(FALSE, NameList, &len);
    if (ret) return ret;

    // 2.通过设备名称建立连接,获取SN信息与参数对比
    char* name = strtok(NameList, "\0");
    while (name) {
        if (SKF_ConnectDev(name, &phDev) == SAR_OK) {
            if (SKF_GetDevInfo(phDev, &pDevInfo) == SAR_OK) {
                _get_name_type(phDev, admin_name, &type);
                sprintf(ukey_info, "%s,%s,%d", pDevInfo.SerialNumber, admin_name, type);

                int info_len = strlen(ukey_info);
                if ((offset + info_len) > ListSize) {
                    return -1;
                }

                memcpy(InfoList + offset, ukey_info, info_len);
                offset += info_len;
                memcpy(InfoList + offset, ";", 1);
                offset += 1;
            }
        } else {
            SKF_DisConnectDev(phDev);
            return -1;
        }
        name = name + strlen(name) + 1;
        name = strtok(name, "\0");
    }

    SKF_DisConnectDev(phDev);
    return 0;
}

int _get_info_from_signData(char* sign_data, char* random, char* randomB, char* device_sn, char* sign_value) {
    if (sign_data == NULL || random == NULL || randomB == NULL || device_sn == NULL || sign_value == NULL) {
        return -1;
    }
    // char randomAB[RANDOM_LEN];
    memcpy(random, sign_data, RANDOM_LEN);
    memcpy(randomB, sign_data + RANDOM_LEN + 1, RANDOM_LEN);
    memcpy(device_sn, sign_data + 2 * RANDOM_LEN + 2, 16);
    memcpy(sign_value, sign_data + 20 + 3 * RANDOM_LEN, RANDOM_LEN * 8);

    return 0;
}

/**
 * @brief 注册管理员
 *
 * @param SN ukey序列号
 * @param Pin PIN码
 * @param AdmName 管理员名称
 * @param AdmType 管理员类型
 * @param Cover 0不覆盖,1覆盖
 * @param manager_id 用户id,1-3是管理员,4是操作员,5是审计员
 * @param pBlob 返回的签名密钥公钥信息
 * @return int 非0失败
 */
int AccountMgr_Register(char* SN, char* Pin, char* AdmName, int AdmType, int Cover, int manager_id, void* pBlob) {
    int               ret            = 0;
    DEVHANDLE         phDev          = {0};
    HAPPLICATION      phApplication  = {0};
    HCONTAINER        phContainer    = {0};
    FILEATTRIBUTE     pFileInfo      = {0};
    char*             AppList        = NULL;
    int               AppListLen     = 0;
    char*             ConList        = NULL;
    int               ConListLen     = 0;
    int               have_con       = 0;
    int               RetryCount     = 0;
    ECCPUBLICKEYBLOB  pub            = {0};
    ECCPRIVATEKEYBLOB pri            = {0};
    ULONG             ulEccPubKeyLen = sizeof(ECCPUBLICKEYBLOB);

    if (_get_device_by_sn(SN, &phDev) == -1) {
        printf(gettext("sn error,not found device!"));
        return -1;
    }

    // 设备认证
    ret = _ukey_device_authentication(phDev);
    API_CHECK_FUNC(ret, "_ukey_device_authentication");

    // 2.获取应用句柄
    ret = SKF_EnumApplication(phDev, AppList, &AppListLen);
    API_CHECK_FUNC(ret, "SKF_EnumApplication");

    if (2 <= AppListLen) {
        AppList = (char*)malloc(AppListLen);
#ifdef UKEY_USE_DEFAULT_APP
        ret = SKF_EnumApplication(phDev, AppList, &AppListLen);
        API_CHECK_FUNC(ret, "SKF_EnumApplication");
        char* app_name  = strtok(AppList, "\0");
        int   have_file = 0;

        while (app_name) {
            if (strstr(app_name, UKEY_APP_NAME) != NULL) {
                have_file = 1;
                break;
            }

            app_name = app_name + strlen(app_name) + 1;
            app_name = strtok(app_name, "\0");
        }

        if (!have_file) {
            SKF_DeleteApplication(phDev, AppList);

            ret = SKF_CreateApplication(
                phDev,
                UKEY_APP_NAME,
                "rockey",
                UKEY_PIN_RETRY_TIME,
                Pin,
                UKEY_PIN_RETRY_TIME,
                SECURE_USER_ACCOUNT,
                &phApplication);

            API_CHECK_FUNC(ret, "SKF_CreateApplication");
        } else {
            ret = SKF_OpenApplication(phDev, UKEY_APP_NAME, &phApplication);
            if (ret == SAR_APPLICATION_NOT_EXISTS) {
                ret = SKF_OpenApplication(phDev, UKEY_APP_NAME1, &phApplication);
            }
            API_CHECK_FUNC(ret, "SKF_OpenApplication");
        }
#else
        ret = SKF_OpenApplication(phDev, AppList, &phApplication);
        API_CHECK_FUNC(ret, "SKF_OpenApplication");
#endif
    } else {
        ret = SKF_CreateApplication(
            phDev,
            UKEY_APP_NAME,
            "rockey",
            UKEY_PIN_RETRY_TIME,
            Pin,
            UKEY_PIN_RETRY_TIME,
            SECURE_USER_ACCOUNT,
            &phApplication);
        API_CHECK_FUNC(ret, "SKF_CreateApplication");
    }

    // 3.校验pin码
    ret = SKF_VerifyPIN(phApplication, USER_TYPE, Pin, &RetryCount);
    if (ret != 0) {
        switch (ret) {
            case SAR_PIN_INCORRECT:
                printf(gettext("pin code error,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LOCKED:
                printf(gettext("pin code locked!"));
                break;
            case SAR_PIN_INVALID:
                printf(gettext("pin code invalid,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LEN_RANGE:
                printf(gettext("pin len error!"));
                break;
            default:
                printf(gettext("pin error,remaining retry count:%d!"), RetryCount);
                break;
        }
        ret = -1;
        goto out;
    }

    // 删除老文件
    SKF_DeleteFile(phApplication, UKEY_USER_FILE_NAME);

    // 4.新建用户文件与密钥容器

    ret = SKF_CreateFile(
        phApplication, UKEY_USER_FILE_NAME, UKEY_USER_FILE_SIZE, SECURE_EVERYONE_ACCOUNT, SECURE_USER_ACCOUNT);
    API_CHECK_FUNC(ret, "SKF_CreateFile");

    char str[UKEY_USER_FILE_SIZE] = {0};
    sprintf(str, "name:%s;type:%d", AdmName, AdmType);

    ret = SKF_WriteFile(phApplication, UKEY_USER_FILE_NAME, 0, str, strlen(str));
    API_CHECK_FUNC(ret, "SKF_WriteFile");

    // 5.生成签名密钥对,加密密钥对需要外部导入
    // 查询是否存在指定容器
    ret = SKF_EnumContainer(phApplication, ConList, &ConListLen);
    API_CHECK_FUNC(ret, "SKF_EnumContainer");

    if (2 <= ConListLen) {
        ConList = (char*)malloc(ConListLen);
        ret     = SKF_EnumContainer(phApplication, ConList, &ConListLen);
        API_CHECK_FUNC(ret, "SKF_EnumContainer");
        char* con_name = strtok(ConList, "\0");

        while (con_name) {
            if (strstr(con_name, UKEY_KEY_FILE_NAME) != NULL) {
                have_con = 1;
                break;
            }

            con_name = con_name + strlen(con_name) + 1;
            con_name = strtok(con_name, "\0");
        }
    }

    ret = SKF_CreateContainer(phApplication, UKEY_KEY_FILE_NAME, &phContainer);
    API_CHECK_FUNC(ret, "SKF_CreateContainer");

    if (Cover == 0 && have_con == 1) {
        // 导出密钥信息
        ret = SKF_ExportPublicKey(phContainer, TRUE, (unsigned char*)pBlob, &ulEccPubKeyLen);
        API_CHECK_FUNC(ret, "SKF_ExportPublicKey");

        ret = SKF_ExportPublicKey(phContainer, FALSE, (unsigned char*)&pub, &ulEccPubKeyLen);
        API_CHECK_FUNC(ret, "SKF_ExportPublicKey");
    } else {
        // 6.1生成签名密钥对
        ret = SKF_GenECCKeyPair(phContainer, SGD_SM2_1, pBlob);
        API_CHECK_FUNC(ret, "SKF_GenECCKeyPair");

        // 6.2外部生成加密密钥对,导入ukey
        ret = create_sm2_key_pair(&pub, &pri);
        API_CHECK_FUNC(ret, "create_sm2_key_pair");

        ret = _UKeyMgr_ImportEccKeyPair(phContainer, pub, pri);
        API_CHECK_FUNC(ret, "pcie_import_ecc_key_pair");
    }

    // 保存密钥到文件
    ret = kf_add_user_pub_key(manager_id, pBlob, &pub);
    if (ret) {
        printf(gettext("failed to write to file!"));
        ret = -1;
        goto out;
    }

    SKF_CloseContainer(phContainer);

out:
    if (AppList) {
        free(AppList);
    }

    if (phApplication) SKF_CloseApplication(phApplication);
    if (phDev) SKF_DisConnectDev(phDev);
    return ret;
}

/**
 * @brief 远端注册用户
 *
 * @param PubKey 公钥信息
 * @param manager_id 管理员id
 * @return int 成功返回0
 */
int AccountMgr_RemoteRegister(char* PubKey, int manager_id) {
    ECCPUBLICKEYBLOB si_pub_key = {0};
    ECCPUBLICKEYBLOB en_pri_key = {0};
    int              key_len    = strlen(PubKey);

    char* base64_str = (char*)malloc(key_len);
    if (base64_str == NULL) {
        return -1;
    }

    // base64解密
    base64_decode(PubKey, base64_str, key_len);

    // 获取公钥,不确定密钥位长是否转大小端,统一改成256
    int ECC_LEN = ECC_MAX_XCOORDINATE_BITS_LEN / 8;
    // memcpy(&(si_pub_key.BitLen), base64_str, 4);
    si_pub_key.BitLen = 256;
    memcpy(si_pub_key.XCoordinate, base64_str + 4, ECC_LEN);
    memcpy(si_pub_key.YCoordinate, base64_str + ECC_LEN + 4, ECC_LEN);

    // memcpy(&(en_pri_key.BitLen), base64_str + 2 * ECC_LEN + 6, 4);
    si_pub_key.BitLen = 256;
    memcpy(en_pri_key.XCoordinate, base64_str + 2 * ECC_LEN + 11, ECC_LEN);
    memcpy(en_pri_key.YCoordinate, base64_str + 3 * ECC_LEN + 11, ECC_LEN);

    // 保存密钥到文件
    int ret = kf_add_user_pub_key(manager_id, &si_pub_key, &en_pri_key);
    if (ret) {
        printf(gettext("failed to write to file!"));
        return -1;
    }

    return ret;
}

/**
 * @brief 本地用户登录
 *
 * @param SN 序列号
 * @param Pin PIN码
 * @return int 成功返回0
 */
int AccountMgr_Login(char* SN, char* Pin, char* UserName, int manager_id) {
    int ret      = 0;
    int UserType = 0;
    // 1.通过SN号获取设备句柄
    DEVHANDLE     phDev         = {0};
    HAPPLICATION  phApplication = {0};
    HCONTAINER    phContainer   = {0};
    FILEATTRIBUTE pFileInfo     = {0};
    int           RetryCount    = 0;

    if (_get_device_by_sn(SN, &phDev) == -1) {
        printf(gettext("device not found!"));
        return -1;
    }

// 2.获取应用句柄
#ifdef UKEY_USE_DEFAULT_APP
    ret = SKF_OpenApplication(phDev, UKEY_APP_NAME, &phApplication);
    if (ret == SAR_APPLICATION_NOT_EXISTS) {
        ret = SKF_OpenApplication(phDev, UKEY_APP_NAME1, &phApplication);
    }
#else
    char AppName[1024] = {0};
    int  len           = 1024;

    SKF_EnumApplication(phDev, AppName, &len);

    ret                = SKF_OpenApplication(phDev, AppName, &phApplication);
#endif
    API_CHECK_FUNC(ret, "SKF_OpenApplication");

    // 3.校验pin码,输出pin码错误,剩余重试次数
    ret = SKF_VerifyPIN(phApplication, USER_TYPE, Pin, &RetryCount);
    if (ret != 0) {
        switch (ret) {
            case SAR_PIN_INCORRECT:
                printf(gettext("pin code error,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LOCKED:
                printf(gettext("pin code locked!"));
                break;
            case SAR_PIN_INVALID:
                printf(gettext("pin code invalid,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LEN_RANGE:
                printf(gettext("pin len error!"));
                break;
            default:
                printf(gettext("pin error,remaining retry count:%d!"), RetryCount);
                break;
        }
        ret = -1;
        goto out;
    }

    // 4.查看是否存在用户信息
    ret = SKF_GetFileInfo(phApplication, UKEY_USER_FILE_NAME, &pFileInfo);
    if (ret == SAR_OK) {
        // 存在用户信息
        char UserInfo[UKEY_USER_FILE_SIZE] = {0};
        int  UserInfoLen                   = 0;
        SKF_ReadFile(phApplication, UKEY_USER_FILE_NAME, 0, UKEY_USER_FILE_SIZE, UserInfo, &UserInfoLen);
        if (UserInfoLen != 0 && strstr(UserInfo, UserName) == NULL) {
            // 管理员未在当前设备注册
            printf(gettext("user not registered!"));
            ret = -1;
            goto out;
        }

    } else {
        // 不存在用户信息
        printf(gettext("user not registered!"));
        ret = -1;
        goto out;
    }

    // 5.计算用户类型,用户名称,SN信息的摘要
    char SignStr[128] = {0};
    sprintf(SignStr, "user:%s;type:%d;sn:%s", UserName, UserType, SN);
    HANDLE phHash       = {0};
    int    pulHashLen   = 32;
    char   HashData[32] = {0};

    ret = SKF_DigestInit(phDev, SGD_SM3, NULL, NULL, 0, &phHash);
    API_CHECK_FUNC(ret, "SKF_DigestInit");
    ret = SKF_Digest(phHash, SignStr, strlen(SignStr), HashData, &pulHashLen);
    API_CHECK_FUNC(ret, "SKF_Digest");

    // 6.签名
    ECCSIGNATUREBLOB SignData = {0};
    ret                       = SKF_OpenContainer(phApplication, UKEY_KEY_FILE_NAME, &phContainer);
    API_CHECK_FUNC(ret, "SKF_OpenContainer");
    ret = SKF_ECCSignData(phContainer, HashData, pulHashLen, &SignData);
    API_CHECK_FUNC(ret, "SKF_ECCSignData");
    ret = SKF_CloseContainer(phContainer);
    API_CHECK_FUNC(ret, "SKF_CloseContainer");

    // 7.验签
    // 从文件中获取对应的公钥信息
    ECCPUBLICKEYBLOB pECCPubKeyBlob = {0};
    ret                             = kf_get_user_pub_key(manager_id, &pECCPubKeyBlob, 0);
    if (ret) {
        // 不存在用户信息
        printf(gettext("can not get user key!"));
        ret = -1;
        goto out;
    }

    ret = SKF_ExtECCVerify(phDev, &pECCPubKeyBlob, HashData, pulHashLen, &SignData);
    API_CHECK_FUNC(ret, "SKF_ExtECCVerify");

out:
    if (phApplication) SKF_CloseApplication(phApplication);
    if (phDev) SKF_DisConnectDev(phDev);
    return ret;
}

/**
 * @brief 远端用户登录身份信息验证
 *
 * @param Hash 身份信息的哈西值
 * @param SignData 签名值
 * @param manager_id 管理员id
 * @return int 成功返回0
 */
int AccountMgr_RemoteLogin(char* Hash, char* SignData, int manager_id) {
    int ret = 0;

    // 1.验签
    ECCPUBLICKEYBLOB pECCPubKeyBlob = {0};
    ret                             = kf_get_user_pub_key(manager_id, &pECCPubKeyBlob, 0);
    if (ret) {
        // 不存在用户信息
        printf(gettext("can not get user key!"));
        ret = -1;
        goto out;
    }

    ret = verify_ecc(&pECCPubKeyBlob, Hash, SignData);
    API_CHECK_FUNC(ret, "verify_ecc");

out:
    return ret;
}

/**
 * @brief 删除uey中的用户信息
 *
 * @param AdmName 用户名字
 * @param AdmType 用户类型
 * @param manager_id 管理员id
 * @param sn 序列号
 * @return int 成功返回0
 */
int AccountMgr_Delete(char* AdmName, int AdmType, int manager_id, char* sn) {
    int           RetryCount    = 0;
    int           ret           = 0;
    DEVHANDLE     phDev         = {0};
    HAPPLICATION  phApplication = {0};
    HCONTAINER    phContainer   = {0};
    FILEATTRIBUTE pFileInfo     = {0};

    char*            AppList             = NULL;
    int              AppListLen          = 0;
    DEVINFO          pDevInfo            = {0};
    char             pbRandom[32]        = {0};
    char             pbEncryptedData[32] = {0};
    int              pulEncryptedLen     = 32;
    HANDLE           phKey               = {0};
    BLOCKCIPHERPARAM EncryptParam        = {0};

    // if (_get_device_by_sn(sn, &phDev) == -1) {
    //     printf("获取设备句柄失败\n");
    //     return -1;
    // }

    // // 设备认证
    // ret = _ukey_device_authentication(phDev);
    // API_CHECK_FUNC(ret, "_ukey_device_authentication");

    // // 获取应用句柄
    // ret = SKF_OpenApplication(phDev, UKEY_APP_NAME, &phApplication);
    // CHECK_FUNC(ret, "SKF_OpenApplication");

    // // 只删除用户文件
    // SKF_DeleteFile(phApplication, UKEY_USER_FILE_NAME);
    // CHECK_FUNC(ret, "SKF_DeleteFile");

    // 删除文件中的公钥信息:todo
    // ret = kf_delete_key_ukey(manager_id);
    if (ret) {
        printf("kf_delete_key_ukey faild %0x\n", ret);
    }

out:
    // if (phApplication) SKF_CloseApplication(phApplication);
    // if (phDev) SKF_DisConnectDev(phDev);
    return ret;
}
/* ======================================================================================
 * implementation
 */
int cps_rights_list_ukey(int argc, char* argv[]) {
    char name[USER_NAME_MAX_LEN] = {0};
    char sn[DEFAULT_SN_STR_LEN]  = {0};
    int  type                    = 0;
    char ukey_list[2048]         = {0};
    int  ret                     = -1;

    char* json_str = NULL;
    // 1.获取设备序列号
    ret = _UKeyMgr_GetUkeyListInfo(ukey_list, 2048);
    if (ret) {
        printf(gettext("device list not obtained!"));
        goto out;
    }

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON* array = cJSON_AddArrayToObject(root, "UKeyList");
    if (NULL == array) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json array failed!");
        goto out;
    }

    cJSON* tmp       = NULL;
    char*  ukey_info = strtok(ukey_list, ";");
    while (ukey_info) {
        memset(name, 0, USER_NAME_MAX_LEN);
        memset(sn, 0, DEFAULT_SN_STR_LEN);
        type = 0;

        sscanf(ukey_info, "%[^,],%[^,],%d", sn, name, &type);

        tmp = cJSON_CreateObject();
        if (NULL == tmp) {
            DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
            goto out;
        }

        cJSON_AddStringToObject(tmp, "sn", sn);
        cJSON_AddStringToObject(tmp, "user", name);
        cJSON_AddNumberToObject(tmp, "user_type", type);

        cJSON_AddItemToArray(array, tmp);

        ukey_info = strtok(NULL, ";");
    }

    json_str = cJSON_PrintUnformatted(root);
    printf("%s\n", json_str);

    ret = 0;
out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }

    return ret;
}

int cps_rights_check_user(int argc, char* argv[]) {
    int   ret       = 0;
    int   type      = atoi(argv[4]);
    char* sn        = argv[6];
    char* name      = argv[8];
    int   tenant_id = 0;
    if (argc == 11) {
        tenant_id = atoi(argv[10]);
    }

    // 1.查找数据库,查看是否已经注册过该用户
    ret = db_check_user_info(sn, name, type, tenant_id);
    if (ret == 1) {
        return 1;
    } else {
        return 0;
    }
}

int cps_rights_add_user(int argc, char* argv[]) {
    ECCPUBLICKEYBLOB pBlob                      = {0};
    USER_INFO_T      user_info                  = {0};
    char             time[DEFAULT_TIME_STR_LEN] = {0};
    int              ret                        = 0;
    user_info.tenant_id                         = g_tenant_id;
    char role[32]                               = {0};

    user_info.type       = atoi(argv[4]);
    user_info.sn         = argv[6];
    user_info.name       = argv[8];
    char* password       = argv[10];
    user_info.login_type = atoi(argv[12]);
    char* pin            = argv[14];
    int   cover          = atoi(argv[16]);
    user_info.login_ip   = argv[18];
    user_info.session_id = argv[20];
    if (argc == 23) {
        if (!strcmp("remark", argv[21])) {
            user_info.remark   = argv[22];
            user_info.pri_temp = NULL;
        } else {
            user_info.remark   = "";
            user_info.pri_temp = argv[22];
        }
    } else if (argc == 25) {
        user_info.remark   = argv[22];
        user_info.pri_temp = argv[24];
    } else {
        user_info.remark   = "";
        user_info.pri_temp = "";
    }
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};

    // 如果是租户管理员类型,需要获取租户id
    if (user_info.type == 3 && g_tenant_id == 0) {
        user_info.tenant_id = db_get_tenant_id_by_session_ip(user_info.session_id, user_info.login_ip);
        if (user_info.tenant_id == -1) {
            printf(gettext("no corresponding tenant!"));
            return -1;
        }
    }

    // 检测用户是否注册
    ret = db_check_user_info(user_info.sn, user_info.name, user_info.type, user_info.tenant_id);
    if (ret) {
        return -1;
    }

    user_info.id = db_get_manager_id(user_info.type);
    if (user_info.id == -1) {
        printf(gettext("there are enough administrators!"));
        return -1;
    }

    // 调用ukey接口
    ret = AccountMgr_Register(user_info.sn, pin, user_info.name, user_info.type, cover, user_info.id, &pBlob);
    if (ret) {
        // 当返回值为-1时在接口内有输出日志
        if (ret != -1) printf(gettext("add user failed!"));
        return ret;
    }

    user_info.login_status = 1;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.create_time = time;
        user_info.last_login  = time;
    }

    // 设置login_info
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("add user failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    if (user_info.type != 3) {
        // 账户设置
        switch (user_info.type) {
            case 0:
                sprintf(role, "%s", "管理员");
                break;
            case 1:
                sprintf(role, "%s", "操作员");
                break;
            case 2:
                sprintf(role, "%s", "审计员");
                break;
            default:
                sprintf(role, "%s", "user");
                break;
        }

        user_info.pri_temp = role;

        // 保存信息到数据库
        ret = db_add_user_info(&user_info);
        if (ret) {
            printf(gettext("add user failed!"));
        }

        // 先删除之前的账户
        char system_str[1024] = {0};
        sprintf(system_str, "/usr/local/bin/admacct del name '%s' > /dev/null 2>&1", user_info.name);
        system(system_str);

        memset(system_str, '0', 1024);

        // 添加账户
        sprintf(
            system_str,
            "/usr/local/bin/admacct add name '%s' password '%s' vmfwid 0 max_err_num 5 valid_time 0 sleep_time 0 role "
            "'%s' employed 1 local_auth on sim_auth off  > /dev/null 2>&1",
            user_info.name,
            password,
            user_info.pri_temp);
        system(system_str);
    } else {
        // 保存信息到数据库
        ret = db_add_user_info(&user_info);
        if (ret) {
            printf(gettext("add user failed!"));
        }
    }

    _save_config();
    return ret;
}

int cps_rights_remote_add_user(int argc, char* argv[]) {
    USER_INFO_T user_info                  = {0};
    char        time[DEFAULT_TIME_STR_LEN] = {0};
    int         ret                        = 0;
    user_info.tenant_id                    = g_tenant_id;
    char role[32]                          = {0};

    user_info.type       = atoi(argv[4]);
    user_info.sn         = argv[6];
    user_info.name       = argv[8];
    char* password       = argv[10];
    user_info.login_type = atoi(argv[12]);
    char* key            = argv[14];
    user_info.login_ip   = argv[16];
    user_info.session_id = argv[18];
    if (argc == 21) {
        if (!strcmp("remark", argv[19])) {
            user_info.remark   = argv[20];
            user_info.pri_temp = NULL;
        } else {
            user_info.remark   = "";
            user_info.pri_temp = argv[20];
        }
    } else if (argc == 23) {
        user_info.remark   = argv[20];
        user_info.pri_temp = argv[22];
    } else {
        user_info.remark   = "";
        user_info.pri_temp = "";
    }
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};

    // 如果是租户管理员类型,需要获取租户id
    if (user_info.type == 3 && g_tenant_id == 0) {
        user_info.tenant_id = db_get_tenant_id_by_session_ip(user_info.session_id, user_info.login_ip);
        if (user_info.tenant_id == -1) {
            printf(gettext("no corresponding tenant!"));
            return -1;
        }
    }

    // 检测用户是否注册
    ret = db_check_user_info(user_info.sn, user_info.name, user_info.type, user_info.tenant_id);
    if (ret) {
        return -1;
    }

    user_info.id = db_get_manager_id(user_info.type);
    if (user_info.id == -1) {
        printf(gettext("there are enough administrators!"));
        return -1;
    }

    ret = AccountMgr_RemoteRegister(key, user_info.id);
    if (ret) {
        if (ret != -1) printf(gettext("add user failed!"));
        return ret;
    }

    user_info.login_status = 1;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.create_time = time;
        user_info.last_login  = time;
    }
    // 设置login_info
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("add user failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    if (user_info.type != 3) {
        // 账户设置
        switch (user_info.type) {
            case 0:
                sprintf(role, "%s", "管理员");
                break;
            case 1:
                sprintf(role, "%s", "操作员");
                break;
            case 2:
                sprintf(role, "%s", "审计员");
                break;
            default:
                sprintf(role, "%s", "user");
                break;
        }

        user_info.pri_temp = role;

        // 保存信息到数据库
        ret = db_add_user_info(&user_info);
        if (ret) {
            printf(gettext("add user failed!"));
        }

        // 先删除之前的账户
        char system_str[1024] = {0};
        sprintf(system_str, "/usr/local/bin/admacct del name '%s' > /dev/null 2>&1", user_info.name);
        system(system_str);

        memset(system_str, '0', 1024);

        // 添加账户
        sprintf(
            system_str,
            "/usr/local/bin/admacct add name '%s' password '%s' vmfwid 0 max_err_num 5 valid_time 0 sleep_time 0 role "
            "'%s' employed 1 local_auth on sim_auth off  > /dev/null 2>&1",
            user_info.name,
            password,
            user_info.pri_temp);
        system(system_str);
    } else {
        // 保存信息到数据库
        ret = db_add_user_info(&user_info);
        if (ret) {
            printf(gettext("add user failed!"));
        }
    }

    _save_config();
    return ret;
}

int cps_rights_add_user_bind_ukey(int argc, char* argv[]) {
    int   ret         = 0;
    char* tenant_name = argv[argc - 1];

    // 1.删除管理员表中,未绑定ukey的管理员数据
    ret = cps_del_data_from_db(CPS_ADMIN_INFO_TABLE, "tenant_id='1000' and manager_name='%s'", tenant_name);
    API_CHECK_FUNC(ret, "cps_del_data_from_db failed");

    // 获取管理员id
    char id[8] = {0};
    cps_get_data_from_db(CPS_TENANT_INFO_TABLE, "name", tenant_name, "tenant_id", id, 8, 0);
    g_tenant_id = atoi(id);

    char* argv1[25] = {};
    argv1[0]        = "cps";
    argv1[1]        = "rights";
    argv1[2]        = "add";
    argv1[3]        = "type";
    argv1[4]        = argv[4];
    argv1[5]        = "sn";
    argv1[6]        = argv[6];
    argv1[7]        = "name";
    argv1[8]        = argv[8];
    argv1[9]        = "password";
    argv1[10]       = "lion@LL99";
    argv1[11]       = "login_type";
    argv1[12]       = "0";
    argv1[13]       = "pin";
    argv1[14]       = argv[10];
    argv1[15]       = "cover";
    argv1[16]       = argv[12];
    argv1[17]       = "ip";
    argv1[18]       = argv[14];
    argv1[19]       = "sessionid";
    argv1[20]       = argv[16];
    argv1[21]       = "remark";
    if (argc == 21) {
        argv1[22] = argv[18];
    } else {
        argv1[22] = "";
    }
    argv1[23] = "pri_temp";
    argv1[24] = "";

    // 2.添加管理员
    ret = cps_rights_add_user(25, argv1);
    if (ret) {
        // 添加失败,恢复未绑定ukey的管理员数据
        ret = cps_insert_data_to_db(
            CPS_ADMIN_INFO_TABLE, "tenant_id='1000',manager_type='3',manager_name='%s'", tenant_name);
        API_CHECK_FUNC(ret, "cps_insert_data_to_db failed");
    }

out:
    return ret;
}

int cps_rights_remote_add_user_bind_ukey(int argc, char* argv[]) {
    int   ret         = 0;
    char* tenant_name = argv[argc - 1];

    // 1.删除管理员表中,未绑定ukey的管理员数据
    ret = cps_del_data_from_db(CPS_ADMIN_INFO_TABLE, "tenant_id='1000' and manager_name='%s'", tenant_name);
    API_CHECK_FUNC(ret, "cps_del_data_from_db failed");

    // 获取管理员id
    char id[8] = {0};
    cps_get_data_from_db(CPS_TENANT_INFO_TABLE, "name", tenant_name, "tenant_id", id, 8, 0);
    g_tenant_id = atoi(id);

    char* argv1[23] = {};
    argv1[0]        = "cps";
    argv1[1]        = "rights";
    argv1[2]        = "remote_add";
    argv1[3]        = "type";
    argv1[4]        = argv[4];
    argv1[5]        = "sn";
    argv1[6]        = argv[6];
    argv1[7]        = "name";
    argv1[8]        = argv[8];
    argv1[9]        = "password";
    argv1[10]       = "lion@LL99";
    argv1[11]       = "login_type";
    argv1[12]       = "0";
    argv1[13]       = "key";
    argv1[14]       = argv[10];
    argv1[15]       = "ip";
    argv1[16]       = argv[12];
    argv1[17]       = "sessionid";
    argv1[18]       = argv[14];
    argv1[19]       = "remark";
    if (argc == 18) {
        argv1[20] = argv[16];
    } else {
        argv1[20] = "";
    }
    argv1[21] = "pri_temp";
    argv1[22] = "";

    // 2.添加管理员
    ret = cps_rights_remote_add_user(23, argv1);
    if (ret) {
        // 添加失败,恢复未绑定ukey的管理员数据
        ret = cps_insert_data_to_db(
            CPS_ADMIN_INFO_TABLE, "tenant_id='1000',manager_type='3',manager_name='%s'", tenant_name);
        API_CHECK_FUNC(ret, "cps_insert_data_to_db failed");
    }
out:
    return ret;
}

int cps_rights_add_user_special(int argc, char* argv[]) {
    ECCPUBLICKEYBLOB pBlob                      = {0};
    USER_INFO_T      user_info                  = {0};
    char             time[DEFAULT_TIME_STR_LEN] = {0};
    int              ret                        = 0;
    user_info.tenant_id                         = 0;

    user_info.type      = atoi(argv[4]);
    user_info.sn        = argv[6];
    user_info.name      = argv[8];
    char* pin           = argv[10];
    int   cover         = atoi(argv[12]);
    user_info.tenant_id = atoi(argv[14]);
    if (argc == 17) {
        user_info.remark = argv[16];
    } else {
        user_info.remark = NULL;
    }
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};

    // 检测用户是否注册
    ret = db_check_user_info(user_info.sn, user_info.name, user_info.type, user_info.tenant_id);
    if (ret) {
        return -1;
    }

    user_info.id = db_get_manager_id(user_info.type);
    if (user_info.id == -1) {
        printf(gettext("there are enough administrators!"));
        return -1;
    }

    // 调用ukey接口
    ret = AccountMgr_Register(user_info.sn, pin, user_info.name, user_info.type, cover, user_info.id, &pBlob);
    if (ret) {
        // 当返回值为-1时在接口内有输出日志
        if (ret != -1) printf(gettext("add user failed!"));
        return ret;
    }

    user_info.login_status = 0;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.create_time = time;
    }

    // 设置login_info
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("add user failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    // 保存信息到数据库
    ret = db_add_user_info(&user_info);
    if (ret) {
        printf(gettext("add user failed!"));
    }

    _save_config();
    return ret;
}

int cps_rights_remote_add_user_special(int argc, char* argv[]) {
    USER_INFO_T user_info                  = {0};
    char        time[DEFAULT_TIME_STR_LEN] = {0};
    int         ret                        = 0;
    user_info.tenant_id                    = 0;

    user_info.type      = atoi(argv[4]);
    user_info.sn        = argv[6];
    user_info.name      = argv[8];
    char* key           = argv[10];
    user_info.tenant_id = atoi(argv[12]);
    if (argc == 15) {
        user_info.remark = argv[14];
    } else {
        user_info.remark = NULL;
    }
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};

    // 检测用户是否注册
    ret = db_check_user_info(user_info.sn, user_info.name, user_info.type, user_info.tenant_id);
    if (ret) {
        return -1;
    }

    user_info.id = db_get_manager_id(user_info.type);
    if (user_info.id == -1) {
        printf(gettext("there are enough administrators!"));
        return -1;
    }

    ret = AccountMgr_RemoteRegister(key, user_info.id);
    if (ret) {
        if (ret != -1) printf(gettext("add user failed!"));
        return ret;
    }

    user_info.login_status = 0;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.create_time = time;
    }
    // 设置login_info
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("add user failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    // 保存信息到数据库
    ret = db_add_user_info(&user_info);
    if (ret) {
        printf(gettext("add user failed!"));
    }

    _save_config();
    return ret;
}

int cps_rights_del_user(int argc, char* argv[]) {
    int   type                   = atoi(argv[4]);
    char* name                   = argv[6];
    char  sn[DEFAULT_SN_STR_LEN] = {0};
    int   manager_id             = 0;
    int   ret                    = 0;
    int   admin_num              = 0;
    int   opera_num              = 0;
    int   audit_num              = 0;
    int   tenant_id              = 0;

    if (argc == 9) {
        tenant_id = atoi(argv[8]);
    }

    if (type == 3) {
        if (argc != 9) {
            printf(gettext("parameter error!"));
            return -1;
        }

        ret = cps_get_num_from_db(CPS_ADMIN_INFO_TABLE, "tenant_id='%d'", tenant_id);
        if (ret <= 1) {
            printf(gettext("not allowed to delete the last admin!"));
            return -1;
        }
    } else {
        // 当只注册了一个管理员的情况下,不允许删除
        db_get_manager_num(&admin_num, &opera_num, &audit_num);
        if (admin_num == 1 && type == 0) {
            printf(gettext("not allowed to delete the last admin!"));
            return -1;
        }
    }

    // 删除数据库中的信息
    ret = db_delete_user(name, type, tenant_id);
    if (ret) {
        printf(gettext("delete user failed!"));
        ret = -1;
    }

    // 删除账户
    if (type != 3) {
        char system_str[1024] = {0};
        sprintf(system_str, "/usr/local/bin/admacct del name '%s' > /dev/null 2>&1 &", name);
        system(system_str);
    }

    _save_config();
    return ret;
}

int cps_rights_login(int argc, char* argv[]) {
    USER_INFO_T user_info                  = {0};
    char        time[DEFAULT_TIME_STR_LEN] = {0};

    user_info.sn                                 = argv[4];
    user_info.name                               = argv[6];
    char* pin                                    = argv[8];
    user_info.login_ip                           = argv[10];
    user_info.session_id                         = argv[12];
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};

    int  ret                         = 0;
    char get_name[USER_NAME_MAX_LEN] = {0};

    // 检查序列号与用户名是否一致
    if (db_check_user_name_and_sn(user_info.sn, user_info.name) != 0) {
        return -1;
    }

    // 获取manager_id
    db_get_user_type_and_id(user_info.sn, &user_info.type, &user_info.id);

    ret = AccountMgr_Login(user_info.sn, pin, user_info.name, user_info.id);
    if (ret) {
        // 返回值为-1时,表示已经输出过日志信息了
        if (ret != -1) printf(gettext("login failed!"));
        return ret;
    }

    user_info.login_status = 1;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.last_login = time;
    }

    // 填充login_info信息
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("login failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    // 修改数据库中的信息
    db_update_login_info(&user_info);

    return ret;
}

/**
 * @brief 生成登录时的随机数
 *
 * @param argc
 * @param argv
 * @return int 0成功
 */
int cps_rights_get_login_random(int argc,char* argv[]) {
    int  ret                           = 0;
    char random[RANDOM_LEN]            = {0};
    char random_base64[RANDOM_LEN * 2] = {0};
    char device_sn[RANDOM_LEN]         = {0};

    // 1.生成随机数
    ret = cps_get_random_soft(random, RANDOM_LEN);
    if (ret) {
        printf(gettext("get random faild"));
        return -1;
    }

    // 2.base64编码
    base64_encode(random, random_base64, RANDOM_LEN);

    // 3.保存到数据库
    time_t current_time = time(NULL);
    db_update_login_random(random_base64, current_time);

    // 4.获取设备序列号
    Db_getDeviceSn(device_sn);

    // 4.输出
    printf("%s,%s\n", random_base64, device_sn);
    return 0;
}

int cps_rights_remote_login(int argc, char* argv[]) {
    USER_INFO_T user_info                  = {0};
    char        time[DEFAULT_TIME_STR_LEN] = {0};

    user_info.name                               = argv[4];
    char* token                                  = argv[6];
    char* hash                                   = argv[8];
    user_info.login_ip                           = argv[10];
    user_info.session_id                         = argv[12];
    char          random[RANDOM_LEN]             = {0};
    char          randomB[RANDOM_LEN]            = {0};
    char          random_base64[RANDOM_LEN * 2]  = {0};
    char          device_sn[RANDOM_LEN]          = {0};
    char          device_sn_local[RANDOM_LEN]    = {0};
    char          sign_value[RANDOM_LEN * 8]     = {0};
    char          sign_data[RANDOM_LEN * 16]     = {0};
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};
    char          sn[DEFAULT_SN_STR_LEN]         = {0};
    user_info.tenant_id                          = 0;

    int ret = 0;

    // 获取租户id
    user_info.tenant_id = db_get_tenant_id_by_session_ip(user_info.session_id, user_info.login_ip);
    if (user_info.tenant_id == -1) {
        printf(gettext("no corresponding tenant!"));
        return -1;
    }

    // 获取manager_id
    ret = db_select_manager_id_and_sn(user_info.name, user_info.tenant_id, &user_info.type, &user_info.id, sn);
    if (ret) {
        printf(gettext("user not registered!"));
        return ret;
    }

    // 解码
    base64_decode(token, sign_data, strlen(token));

    _get_info_from_signData(sign_data, random, randomB, device_sn, sign_value);

    // 判断随机数
    base64_encode(random, random_base64, RANDOM_LEN);
    if (db_update_check_random(random_base64)) {
        DEBUG_CLI(ERR_DEBUG, "check random fail");
        printf(gettext("login failed!"));
        return -1;
    }

    // 判断序列号
    Db_getDeviceSn(device_sn_local);

    if (strcmp(device_sn_local, device_sn) != 0) {
        DEBUG_CLI(ERR_DEBUG, "check rdevice sn fail");
        printf(gettext("login failed!"));
        return -1;
    }

    user_info.sn = sn, ret = AccountMgr_RemoteLogin(hash, sign_value, user_info.id);
    if (ret) {
        DEBUG_CLI(ERR_DEBUG, "AccountMgr_RemoteLogin fail");
        if (ret != -1) printf(gettext("login failed!"));
        return ret;
    }

    user_info.login_status = 1;
    // 获取当前时间,时间格式:年:月:日 时:分:秒
    if (!_get_time_str(time)) {
        user_info.last_login = time;
    }

    // 填充login_info信息
    ret = fill_login_info(
        user_info.name,
        user_info.type,
        user_info.last_login,
        user_info.login_ip,
        user_info.session_id,
        user_info.login_status,
        log_info);
    if (ret) {
        printf(gettext("login failed!"));
        return ret;
    }
    log_info[DEFAULT_LOG_INFO_LEN] = '\0';
    user_info.login_info           = log_info;

    // 修改数据库中的信息
    ret = db_update_login_info(&user_info);
    if (ret) {
        printf(gettext("login failed!"));
    }

    return ret;
}

int cps_rights_logout(int argc, char* argv[]) {
    char*         name                           = argv[4];
    int           ret                            = -1;
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};
    int           tenant_id                      = 0;

    if (argc == 7) {
        tenant_id = atoi(argv[6]);
    }

    ret = db_get_login_info(name, tenant_id, log_info);
    if (ret) {
        printf(gettext("login out failed!"));
        return ret;
    }

    ret = mod_loginfo(log_info);
    if (ret) {
        printf(gettext("login out failed!"));
        return ret;
    }

    // 修改数据库中的登录状态
    ret = db_set_login_info_log_out(name, log_info, tenant_id);
    if (ret) {
        printf(gettext("login out failed!"));
    }

    return ret;
}

int cps_rights_logout_by_ip(int argc, char* argv[]) {
    char* ip         = argv[4];
    char* session_id = NULL;
    int   tenant_id  = 0;
    if (argc == 7) {
        if (!strcmp(argv[6], "sessionid")) {
            session_id = argv[6];
        } else {
            tenant_id = atoi(argv[6]);
        }
    } else if (argc == 9) {
        session_id = argv[6];
        tenant_id  = atoi(argv[8]);
    }

    int           ret                            = 0;
    unsigned char log_info[DEFAULT_LOG_INFO_LEN] = {0};
    char*         name                           = NULL;

    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;

    if (sqlite3_open(DB_FILE_PATH, &db_admin)) {
        printf(gettext("open db failed!"));
        return -1;
    }

    // 如果是初始化状态不注销用户信息
    rc = sqlite3_get_table_printf(db_admin, "select system_init_status from device_init", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        goto out;
    }

    if (row > 0) {
        int system_init = atoi(result[col]);
        if (system_init == 0) {
            goto out;
        }
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (session_id != NULL) {
        rc = sqlite3_get_table_printf(
            db_admin,
            "select manager_name,login_info from admin_info where tenant_id='%d' and login_ip='%s' and session_id "
            "='%s'",
            &result,
            &row,
            &col,
            &errmsg,
            tenant_id,
            ip,
            session_id);
    } else {
        rc = sqlite3_get_table_printf(
            db_admin,
            "select manager_name,login_info from admin_info where tenant_id='%d' and login_ip='%s'",
            &result,
            &row,
            &col,
            &errmsg,
            tenant_id,
            ip);
    }
    if (rc != SQLITE_OK) {
        goto out;
    }

    for (int i = 1; i <= row; i++) {
        memset(log_info, '\0', DEFAULT_LOG_INFO_LEN);
        name = result[col * i];
        memcpy(log_info, result[col * i + 1], strlen(result[col * i + 1]));

        ret = mod_loginfo(log_info);

        // 修改数据库中的登录状态
        ret = db_set_login_info_log_out(name, log_info, tenant_id);
        if (ret) {
            printf(gettext("login out failed!"));
        }
    }

out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (db_admin != NULL) {
        sqlite3_close(db_admin);
        db_admin = NULL;
    }

    return ret;
}

int cps_rights_get_status(int argc, char* argv[]) {
    sqlite3* db_hsm = NULL;
    int      rc     = 0;
    char*    errmsg = NULL;
    char**   result = NULL;
    int      row, col;
    int      ret       = -1;
    int      tenant_id = 0;
    if (argc == 5) {
        tenant_id = atoi(argv[4]);
    }

    // 从文件中获取用户信息填充到cjson结构中
    char* json_str = NULL;

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON* array = cJSON_AddArrayToObject(root, "InfoList");
    if (NULL == array) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json array failed!");
        goto out;
    }

    cJSON* tmp = NULL;
    tmp        = cJSON_CreateObject();
    if (NULL == tmp) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    if (sqlite3_open(CPS_DB_PATH, &db_hsm)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "open database failed!");
        return -1;
    }

    rc = sqlite3_get_table_printf(
        db_hsm,
        "select manager_name,login_info from admin_info where tenant_id='%d' and login_status=1",
        &result,
        &row,
        &col,
        &errmsg,
        tenant_id);
    if (rc != SQLITE_OK) {
        printf("执行数据库查询操作失败\n");
        goto out;
    }

    char name[USER_NAME_MAX_LEN]                = {0};
    int  type                                   = 0;
    char last_login[DEFAULT_TIME_STR_LEN]       = {0};
    char login_ip[DEFAULT_IP_STR_LEN]           = {0};
    char session_id[DEFAULT_SESSION_ID_STR_LEN] = {0};
    int  login_status                           = 0;

    for (int i = 1; i <= row; i++) {
        memset(name, '\0', USER_NAME_MAX_LEN);
        memset(last_login, '\0', DEFAULT_TIME_STR_LEN);
        memset(login_ip, '\0', DEFAULT_IP_STR_LEN);
        ret = decom_log_info(
            name, &type, last_login, login_ip, session_id, &login_status, (unsigned char*)result[col * i + 1]);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "get %s log_info failed!", result[col * i]);
        }

        if (login_status == 1) {
            cJSON* tmp = cJSON_CreateObject();
            if (NULL == tmp) {
                DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
                goto out;
            }

            cJSON_AddStringToObject(tmp, "user_name", name);
            cJSON_AddNumberToObject(tmp, "user_type", type);
            cJSON_AddNumberToObject(tmp, "login_status", login_status);
            cJSON_AddStringToObject(tmp, "login_time", last_login);
            cJSON_AddStringToObject(tmp, "login_ip", login_ip);
            cJSON_AddStringToObject(tmp, "session_id", session_id);

            cJSON_AddItemToArray(array, tmp);
        }
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    sqlite3_close(db_hsm);

    json_str = cJSON_PrintUnformatted(root);
    printf("%s\n", json_str);

    ret = 0;

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }

    return ret;
}

int cps_rights_check_privileage(int argc, char* argv[]) {
    char*  ip         = argv[4];
    char*  session_id = argv[6];
    char*  route      = argv[8];
    int    rc         = 0;
    char*  errmsg     = NULL;
    char** result     = NULL;
    int    row, col;
    int    pri_id            = -1;
    int    admin_num         = 0;
    int    opera_num         = 0;
    int    audit_num         = 0;
    int    login_manager_num = 0;
    int    ret               = 0;
    int    check_flag        = 0;
    int    is_tenant         = 0;

    if (db_check_is_administrator(ip, session_id) == 0) {
        return 1;
    }

    if (argc == 11) {
        is_tenant = atoi(argv[10]);
    }

    // 获取隐藏文件,判断是否做权限校验
    FILE* fw       = NULL;
    char  check[4] = {0};

    fw = fopen(FW_AC_FILE_PATH, "r");
    if (fw != NULL) {
        ssize_t bytes_read = fread(check, 1, 4, fw);
        if (bytes_read) {
            check_flag = atoi(check);

            if (check_flag) {
                return 1;
            }
        }
    }
    if (fw) fclose(fw);

    // 1. 获取注册的管理员,操作员,审计员数目
    db_get_manager_num(&admin_num, &opera_num, &audit_num);

    // 2. 通过路由查看对应所需要的权限
    if (sqlite3_open(DB_FILE_PATH, &db_privileage)) {
        printf(gettext("open db failed!"));
        return -1;
    }

    if (is_tenant) {
        rc = sqlite3_get_table_printf(
            db_privileage,
            "select pri_id from route_privileage where route_name='%s' and pri_id=4",
            &result,
            &row,
            &col,
            &errmsg,
            route);
    } else {
        rc = sqlite3_get_table_printf(
            db_privileage,
            "select pri_id from route_privileage where route_name='%s' and pri_id!=4",
            &result,
            &row,
            &col,
            &errmsg,
            route);
    }

    if (rc != SQLITE_OK) {
        printf(gettext("check failed!"));
        return rc;
    }

    if (row > 0) {
        pri_id = atoi(result[col]);
        switch (pri_id) {
            case 0:  // 超级管理员权限
                login_manager_num = db_get_manager_num_with_login(0);
                // 当前管理员数目大于半数且当前ip登录了一个管理员
                if ((admin_num < login_manager_num * 2 && admin_num != 0) &&
                    1 <= db_get_manager_num_by_ip_and_type(ip, session_id, 0)) {
                    ret = 1;
                    goto out;
                } else {
                    printf(gettext("super administrator permissions required!"));
                    ret = 0;
                    goto out;
                }
                break;
            case 1:  // 普通管理员权限
                // 当前ip是否登录一个管理员
                if (1 <= db_get_manager_num_by_ip_and_type(ip, session_id, 0)) {
                    ret = 1;
                    goto out;
                } else {
                    printf(gettext("administrator permissions required!"));
                    ret = 0;
                    goto out;
                }
                break;
            case 2:  // 操作员权限
                     // 当前ip是否登录一个操作员
                if (1 <= db_get_manager_num_by_ip_and_type(ip, session_id, 1)) {
                    ret = 1;
                    goto out;
                } else {
                    printf(gettext("operator permissions required!"));
                    ret = 0;
                    goto out;
                }
                break;
            case 3:  // 审计员权限
                     // 当前ip是否登录一个审计员
                if (1 <= db_get_manager_num_by_ip_and_type(ip, session_id, 2)) {
                    ret = 1;
                    goto out;
                } else {
                    printf(gettext("auditor permissions required!"));
                    ret = 0;
                    goto out;
                }
                break;
            case 4:  // 租户管理员权限
                if (1 <= db_get_manager_num_by_ip_and_type(ip, session_id, 3)) {
                    ret = 1;
                    goto out;
                } else {
                    printf(gettext("tenant permissions required!"));
                    ret = 0;
                    goto out;
                }
                break;
            default:
                return 1;
                break;
        }
    }

out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    sqlite3_close(db_privileage);
    db_privileage = NULL;
    return ret;
}

int cps_rights_set_pin(int argc, char* argv[]) {
    int   ret       = 0;
    char* sn        = argv[4];
    char* c_old_pin = argv[6];
    char* c_new_pin = argv[8];

    DEVHANDLE    phDev         = {0};
    HAPPLICATION phApplication = {0};
    int          RetryCount    = 0;

    // 1.通过SN号获取设备句柄
    if (_get_device_by_sn(sn, &phDev) == -1) {
        printf(gettext("device not found!"));
        return -1;
    }

    // 2.获取应用句柄
#ifdef UKEY_USE_DEFAULT_APP
    ret = SKF_OpenApplication(phDev, UKEY_APP_NAME, &phApplication);
    if (ret == SAR_APPLICATION_NOT_EXISTS) {
        ret = SKF_OpenApplication(phDev, UKEY_APP_NAME1, &phApplication);
    }
#else
    char AppName[1024] = {0};
    int  len           = 1024;

    SKF_EnumApplication(phDev, AppName, &len);

    ret = SKF_OpenApplication(phDev, AppName, &phApplication);
#endif
    if (ret != 0) {
        printf(gettext("open device failed!"));
        return -1;
    }

    ret = SKF_ChangePIN(phApplication, USER_TYPE, c_old_pin, c_new_pin, &RetryCount);
    if (ret != 0) {
        switch (ret) {
            case SAR_PIN_INCORRECT:
                printf(gettext("pin code error,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LOCKED:
                printf(gettext("pin code locked!"));
                break;
            case SAR_PIN_INVALID:
                printf(gettext("pin code invalid,remaining retry count:%d!"), RetryCount);
                break;
            case SAR_PIN_LEN_RANGE:
                printf(gettext("pin len error!"));
                break;
            default:
                printf(gettext("pin error,remaining retry count:%d!"), RetryCount);
                break;
        }
        ret = -1;
        goto out;
    }

out:
    if (phApplication) SKF_CloseApplication(phApplication);
    if (phDev) SKF_DisConnectDev(phDev);

    return ret;
}

int cps_rights_set_user_info(int argc, char* argv[]) {
    char* name       = argv[4];
    int   login_type = atoi(argv[6]);
    char* pri_temp   = "";
    char* remark     = "";
    int   tenant_id  = 0;
    for (int i = 7; i < argc; i += 2) {
        if (!strcmp(argv[i], "pri_temp")) {
            pri_temp = argv[i + 1];
        } else if (!strcmp(argv[i], "tenant_id")) {
            tenant_id = atoi(argv[i + 1]);
        } else {
            remark = argv[i + 1];
        }
    }

    if (db_set_set_user_remark(name, login_type, pri_temp, tenant_id, remark) != 0) {
        printf(gettext("failed to modify comments!"));
        return -1;
    }

    _save_config();
    return 0;
}
