/*
 * Project: base
 * Moudle: cps
 * File: cps_work_order.c
 * Created Date: 2023-08-29 14:59:11
 * Author: --
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include "cps_common.h"
#include "cps_msg.h"
#include "cps_work_order.h"
#include "database_api.h"
#include "public/key_operate.h"
#include "cps_vsm_manage.h"

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */
extern int _get_time_str(char* time_str);

/* ======================================================================================
 * private implementation
 */
static int _set_tenant_src_cb(char* str, char* request_id) {
    int ret = 0;

    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }
    DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\r\n", str);

    cJSON* tmp = NULL;
    tmp        = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret = tmp->valueint;
            goto out;
        }
    } else {
        ret = -1;
        goto out;
    }

out:
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

static int _set_tenant_set_request_str(char* tenant_id, int cpu, int mem, char* request_id, char* out_str) {
    int    ret      = -1;
    char*  json_str = NULL;
    cJSON* root     = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "oprType", "tenant_request");
    cJSON_AddStringToObject(root, "tentId", tenant_id);
    cJSON* tenant_src = cJSON_AddObjectToObject(root, "tenantReq");
    cJSON_AddNumberToObject(tenant_src, "cpuNum", cpu);
    cJSON_AddNumberToObject(tenant_src, "memNum", mem);

    json_str = cJSON_PrintUnformatted(root);
    memcpy(out_str, json_str, strlen(json_str));

    ret = 0;

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

static int _set_tenant_del_request_str(char* tenant_id, char* request_id, char* out_str) {
    int    ret      = -1;
    char*  json_str = NULL;
    cJSON* root     = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "oprType", "tenant_delete");
    cJSON_AddStringToObject(root, "tentId", tenant_id);

    json_str = cJSON_PrintUnformatted(root);
    memcpy(out_str, json_str, strlen(json_str));

    ret = 0;

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

int cps_work_order_check_vsm_type(int wo_index, char* vsm_id) {
    int   ret                             = 0;
    char  type[2]                         = {};
    char  work_order_vsmType[2]           = {};
    char  vsm_manage_vsmType[2]           = {};
    char  *token                          = NULL;
    char  *tmp_vsm_id                     = NULL;

    tmp_vsm_id = malloc(strlen(vsm_id) + 1);
    if (tmp_vsm_id != NULL) {
        memset(tmp_vsm_id, 0, strlen(vsm_id) + 1);
        memcpy(tmp_vsm_id, vsm_id, strlen(vsm_id));
    } else {
        DEBUG_CPS_CLI(ERR_DEBUG, "tmp_vsm_id malloc fail");
        ret = -1;
        goto out;
    }

    cps_get_data_from_db(CPS_WORK_ORDER_TABLE, "wo_index", (void*)&wo_index, "vsm_app_type", work_order_vsmType, 2, true);
    token = strtok(tmp_vsm_id, ",");
    while (token != NULL) {
        memset(vsm_manage_vsmType, 0, sizeof(vsm_manage_vsmType));
        cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void*)token, "vsm_type", vsm_manage_vsmType, 2, false);
        if (strcmp(work_order_vsmType, vsm_manage_vsmType) != 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "work_order_vsmType[%s], vsm_manage_vsmType[%s]", work_order_vsmType, vsm_manage_vsmType);
            printf (gettext("vsm type not matching"));
            ret = -1;
            goto out;
        }

        token = strtok(NULL, ",");
    }
out:
    if (tmp_vsm_id)
        free(tmp_vsm_id);
    return ret;
}

int send_set_tenant_src_request(char* ip, char* tenant_id, int cpu, int mem) {
    msg_header_t* msg_header;
    int           ret             = 0;
    char          request_id[9]   = {0};
    int           request_id_len  = 8;
    char          body_data[2048] = {0};
    int           body_len        = 2048;
    char          signature1[128] = {0};
    int           signature1_len  = 0;

    msg_header = (msg_header_t*)malloc(body_len);
    memset(msg_header, '\0', body_len);
    char request_data[1024]      = {0};
    char request_data_base[1024] = {0};
    ret                          = cps_generate_random(request_id, request_id_len);

    _set_tenant_set_request_str(tenant_id, cpu, mem, request_id, request_data);
    ret = sign_with_internal_key(request_data, strlen(request_data), signature1, &signature1_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key!");
    base64_encode(request_data, request_data_base, strlen(request_data));

    ret = cps_msg_body_comp(
        ip, request_id, signature1, request_data_base, strlen(request_data_base), body_data, &body_len);
    cps_request_init(msg_header, body_data, body_len, VSM_MAIN_TYPE, CMD_TENANT_REQUEST);
    ret = cps_request_send(msg_header, _set_tenant_src_cb, request_id);

    if (msg_header) {
        free(msg_header);
    }
out:
    return ret;
}

int send_del_tenant_src_request(char* ip, char* tenant_id) {
    msg_header_t* msg_header;
    int           ret             = 0;
    char          request_id[9]   = {0};
    int           request_id_len  = 8;
    char          body_data[2048] = {0};
    int           body_len        = 2048;
    char          signature1[128] = {0};
    int           signature1_len  = 0;

    msg_header = (msg_header_t*)malloc(body_len);
    memset(msg_header, '\0', body_len);
    char request_data[1024]      = {0};
    char request_data_base[1024] = {0};
    ret                          = cps_generate_random(request_id, request_id_len);

    _set_tenant_del_request_str(tenant_id, request_id, request_data);
    ret = sign_with_internal_key(request_data, strlen(request_data), signature1, &signature1_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key!");
    base64_encode(request_data, request_data_base, strlen(request_data));

    ret = cps_msg_body_comp(
        ip, request_id, signature1, request_data_base, strlen(request_data_base), body_data, &body_len);
    cps_request_init(msg_header, body_data, body_len, VSM_MAIN_TYPE, CMD_TENANT_DALETE);
    ret = cps_request_send(msg_header, _set_tenant_src_cb, request_id);

    if (msg_header) {
        free(msg_header);
    }
out:
    return ret;
}
/* ======================================================================================
 * implementation
 */
int cps_work_order_check_vsm(int argc, char* argv[]) {
    char* name    = argv[4];
    int   vsm_cpu = atoi(argv[6]);
    int   vsm_mem = atoi(argv[8]);

    return db_work_order_check_vsm(name, vsm_cpu, vsm_mem);
}

int cps_work_order_approval(int argc, char* argv[]) {
    int   ret                            = 0;
    int   wo_index                       = 0;
    char* vsm_id                         = NULL;
    int   opinion                        = 0;
    char* session_id                     = NULL;
    char* annot                          = NULL;
    char *token                          = NULL;
    char  time_str[DEFAULT_TIME_STR_LEN] = {0};
    char  tenant_name[64]                = {0};
    char tenant_id[16] = {0};
    int   vsm_num                        = 0;
    char   type[2]                        = {};
    char  work_order_vsmType[2]           = {};
    char  vsm_manage_vsmType[2]           = {};
    char   flavor[2]                      = {};
    for (int i = 3; i < argc; i = i + 2) {
        if (strcasecmp(argv[i], "wo_index") == 0) {
            wo_index = atoi(argv[i + 1]);
        } else if (strcasecmp(argv[i], "vsm_id") == 0) {
            vsm_id = argv[i + 1];
        } else if (strcasecmp(argv[i], "vsm_num") == 0) {
            vsm_num = atoi(argv[i + 1]);
        } else if (strcasecmp(argv[i], "opinion") == 0) {
            opinion = atoi(argv[i + 1]);
        } else if (strcasecmp(argv[i], "sessionid") == 0) {
            session_id = argv[i + 1];
        } else if (strcasecmp(argv[i], "annotations") == 0) {
            annot = argv[i + 1];
        } else {
            printf(gettext("invalid command"));
            return -1;
        }
    }

    // 获取申请工单中的虚机申请数以及租户名
    ret = db_work_order_get_tenant(wo_index, tenant_name, tenant_id);
    if (ret) {
        printf(gettext("no corresponding tenant found!"));
        return ret;
    }

    if (opinion == 1) {
        // 获取工单类型
        cps_get_data_from_db(CPS_WORK_ORDER_TABLE, "wo_index", (void*)&wo_index, "type", type, 2, true);
        if (atoi(type) == 0) {
            // 1.检测网络地址是否设置
            ret = cps_get_num_from_db(CPS_ADDR_INFO_TABLE, "addr_name!=''");
            if (!ret) {
                printf(gettext("address information not configured!"));
                return -1;
            }
            // 2.判断分配虚机与工单虚机是否匹配
            ret = cps_work_order_check_vsm_type(wo_index, vsm_id);
            if (ret)
                return ret;

            ret = cps_update_data_to_db(
                CPS_VSM_MANAGE_TABLE,
                "tenant_name='%s' where instr(','||'%s'||',',','||vsm_id||',') >0",
                tenant_name,
                vsm_id);
            if (ret) {
                printf(gettext("approval faild!"));
                return ret;
            }

            db_work_order_update_ip_info(tenant_name,vsm_id);
        } else {
            char vsm_id[64] = {0};
            char   vsm_state[4]                      = {0};
            cps_get_data_from_db(CPS_WORK_ORDER_TABLE, "wo_index", (void*)&wo_index, "vsm_app_flavor", flavor, 2, true);
            cps_get_data_from_db(CPS_WORK_ORDER_TABLE, "wo_index", (void*)&wo_index, "vsm_id", vsm_id, 64, true);
            cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void*)vsm_id, "opt_state", vsm_state, 4, false);

            // 判断虚拟机是否存在
            ret = cps_get_num_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id=\'%s\'", vsm_id);
            if (!ret) {
                printf(gettext("vsm not exist"));
                return -1;
            }

            // 资源判断是否足够
            ret= cps_vsm_source_check(vsm_id,atoi(flavor));
            if (ret) {
                printf(gettext("insufficient memory and CPU resources!"));
                return ret;
            }

            // 判断虚机是否关闭
            if(atoi(vsm_state) != 3 && atoi(vsm_state) != 13){
                printf(gettext("need to shut down the virtual machine!"));
                return -1;
            }

            //更新规格
            char* argva[10] = {0};
            argva[5]        = tenant_name;
            argva[7]        = vsm_id;
            argva[9]        = flavor;
            ret = cps_vsm_spec_set(10,argva);
            if(ret){
                return ret;
            }
            // cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "update_flag=1 where vsm_id='%s'", vsm_id);
        }
    }else{
        vsm_num = 0;
    }
    // 获取审批时间
    _get_time_str(time_str);

    // 写入数据库
    ret = db_set_work_order_approval(wo_index, opinion, session_id, annot, time_str, vsm_num);
    if (ret) {
        printf(gettext("approval faild!"));
    }

out:
    return ret;
}

int cps_work_order_create(int argc, char* argv[]) {
    unsigned int ret  = ERR_NONE;
    int          nrow = 0, ncol = 0;
    int          i = 0;
    int          tenant_id                          = 0;
    int          type                               = 0;
    int          vsm_num                            = 0;
    int          vsm_type                           = 0;
    int          apply_spec                         = 0;
    int          dila_spec                          = 0;
    int          cpu_model                          = 0;
    char*        order_name                         = argv[4];
    char*        vsm_id                             = NULL;
    char*        remark                             = NULL;
    char*        session_id                         = NULL;
    char*        corp_name                          = NULL;
    char**       result                             = NULL;
    char         sql[SQL_LEN_MAX]                   = {0};
    char         time_str[64]                       = {0};
    char         apply_obj[TENANT_NAME_MAX_LEN * 3] = {0};
    char*        tenant_name                        = NULL;

    type = atoi(argv[6]);
    if (type == WORK_ORDER_TYPE_SOURCE) {
        if (((argc != 17) && (argc != 19)) ||
            (strcmp(argv[7], "vsm_num") != 0) ||
            (strcmp(argv[9], "vsm_type") != 0) ||
            (strcmp(argv[11], "apply_spec") != 0) ||
            (strcmp(argv[13], "cpu_model") != 0) ||
            (strcmp(argv[15], "session_id") != 0))
            return ERR_PARAM;
            vsm_num = atoi(argv[8]);
            vsm_type = atoi(argv[10]);
            apply_spec = atoi(argv[12]);
            cpu_model = atoi(argv[14]);
            session_id = argv[16];
            if ((argc == 19) && (strcmp(argv[17], "remark") == 0))
                remark = argv[18];
    }else {
        if (((argc != 13) && (argc != 15)) ||
            (strcmp(argv[7], "vsm_id") != 0) ||
            (strcmp(argv[9], "dila_spec") != 0) ||
            (strcmp(argv[11], "session_id") != 0))
                return ERR_PARAM;
            vsm_id = argv[8];
            dila_spec = atoi(argv[10]);
            session_id = argv[12];
            if ((argc == 15) && (strcmp(argv[13], "remark") == 0))
                remark = argv[14];
    }


    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n", __func__);
    ret = cps_get_time_now(time_str, sizeof(time_str));
    API_CHECK_FUNC(ret, "cps_get_time_now");

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    // 通过session_id获取租户管理员和公司名称
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT tenant_id,manager_name from %s where session_id = \"%s\"", "admin_info", session_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_OPT_AUTH;
        goto out;
    }
    tenant_id = atoi(result[ncol]);

    memset(apply_obj, 0, sizeof(apply_obj));
    strcat(apply_obj, result[ncol + 1]);
    for (i = 1; i < nrow && i < 3; i++) {
        strcat(apply_obj, "\\");
        strcat(apply_obj, result[(ncol * (i + 1)) + 1]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // 资源申请 -- 判断虚拟机类型是否授权
    if (type == WORK_ORDER_TYPE_SOURCE) {
        int used_flag = 0;
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "SELECT used_flag from %s where image_id = %d", "image_map", vsm_type);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        used_flag = atoi(result[ncol]);
        if (used_flag == 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "vsm type unauthorized\n");
            ret = ERR_VSM_TYPE;
            goto out;
        }
    }

    // 资源扩缩容 - 需要虚机模式 + 虚拟机类型
    if (type == WORK_ORDER_TYPE_SPEC) {
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "SELECT cpu_model from %s where vsm_id = \"%s\"", "vsm_manage", vsm_id);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        cpu_model = atoi(result[ncol]);

        memset(sql, 0, sizeof(sql));
        sprintf(sql, "SELECT vsm_type from %s where vsm_id = \"%s\"", "vsm_manage", vsm_id);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        vsm_type = atoi(result[ncol]);
    }

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT corporate_name,name from %s where tenant_id = %d", "tenant_info", tenant_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    corp_name   = result[ncol];
    tenant_name = result[ncol + 1];

    memset(sql, 0, sizeof(sql));
    if (type == WORK_ORDER_TYPE_SOURCE) {
        snprintf(sql, sizeof(sql) - 1, "insert into %s (name, type, applicant, tenant_name, corporate_name, vsm_app_num, vsm_app_flavor, vsm_app_src_mode, vsm_app_type, app_date, approval_status, remark) values(\'%s\', %d, \'%s\', \'%s\', \'%s\', %d, %d, %d, %d, \'%s\', %d, \'%s\')", CPS_WORK_ORDER_TABLE, order_name, type, apply_obj, tenant_name, corp_name, vsm_num, apply_spec, cpu_model, vsm_type, time_str, 0, (remark == NULL) ? "" : remark);
    }else {
        snprintf(sql, sizeof(sql) - 1, "insert into %s (name, type, applicant, tenant_name, corporate_name, vsm_id, vsm_app_flavor, vsm_app_src_mode, vsm_app_type, app_date, approval_status, remark) values(\'%s\', %d, \'%s\', \'%s\', \'%s\', \'%s\', %d, %d, %d, \'%s\', %d, \'%s\')", CPS_WORK_ORDER_TABLE, order_name, type, apply_obj, tenant_name, corp_name, vsm_id, dila_spec, cpu_model, vsm_type,time_str, 0, (remark == NULL) ? "" : remark);
    }
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec(cps_db, sql, 0, 0, 0);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }

out:
    if(result){
        sqlite3_free_table(result);
    }

    print_errmsg(ret);
    // close_cps_db();
    return ret;
}
