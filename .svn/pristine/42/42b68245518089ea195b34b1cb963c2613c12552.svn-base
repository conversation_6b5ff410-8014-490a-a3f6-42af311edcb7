#include "cps_common.h"
#include "cps_msg.h"
#include "cps_image_manage.h"
#include "cps_vsm_manage.h"
#include "key_operate.h"
#include "message.h"

int update_image_import_status(int opt_result, char *request_id)
{
    unsigned int ret = ERR_NONE;
    sqlite3_stmt *stmt;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    char *sql = sqlite3_mprintf("update image_import set opt_status=1,opt_result=? where request_id=?;");
    ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);

    sqlite3_bind_int(stmt, 1, opt_result);
    sqlite3_bind_text(stmt, 2, request_id, -1, SQLITE_STATIC);

    ret = sqlite3_step(stmt);
    if (ret != SQLITE_DONE) {
        usleep(500000);
        if ((ret = sqlite3_step(stmt)) != SQLITE_DONE) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sqlite3_step failed! ret:%d", ret);
            goto out;
        }
    }
    ret = (ret == SQLITE_DONE)? ERR_NONE : ret;

out:
    if (sql)
        sqlite3_free(sql);
    if (stmt)
        sqlite3_reset(stmt);
    if (stmt)
        sqlite3_finalize(stmt);
    //close_cps_db();
    return ret;
}

int image_del_all(char *tenant_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
	char image_file[256] = {0};
    char **result = NULL;

    if (tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select real_image from %s where tenant_name=\'%s\' and export_state=1", CPS_IMAGE_EXPORT_TABLE, tenant_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    for (i = 0; i < nrow; i++) {
        memset(image_file, 0, sizeof(image_file));
        sprintf(image_file, "%s/%s", CPS_VSM_IMAGE_PATH, result[i + 1]);
        unlink(image_file);
    }
    ret = cps_del_data_from_db(CPS_IMAGE_EXPORT_TABLE, "tenant_name=\'%s\'", tenant_name);
    API_CHECK_FUNC(ret, "cps_del_data_from_db");

    ret = cps_del_data_from_db(CPS_IMAGE_IMPORT_TABLE, "tenant_name=\'%s\'", tenant_name);
    API_CHECK_FUNC(ret, "cps_del_data_from_db");
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int image_del_proc(char *image_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "delete from %s where real_image = \'%s\'", CPS_IMAGE_EXPORT_TABLE, image_name);
    if (sqlite3_exec(cps_db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }

out:
    //close_cps_db();
    return ret;
}

int image_import_proc(char *image_name, char *vsm_ipv4, char *vsm_ipv6, char *tenant_name, char *request_id)
{
    unsigned int ret = ERR_NONE;
	char import_time[64] = {0};
	char image_ver[64] = {0};

    //获取时间
    memset(import_time, 0, sizeof(import_time));
    cps_get_time_now(import_time, sizeof(import_time));

    //获取影像版本
    memset(image_ver, 0, sizeof(image_ver));
    ret = cps_get_data_from_db(CPS_IMAGE_EXPORT_TABLE, "real_image", (void *)image_name, "image_ver", image_ver, sizeof(image_ver), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = cps_insert_data_to_db(CPS_IMAGE_IMPORT_TABLE, "(tenant_name,request_id,image_ver,vsm_ipv4,vsm_ipv6,import_time,opt_status) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',0)", tenant_name, request_id, image_ver, (strlen(vsm_ipv4) > 0)? vsm_ipv4 : "", (strlen(vsm_ipv6) > 0)? vsm_ipv6 : "", import_time);
    API_CHECK_FUNC(ret, "cps_insert_data_to_db");
out:
    return ret;
}

int image_export_proc(char *vsm_id, char *tenant_name, char *request_id)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int version = 1000;
    int vsm_type = 0;
    char image_name[256] = {0};
	char tmp[8] = {0};
	char vsm_ipv4[16] = {0};
	char vsm_ipv6[64] = {0};
	char vsm_name[128] = {0};
    char real_image[256] = {0};
    char image_path[256] = {0};
	char export_time[64] = {0};
	char image_ver[64] = {0};
	char sql[SQL_LEN_MAX] = {0};
	char **result = NULL;

    if (vsm_id == NULL || tenant_name == NULL ||request_id == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取当前数据库存放的最新版本号，没有使用默认的version
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select real_image,ver_num from %s where vsm_id = \'%s\' order by ver_num DESC", CPS_IMAGE_EXPORT_TABLE, vsm_id);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    version =  (nrow <= 0)? 1000 : (atoi(result[ncol + 1]) + 1);
    convert_to_string(version, image_ver);
    DEBUG_CPS_CLI(COMMON_DEBUG, "image version:%s %d\n", image_ver, version);

    //如果影像数量大于5个，需删除最旧的影像文件
    if (nrow > 4) {
        for (i = 4; i < nrow; i++) {
            memset(sql, 0, sizeof(sql));
            sprintf(sql, "delete from %s where real_image = \'%s\'", CPS_IMAGE_EXPORT_TABLE, result[ncol * (i + 1)]);
            sqlite3_busy_handler(cps_db, callback_db, NULL);
            if (sqlite3_exec(cps_db, sql, NULL, NULL, NULL) != SQLITE_OK) {
                DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
                ret = ERR_SQL_OPT;
                goto out;
            }
            //删除本地文件
            DEBUG_CPS_CLI(COMMON_DEBUG, "image_file:%s\n", result[ncol * (i + 1)]);
            memset(image_path, 0, sizeof(image_path));
            sprintf(image_path, "%s/%s", CPS_VSM_IMAGE_PATH, result[ncol * (i + 1)]);
            unlink(image_path);
        }
    }

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "vsm_type", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    vsm_type = atoi(tmp);

    memset(vsm_name, 0, sizeof(vsm_name));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "vsm_name", vsm_name, sizeof(vsm_name), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
    ret = cps_get_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id", (void *)vsm_id, "vsm_ipv4", vsm_ipv4, sizeof(vsm_ipv4), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    memset(vsm_ipv6, 0, sizeof(vsm_ipv6));
    ret = cps_get_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id", (void *)vsm_id, "vsm_ipv6", vsm_ipv6, sizeof(vsm_ipv6), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    memset(export_time, 0, sizeof(export_time));
    cps_get_time_now(export_time, sizeof(export_time));

    memset(image_name, 0, sizeof(image_name));
    sprintf(image_name, "%s_%s", vsm_name, request_id);

    memset(real_image, 0, sizeof(real_image));
    sprintf(real_image, "%s_%s.image", vsm_id, request_id);

    //插入数据
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "insert into %s (real_image,image_name,tenant_name,vsm_id,vsm_type,vsm_name,vsm_ipv4,vsm_ipv6,ver_num,image_ver,export_state,export_time,request_id) values(\'%s\',\'%s\',\'%s\',\'%s\',%d,\'%s\',\'%s\',\'%s\',%d,\'%s\',%d,\'%s\',\'%s\')", CPS_IMAGE_EXPORT_TABLE, real_image, image_name, tenant_name, vsm_id, vsm_type, vsm_name, (strlen(vsm_ipv4) > 0)? vsm_ipv4 : "", (strlen(vsm_ipv6) > 0)? vsm_ipv6 : "", version, image_ver, EXPORT_START, export_time, request_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    if (sqlite3_exec(cps_db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int image_file_sign(char *file_name, char *sign_data, int *data_len)
{
    unsigned int ret = ERR_NONE;
    unsigned long size = 0, read = 0;
    char file_path[256] = {0};
    char *content = NULL;
    FILE *fp = NULL;

    if ((file_name == NULL) || (sign_data == NULL))
        return ERR_PARAM;

    memset(file_path, 0, sizeof(file_path));
    sprintf(file_path, "%s/%s", CPS_VSM_IMAGE_PATH, file_name);
    fp = fopen(file_path, "rb");
    if (fp == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "file:%s open failed\n", file_path);
        return -1;
    }

    fseek(fp, 0, SEEK_END);
    size = ftell(fp);
    fclose(fp);

    fp = fopen(file_path, "rb");
    if (fp == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "file:%s open failed\n", file_path);
        ret = -1;
        goto out;
    }

    content = (char *)malloc(size + 1);
    if (content == NULL) {
        ret = ERR_MALLOC;
        goto out;
    }

    memset(content, 0, size);
    while((read = fread(content, 1, size, fp)) > 0) {
        if (read == size) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "size %ld read %ld content %ld\n", size, read, strlen(content));
            break;
        }
    }

    ret = sign_with_internal_key(content, size, sign_data, data_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

out:
    fclose(fp);
    if (content != NULL) {
        free(content);
        content = NULL;
    }

    return ret;
}

int image_import_data_comp(char *request_id, char *image_name, char *vsm_id, char *callback_ip, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char image_url[256] = {0};
    char image_addr[64] = {0};
    char sign_data[1024] = {0};
    char callback_url[128];
    char *pData = NULL;
    char *pRequestid = NULL;
    cJSON *root = NULL;
    int len = 0;

    //获取影像上传地址
    ret = cps_get_image_addr(image_addr, sizeof(image_addr));
    API_CHECK_FUNC(ret, "cps_get_image_addr");

    memset(image_url, 0, sizeof(image_url));
    cps_url_comp(image_addr, HTTP_SERVER_PORT, image_name, image_url, sizeof(image_url));

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));

    //对文件进行签名
    memset(sign_data, 0, sizeof(sign_data));
    len = sizeof(sign_data);
    ret = image_file_sign(image_name, sign_data, &len);
    API_CHECK_FUNC(ret, "image_file_sign");

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "import");
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
	cJSON_AddStringToObject(root, "imageUrl", image_url);
	cJSON_AddStringToObject(root, "alg", "SM2WithSM3");
	cJSON_AddStringToObject(root, "sign", sign_data);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        //data[strlen(pData)] = '\0';
    }
out:
    if (pData != NULL)
        cJSON_free(pData);
    if (root != NULL)
        cJSON_Delete(root);

    return ret;
}

int image_export_data_comp(char *request_id, char *vsm_id, char *callback_ip, char *data, unsigned int data_len, int drift_flag)
{
    int ret = ERR_NONE;
    char callback_url[128];
    char *pData = NULL;
    cJSON *root = NULL;

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "export");
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);
    if (drift_flag == true)
        cJSON_AddBoolToObject(root, "is_drift", 1);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        //data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

int cps_image_export_result_check(char *vsm_id, char *request_id)
{
    unsigned int ret = ERR_VSM_SYMM;
    int nrow = 0, ncol = 0;
    int i = 0;
    int time_out = 60; //单位秒
	char sql[SQL_LEN_MAX] = {0};
	char image_file[256] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (request_id == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    sprintf(sql, "select export_state from %s where request_id=\'%s\'", CPS_IMAGE_EXPORT_TABLE, request_id);
    while(time_out > 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "%s", sql);
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
            sqlite3_free(err_msg);
            ret = ERR_SQL_OPT;
            break;
        }
        DEBUG_CPS_CLI(COMMON_DEBUG, "export_state1 %d\n", atoi(result[1]));
        if (atoi(result[1]) == EXPORT_SUCCESS) {
            ret = ERR_NONE;
            break;
        }else if(atoi(result[1]) == EXPORT_FAIL) {
            ret = ERR_VSM_SYMM;
            break;
        }
        sleep(1);
        time_out -= 1;
        if (time_out == 0) ret = ERR_VSM_SYMM;
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    DEBUG_CPS_CLI(COMMON_DEBUG, "ret %d\n", ret);
    return ret;
}

int cps_image_export(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *tenant_name = argv[5];
    char *vsm_id = argv[7];

    ret = cps_image_export_proc(tenant_name, vsm_id, NULL, 0, false);
out:
    print_errmsg(ret);
    return ret;
}

int cps_image_export_proc(char *tenant_name, char *vsm_id,char *req_id, int id_len, int drift_flag)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char *data_base64 = NULL;
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char tenant[128] = {0};
    char request_id[16] = {0};
    char host_ip[64] = {0};
    char callback_ip[64] = {0};
    msg_header_t *msg_header = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    if (tenant_name == NULL || vsm_id == NULL)
        return ERR_PARAM;

    //虚拟机状态是否允许操作
    ret = vsm_opt_state_check(vsm_id, "export", tenant_name);
    API_CHECK_FUNC(ret, "vsm_opt_state_check");

    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        return ERR_SUPP_OPT;
    }

    //获取回调地址
    ret = cps_get_callback_addr(callback_ip, sizeof(callback_ip));
    API_CHECK_FUNC(ret, "cps_get_callback_addr");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");
    if ((req_id != NULL) && (id_len > strlen(request_id)))
        strcpy(req_id, request_id);

    //获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //组创建虚拟机接口调用的json数据,即传入参数
    ret = image_export_data_comp(request_id, vsm_id, callback_ip, data, sizeof(data), drift_flag);
    API_CHECK_FUNC(ret, "image_export_data_comp");
    data_len = strlen(data);
    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);
    
    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_EXPORT_IMAGE);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm oprate failed! ret:%d\r\n", ret);
        goto out;
    }
    //更新影像数据库
    ret = image_export_proc(vsm_id, tenant_name, request_id);
    API_CHECK_FUNC(ret, "image_export_proc");

    //更新虚拟机操作状态
    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d,request_id=\'%s\' where vsm_id=\'%s\'", STATE_EXPORTING, request_id, vsm_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");
out:
    if (msg_header) {
        free(msg_header);
        msg_header = NULL;
    }
    if (data_base64) {
        free(data_base64);
        data_base64 = NULL;
    }
    return ret;
}

int asymm_image_import_proc(char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    char tmp[8];

    if (state) {
        DEBUG_CPS_CLI(ERR_DEBUG, "asymm proc failed! state:%d err_msg:%s\r\n", state, resp_msg);
    }

    //ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "run_state", tmp, sizeof(tmp), false);
    //API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = update_image_import_status((state == 0)? IMPORT_SUCCESS : IMPORT_FAIL, request_id);
    API_CHECK_FUNC(ret, "update_image_import_status");

    ret = update_vsm_state(STATE_STARTED, -1, "request_id", request_id);
    API_CHECK_FUNC(ret, "update_vsm_opt_state_by_requestid");

out:
    return ret;
}

int asymm_image_export_proc(char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    char vsm_id[64];

    if (state) {
        DEBUG_CPS_CLI(ERR_DEBUG, "asymm proc failed! state:%d err_msg:%s\r\n", state, resp_msg);
    }

    ret = cps_update_data_to_db(CPS_IMAGE_EXPORT_TABLE, "export_state=%d where request_id=\'%s\'", (state == 0)? EXPORT_SUCCESS : EXPORT_FAIL, request_id);
    API_CHECK_FUNC(ret, "cps_update_db_to_db");

    ret = update_vsm_state(STATE_STARTED, -1, "request_id", request_id);
    API_CHECK_FUNC(ret, "update_vsm_state");
    //TODO:写日志
out:
    return ret;
}

int cps_image_import_result_check(char **request_id, int vsm_num)
{
    unsigned int ret = ERR_VSM_SYMM;
    unsigned int rc = 0;
    int nrow = 0, ncol = 0;
    int i = 0;
    int time_out = 60; //单位秒
	char sql[SQL_LEN_MAX] = {0};
	char image_file[256] = {0};
    char **result = NULL;

    if (request_id == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    for (i = 0; i < vsm_num; i++) {
        if ((request_id[i] == NULL) || (strlen(request_id[i]) == 0))
            continue ;
        memset(sql, 0, sizeof(sql));
        sqlite3_busy_handler(cps_db, callback_db, NULL);
        sprintf(sql, "select opt_result,opt_status,remark from %s where request_id=\'%s\'", CPS_IMAGE_IMPORT_TABLE, request_id[i]);
        while(time_out > 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "%s", sql);
            rc = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
            if (rc != SQLITE_OK) {
                DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
                ret = ERR_SQL_OPT;
                break;
            }
            DEBUG_CPS_CLI(COMMON_DEBUG, "opt_result %d opt_status %d\n", atoi(result[ncol]), atoi(result[ncol + 1]));
            if ((atoi(result[ncol]) == IMPORT_SUCCESS) && (atoi(result[ncol + 1]) == 1)) {
                ret = ERR_NONE;
                break;
            }else if ((atoi(result[ncol]) == IMPORT_FAIL) && (atoi(result[ncol + 1]) == 1)) {
                ret = ERR_VSM_SYMM;
                break;
            }
            sleep(1);
            time_out -= 1;
            if (time_out == 0) ret = ERR_VSM_SYMM;
        }
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_image_import(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *tenant_name = argv[4];
    char *image_name = argv[6];
    char *id  = argv[8];

    ret = cps_image_import_proc(tenant_name, image_name, id, NULL, 0, NULL);
out:
    print_errmsg(ret);
    return ret;
}

int cps_image_import_proc(char *tenant_name, char *image_name, char *id, char **req_id, int id_len, char *vsmid)
{
    unsigned int ret = ERR_NONE;
    int i = 0, array_num = 0;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    int *id_val = NULL;
	char vsm_id[128] = {0};
	char vsm_ipv4[16] = {0};
	char vsm_ipv6[64] = {0};
	char host_ip[64] = {0};
	char request_id[16] = {0};
    char callback_ip[64] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char tenant[128] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    if (tenant_name == NULL || image_name == NULL || id == NULL)
        return ERR_PARAM;

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    DEBUG_CPS_CLI(COMMON_DEBUG, "vsm id:%s\n", id);
    //解析id数据转为整形数组
    id_val = parse_string_to_array(id, &array_num);

    //遍历虚拟机索引号，依次进行导入
    for (i = 0; i < array_num; i++) {
        //获取vsm_id
        memset(vsm_id, 0, sizeof(vsm_id));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&id_val[i], "vsm_id", vsm_id, sizeof(vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (vsmid) {
            if (strcmp(vsm_id, vsmid) == 0)
                continue ;
        }

        //获取虚拟机ipv4
        memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
        ret = cps_get_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id", vsm_id, "vsm_ipv4", vsm_ipv4, sizeof(vsm_ipv4), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //获取虚拟机ipv6
        memset(vsm_ipv6, 0, sizeof(vsm_ipv6));
        ret = cps_get_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id", vsm_id, "vsm_ipv6", vsm_ipv6, sizeof(vsm_ipv6), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //获取回调地址
        ret = cps_get_callback_addr(callback_ip, sizeof(callback_ip));
        API_CHECK_FUNC(ret, "cps_get_callback_addr");

        //生成8位随机数作为requestid
        ret = cps_generate_random(request_id, sizeof(request_id));
        API_CHECK_FUNC(ret, "cps_generate_random");
        if (req_id != NULL) {
            if ((req_id[i] != NULL) && (id_len > strlen(request_id)))
                strcpy(req_id[i], request_id);
        }

        //获取宿主机ip
        memset(host_ip, 0, sizeof(host_ip));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&id_val[i], "host_ip", host_ip, sizeof(host_ip), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //组创建虚拟机接口调用的json数据,即传入参数
        memset(data, 0, sizeof(data));
        ret = image_import_data_comp(request_id, image_name, vsm_id, callback_ip, data, sizeof(data));
        API_CHECK_FUNC(ret, "image_import_data_comp");
        data_len = strlen(data);

        //对数据进行base64编码
        data_base64 = (unsigned char *)realloc(data_base64, strlen(data) * 2);
        if (!data_base64) {
            ret = ERR_MALLOC;
            goto out;
        }
        memset(data_base64, 0, strlen(data) * 2);
        base64_encode(data, data_base64, data_len);

        //对json数据进行签名
        ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
        API_CHECK_FUNC(ret, "sign_with_internal_key");

        //组tcp传输body数据
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)realloc(msg_header, sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_IMPORT_IMAGE);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");

        //更新数据库
        ret = image_import_proc(image_name, vsm_ipv4, vsm_ipv6, tenant_name, request_id);
        API_CHECK_FUNC(ret, "image_import_proc");

        //更新虚拟机操作状态
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d,request_id=\'%s\' where vsm_id=\'%s\'", STATE_IMPORTING, request_id, vsm_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

out:
    if (msg_header) {
        free(msg_header);
        msg_header = NULL;
    }
    if (data_base64) {
        free(data_base64);
        data_base64 = NULL;
    }
    if (id_val) {
        free(id_val);
        id_val = NULL;
    }
    return ret;
}

int cps_image_del(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *tenant_name = argv[4];
    char *image_name = argv[6];
    char tenant[128] = {0};
    char image_file[256] = {0};

    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_IMAGE_EXPORT_TABLE, "real_image", image_name, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tanent name: %s %s", tenant, tenant_name);
        ret =  ERR_SUPP_OPT;
        goto out;
    }

    //删除本地文件
    memset(image_file, 0, sizeof(image_file));
    sprintf(image_file, "%s/%s", CPS_VSM_IMAGE_PATH, image_name);
    unlink(image_file);

    //更新数据库
    ret = image_del_proc(image_name);

out:
    print_errmsg(ret);
    return ret;
}
