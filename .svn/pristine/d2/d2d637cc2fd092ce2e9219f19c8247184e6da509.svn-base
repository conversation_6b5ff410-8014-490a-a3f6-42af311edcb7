#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "cjson.h"
#include "cps_common.h"
#include "cps_tenant.h"
#include "database_api.h"
#include "sqlite3.h"
#define DB_ACCOUNT_PATH "/usr/local/web/html/database/database.sqlite"
#define DB_FILE_PATH    "/usr/local/conf/cps.db"
static sqlite3* Db_Store = NULL;

extern int send_del_tenant_src_request(char* ip, char* tenant_id);
/**
 * @brief 打开数据库
 *
 * @return int 成功返回0
 */
int _Open_DbFile(void) {
    int   ret     = 0;
    char* err_msg = NULL;

    if (Db_Store == NULL) {
        if (sqlite3_open_v2(
                DB_FILE_PATH, &Db_Store, SQLITE_OPEN_FULLMUTEX | SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE, NULL) !=
            SQLITE_OK) {
            return -1;
        }
        ret = sqlite3_exec(
            Db_Store, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg);
        if (ret) {
            usleep(500000);
            if (ret = sqlite3_exec(
                    cps_db, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg)) {
                DEBUG_CPS_CLI(ERR_DEBUG, "synchronous set failed!(%s)", err_msg);
                sqlite3_free(err_msg);
            }
        }
    }
    return 0;
}

/**
 * @brief 关闭数据库
 *
 * @return int 成功返回0
 */
int _Close_DbFile(void) {
    if (Db_Store != NULL) {
        sqlite3_close(Db_Store);
        Db_Store = NULL;
    }
    return 0;
}

/**
 * @brief 获取设备的sn码
 *
 * @return int 成功返回0
 */
int Db_getDeviceSn(char* device_sn) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret    = 0;
    char *err_msg = NULL;
    sqlite3* Db_CHSM_Store = NULL;

    if (Db_CHSM_Store == NULL) {
        if (sqlite3_open_v2("/usr/local/conf/chsm.db", &Db_CHSM_Store, SQLITE_OPEN_FULLMUTEX | SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE, NULL) != SQLITE_OK) {
            DEBUG_CLI(ERR_DEBUG, "fail to open db file!");
            return -1;
        }
        rc = sqlite3_exec(Db_CHSM_Store, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg);
        if (ret) {
            usleep(500000);
            if (ret = sqlite3_exec(cps_db, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg)) {
                DEBUG_CPS_CLI(ERR_DEBUG, "synchronous set failed!(%s)", err_msg);
                sqlite3_free(err_msg);
            }
        }
    }

    rc = sqlite3_get_table_printf(Db_CHSM_Store, "select device_dn from device_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        if (errmsg) sqlite3_free(errmsg);
        DEBUG_CLI(ERR_DEBUG, "select db file failed!");
        return rc;
    }

    if (row > 0) {
        memcpy(device_sn, result[col], strlen(result[col]));
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (Db_CHSM_Store != NULL) {
        sqlite3_close(Db_CHSM_Store);
        Db_CHSM_Store = NULL;
    }
}

int Db_delete_table(char* table_name) {
    int   rc     = 0;
    char* errmsg = NULL;
    rc           = sqlite3_exec_printf(Db_Store, "delete from %s", 0, 0, &errmsg, table_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete table:%s!", table_name);
        return rc;
    }

    return rc;
}

int Db_reset_addr_info(void) {
    int   rc     = 0;
    char* errmsg = NULL;
    rc           = sqlite3_exec_printf(
        Db_Store, "UPDATE addr_info SET ip_family='',segment ='',ip_range='',ip_pool=''", 0, 0, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to reset addr_info!");
        return rc;
    }

    return rc;
}

int Db_reset_callback_addr(void) {
    int   rc     = 0;
    char* errmsg = NULL;
    rc           = sqlite3_exec_printf(Db_Store, "UPDATE callback_addr SET addr=''", 0, 0, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to reset callback_addr!");
        return rc;
    }

    return rc;
}

/**
 * @brief 删除所有表信息
 *
 * @return int 非0失败
 */
int Db_DeleteAllKey(void) {
    int ret = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    ret = Db_delete_table("device_status_info");
    ret = Db_delete_table("tenant_info");
    ret = Db_delete_table("work_order_info");
    ret = Db_delete_table("admin_info");
    ret = Db_delete_table("vsm_manage");
    ret = Db_delete_table("image_export");
    ret = Db_delete_table("image_import");
    ret = Db_delete_table("cluster_vsm");
    ret = Db_delete_table("cluster_host");
    ret = Db_delete_table("srv_manage");
    ret = Db_delete_table("flavor_info");
    ret = Db_reset_addr_info();
    ret = Db_reset_callback_addr();
    ret = Db_delete_table("ip_info");

    return ret;
}

/**
 * @brief 检测数据库中是否存在对应信息,查看对应sn号是否已经注册
 *
 * @param sn ukey序列号
 * @param name 用户名
 * @param type 用户类型
 * @return int 存在返回1,否则返回0
 */
int db_check_user_info(char* sn, char* name, int type, int tenant_id) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    // sn号已经被注册过
    rc = sqlite3_get_table_printf(
        Db_Store, "select * from admin_info where ukey_sn='%s'", &result, &row, &col, &errmsg, sn);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (row > 0) {
        printf(gettext("the sn has already registered!"));
        ret = 1;
        goto out;
    }

    // 用户名已经被注册过
    rc = sqlite3_get_table_printf(
        Db_Store,
        "select * from admin_info where tenant_id='%d' and manager_name='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        tenant_id,
        name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (row > 0) {
        printf(gettext("the name has already registered!"));
        ret = 1;
        goto out;
    }

out:

    return ret;
}

int db_get_manager_id(int type) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret            = -1;
    int    max_admin_num  = 0;
    int    max_opt_num    = 0;
    int    max_audit_num  = 0;
    int    max_tenant_num = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_count from ukey_manager_count where manager_type='0'", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (0 < row) {
        max_admin_num = atoi(result[col]);
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_count from ukey_manager_count where manager_type='1'", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (0 < row) {
        max_opt_num = atoi(result[col]);
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_count from ukey_manager_count where manager_type='2'", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (0 < row) {
        max_audit_num = atoi(result[col]);
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_count from ukey_manager_count where manager_type='3'", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (0 < row) {
        max_tenant_num = atoi(result[col]);
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_id from admin_info where manager_type='%d'", &result, &row, &col, &errmsg, type);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    switch (type) {
        case 0:
            if (row == max_admin_num) {
                goto out;
            }

            if (row == 0) {
                ret = 1;
            } else {
                int find_flag = 0;
                for (int i = 1; i <= max_admin_num; i++) {
                    find_flag = 0;
                    for (int j = 1; j <= row; j++) {
                        if (atoi(result[col * j]) == i) {
                            find_flag = 1;
                            break;
                        }
                    }

                    if (find_flag == 0) {
                        ret = i;
                        break;
                    }
                }
            }
            break;

        case 1:
            if (row == max_opt_num) {
                goto out;
            }

            if (row == 0) {
                ret = max_admin_num + 1;
            } else {
                int find_flag = 0;
                for (int i = max_admin_num + 1; i <= max_admin_num + max_opt_num; i++) {
                    find_flag = 0;
                    for (int j = 1; j <= row; j++) {
                        if (atoi(result[col * j]) == i) {
                            find_flag = 1;
                            break;
                        }
                    }

                    if (find_flag == 0) {
                        ret = i;
                        break;
                    }
                }
            }
            break;
        case 2:
            if (row == max_audit_num) {
                goto out;
            }

            if (row == 0) {
                ret = max_admin_num + max_opt_num + 1;
            } else {
                int find_flag = 0;
                for (int i = max_admin_num + max_opt_num + 1; i <= max_admin_num + max_opt_num + max_audit_num; i++) {
                    find_flag = 0;
                    for (int j = 1; j <= row; j++) {
                        if (atoi(result[col * j]) == i) {
                            find_flag = 1;
                            break;
                        }
                    }

                    if (find_flag == 0) {
                        ret = i;
                        break;
                    }
                }
            }
            break;
        case 3:
            if (row == max_tenant_num) {
                goto out;
            }

            if (row == 0) {
                ret = max_admin_num + max_opt_num + max_audit_num + 1;
            } else {
                int find_flag = 0;
                for (int i = max_admin_num + max_opt_num + +max_audit_num + 1;
                     i <= max_admin_num + max_opt_num + max_audit_num + max_tenant_num;
                     i++) {
                    find_flag = 0;
                    for (int j = 1; j <= row; j++) {
                        if (atoi(result[col * j]) == i) {
                            find_flag = 1;
                            break;
                        }
                    }

                    if (find_flag == 0) {
                        ret = i;
                        break;
                    }
                }
            }
            break;
        default:
            goto out;
    }
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    return ret;
}

int db_select_manager_id_and_sn(char* name, int telnat_id, int* type, int* id, char* sn) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select manager_type,manager_id,ukey_sn from admin_info where tenant_id='%d' and manager_name='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        telnat_id,
        name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }
    if (row > 0) {
        *type = atoi(result[col]);
        *id   = atoi(result[col + 1]);
        memset(sn, '\0', 16);
        sprintf(sn, "%s", result[col + 2]);
    } else {
        ret = -1;
        goto out;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;

out:

    return ret;
}

int db_delete_user(char* name, int type, int tenant_id) {
    int   rc     = 0;
    char* errmsg = NULL;
    int   ret    = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "delete from admin_info where tenant_id='%d' and manager_type='%d' and manager_name='%s'",
        0,
        0,
        &errmsg,
        tenant_id,
        type,
        name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
    }
    ret = 0;
out:

    return ret;
}

/**
 * @brief 往数据库中添加用户信息
 *
 * @param admin 用户信息结构体
 * @return int 成功返回0
 */
int db_add_user_info(USER_INFO_T* admin) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into admin_info (tenant_id,manager_type, manager_name, "
        "manager_id,ukey_sn,create_time,last_login, "
        "auth_info,login_status,login_ip,session_id,login_info,remark,login_type,pri_temp)"
        " values ('%d','%d', '%s', '%d','%s', '%s', '%s','%s','%d','%s','%s','%s','%s','%d','%s') ",
        0,
        0,
        &errmsg,
        admin->tenant_id,
        admin->type,
        admin->name,
        admin->id,
        admin->sn,
        admin->create_time,
        admin->last_login,
        admin->auth_info,
        admin->login_status,
        admin->login_ip,
        admin->session_id,
        admin->login_info,
        admin->remark,
        admin->login_type,
        admin->pri_temp);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

    return rc;
}

int db_check_user_name_and_sn(char* sn, char* name) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select manager_name from admin_info where ukey_sn='%s'", &result, &row, &col, &errmsg, sn);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }
    if (row > 0) {
        if (strcmp(name, result[col]) != 0) {
            printf(gettext("sn and name not corresponding!"));
            ret = -1;
        }
    } else {
        printf(gettext("sn not register!"));
        ret = -1;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_get_user_name_and_type(char* sn, char* name, int* type) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select manager_type,manager_name from admin_info where ukey_sn='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        sn);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }
    if (row > 0) {
        *type = atoi(result[col]);
        memset(name, '\0', 16);
        sprintf(name, "%s", result[col + 1]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;

out:

    return ret;
}

int db_get_user_type_and_id(char* sn, int* type, int* id) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select manager_type,manager_id from admin_info where ukey_sn='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        sn);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }
    if (row > 0) {
        *type = atoi(result[col]);
        *id   = atoi(result[col + 1]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;

out:

    return ret;
}

int db_set_login_info_log_out(char* name, char* log_info, int tenant_id) {
    int   rc     = 0;
    char* errmsg = NULL;
    int   row, col;
    int   ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE admin_info SET login_status='0',login_ip ='',session_id='',login_info='%s' where tenant_id='%d' and "
        "manager_name='%s'",
        0,
        0,
        &errmsg,
        log_info,
        tenant_id,
        name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to UPDATE!!");
        ret = -1;
        goto out;
    }

    ret = 0;
out:

    return ret;
}

int db_get_login_info(char* name, int tenant_id, char* log_info) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select login_info from admin_info where tenant_id='%d' and manager_name='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        tenant_id,
        name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (row > 0) {
        sprintf(log_info, "%s", result[col]);
        ret = 0;
    } else {
        DEBUG_CPS_CLI(ERR_DEBUG, "have not info!");
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_update_login_info(USER_INFO_T* admin) {
    int   rc     = 0;
    char* errmsg = NULL;
    int   row, col;
    int   ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE admin_info SET last_login='%s',login_status='%d',login_ip='%s',session_id='%s',login_info ='%s' where "
        "manager_name='%s' and ukey_sn='%s'",
        0,
        0,
        &errmsg,
        admin->last_login,
        admin->login_status,
        admin->login_ip,
        admin->session_id,
        admin->login_info,
        admin->name,
        admin->sn);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to UPDATE!!");
        ret = -1;
        goto out;
    }

    ret = 0;
out:

    return ret;
}

int db_update_login_random(char* random, long time) {
    db_del_data("login_random", "create_time < %ld", time - 300);
    return db_insert_data("login_random", "(random, create_time) values ('%s', '%ld')", random, time);
}

int db_update_check_random(char* random) {
    int i = db_get_num("login_random", "random ='%s'", random);
    if (i == 1) {
        db_del_data("login_random", "random ='%s'", random);
        return 0;
    } else {
        return -1;
    }
}

int db_get_manager_num(int* admin_num, int* opera_num, int* audit_num) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select count(*) from admin_info where manager_type=0", &result, &row, &col, &errmsg);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        ret = -1;
        goto out;
    }

    *admin_num = atoi(result[col]);

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select count(*) from admin_info where manager_type=1", &result, &row, &col, &errmsg);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        ret = -1;
        goto out;
    }

    *opera_num = atoi(result[col]);

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select count(*) from admin_info where manager_type=2", &result, &row, &col, &errmsg);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        ret = -1;
        goto out;
    }

    *audit_num = atoi(result[col]);

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    ret = 0;

out:

    return ret;
}

int db_get_manager_num_by_ip_and_type(char* ip, char* session_id, int type) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    count = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    // 未初始化完成不判断sessionid
    rc = sqlite3_get_table_printf(Db_Store, "select system_init_status from device_init", &result, &row, &col, &errmsg);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        count = -1;
        goto out;
    }

    if (atoi(result[col])) {
        rc = sqlite3_get_table_printf(
            Db_Store,
            "select count(*) from admin_info where manager_type='%d' and login_ip='%s' and session_id='%s'",
            &result,
            &row,
            &col,
            &errmsg,
            type,
            ip,
            session_id);
    } else {
        rc = sqlite3_get_table_printf(
            Db_Store,
            "select count(*) from admin_info where manager_type='%d' and login_ip='%s'",
            &result,
            &row,
            &col,
            &errmsg,
            type,
            ip);
    }

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        count = -1;
        goto out;
    }

    count = atoi(result[col]);

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return count;
}

int db_get_manager_num_with_login(int type) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    count = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select count(*) from admin_info where manager_type='%d' and login_status=1",
        &result,
        &row,
        &col,
        &errmsg,
        type);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!");
        count = -1;
        goto out;
    }

    count = atoi(result[col]);

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return count;
}

int db_set_set_user_remark(char* name, int login_type, char* pri_temp, int tenant_id, char* remark) {
    int   rc     = 0;
    char* errmsg = NULL;
    int   row, col;
    int   ret = -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE admin_info SET remark='%s',login_type='%d',pri_temp='%s' where "
        "tenant_id='%d' and manager_name='%s'",
        0,
        0,
        &errmsg,
        remark,
        login_type,
        pri_temp,
        tenant_id,
        name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to UPDATE!!");
        ret = -1;
        goto out;
    }

    ret = 0;
out:

    return ret;
}

int db_set_device_init_status(int status) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(Db_Store, "UPDATE device_init SET device_init_status='%d'", 0, 0, &errmsg, status);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
    }

    return rc;
}
int db_add_device(DEVICE_INFO_T device_info) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into device_status_info (device_name, device_type, "
        "ip,device_status,cpu_cores,cpu_usage,remain_cpu,memory_size,memory_usage,remain_mem,disk_size,disk_usage,"
        "remain_disk,create_vsm_num,max_vsm_num,remark)"
        " values ('%s','%d','%s', '%d', '%d','%f','%d','%d','%f','%d','%d','%f','%d','%d','%d','%s') ",
        0,
        0,
        &errmsg,
        device_info.device_name,
        device_info.device_type,
        device_info.ip,
        device_info.device_status,
        device_info.cpu_cores,
        device_info.cpu_usage,
        device_info.remain_cpu,
        device_info.memory_size,
        device_info.memory_usage,
        device_info.remain_mem,
        device_info.disk_size,
        device_info.disk_usage,
        device_info.remain_disk,
        device_info.create_vsm_num,
        device_info.max_vsm_num,
        device_info.remark);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

    return rc;
}

int db_set_device(DEVICE_INFO_T device_info) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE device_status_info SET "
        "device_status='%d',cpu_cores='%d',cpu_usage='%f',remain_cpu='%d',memory_size='%d',memory_usage='%f',remain_"
        "mem='%d',disk_size='%d',disk_usage='%f',remain_disk = '%d',create_vsm_num='%d',max_vsm_num='%d' where ip = "
        "'%s'",
        0,
        0,
        &errmsg,
        device_info.device_status,
        device_info.cpu_cores,
        device_info.cpu_usage,
        device_info.remain_cpu,
        device_info.memory_size,
        device_info.memory_usage,
        device_info.remain_mem,
        device_info.disk_size,
        device_info.disk_usage,
        device_info.remain_disk,
        device_info.create_vsm_num,
        device_info.max_vsm_num,
        device_info.ip);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
    }

    return rc;
}

int db_check_device_ip_and_name(char* name, char* ip) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select * from device_status_info where device_name='%s' or ip='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        name,
        ip);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (row > 0) {
        ret = 1;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_set_pub_key_fingp(char* ip, char* fingp) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store, "UPDATE device_status_info SET pub_fingerprint='%s' where ip='%s'", 0, 0, &errmsg, fingp, ip);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
    }

    return rc;
}

int mod_addr_info(char* subnet_str) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret                    = -1;
    char   new_all_ip_range[1024] = {0};
    char   new_all_subnet[512]    = {0};
    char   new_all_gateway[512]   = {0};

    char ip_range[10][128] = {0};
    char subnet[10][64]    = {0};
    char gateway[10][64]   = {0};

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select ip_range,subnet,gateway,tenant_name from addr_info where subnet like '%%%s%%'",
        &result,
        &row,
        &col,
        &errmsg,
        subnet);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            memset(new_all_ip_range, 0, 1024);
            memset(new_all_subnet, 0, 512);
            memset(new_all_gateway, 0, 512);
            memset(ip_range, 0, 10 * 128);
            memset(subnet, 0, 10 * 64);
            memset(gateway, 0, 10 * 64);

            sscanf(
                result[col * j],
                "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,]",
                ip_range[0],
                ip_range[1],
                ip_range[2],
                ip_range[3],
                ip_range[4],
                ip_range[5],
                ip_range[6],
                ip_range[7],
                ip_range[8],
                ip_range[9]);
            sscanf(
                result[col * j + 1],
                "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,]",
                subnet[0],
                subnet[1],
                subnet[2],
                subnet[3],
                subnet[4],
                subnet[5],
                subnet[6],
                subnet[7],
                subnet[8],
                subnet[9]);
            sscanf(
                result[col * j + 2],
                "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,]",
                gateway[0],
                gateway[1],
                gateway[2],
                gateway[3],
                gateway[4],
                gateway[5],
                gateway[6],
                gateway[7],
                gateway[8],
                gateway[9]);

            int first_flag = 0;
            for (int i = 0; i < 10; i++) {
                if (strcmp(subnet_str, subnet[i]) != 0 && strlen(subnet[i]) != 0) {
                    if (first_flag == 1) {
                        sprintf(new_all_ip_range + strlen(new_all_ip_range), ",%s", ip_range[i]);
                        sprintf(new_all_subnet + strlen(new_all_subnet), ",%s", subnet[i]);
                        sprintf(new_all_gateway + strlen(new_all_gateway), ",%s", gateway[i]);
                    } else {
                        sprintf(new_all_ip_range + strlen(new_all_ip_range), "%s", ip_range[i]);
                        sprintf(new_all_subnet + strlen(new_all_subnet), "%s", subnet[i]);
                        sprintf(new_all_gateway + strlen(new_all_gateway), "%s", gateway[i]);
                        first_flag = 1;
                    }
                }
            }

            if (strlen(new_all_subnet) == 0) {
                // 删除当前地址信息
                sqlite3_exec_printf(
                    Db_Store, "delete from addr_info where tenant_name='%s'", 0, 0, &errmsg, result[col * j + 3]);
            } else {
                // 更新当前地址信息
                sqlite3_exec_printf(
                    Db_Store,
                    "UPDATE addr_info SET ip_range='%s',subnet='%s',gateway='%s' where tenant_name='%s'",
                    0,
                    0,
                    &errmsg,
                    new_all_ip_range,
                    new_all_subnet,
                    new_all_gateway,
                    result[col * j + 3]);
            }
        }
    }
    ret = 0;
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    return ret;
}

int db_delete_device(char* name) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    char** result1 = NULL;
    int    row1, col1;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }
    // 调整工单数据
    // rc = sqlite3_exec_printf(Db_Store, "delete from work_order_info where host_name='%s'", 0, 0, &errmsg, name);
    // if (rc != SQLITE_OK) {
    //     DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete work_order_info!");
    //     goto out;
    // }

    // 调整地址池数据
    rc = sqlite3_get_table_printf(Db_Store, "select subnet from interface_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (row > 0) {
        /*查找接口信息中,其他设备是否存在相同子网,相同不做处理*/
        for (int j = 1; j <= row; j++) {
            rc = sqlite3_get_table_printf(
                Db_Store,
                "select subnet from interface_info where device_name!='%s' and subnet='%s'",
                &result1,
                &row1,
                &col1,
                &errmsg,
                name,
                result[col * j]);
            if (rc != SQLITE_OK) {
                DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
                goto out;
            }

            if (row1 <= 0) {
                mod_addr_info(result[col * j]);
            }
        }
    }

    // 删除设备信息
    rc = sqlite3_exec_printf(Db_Store, "delete from device_status_info where device_name='%s'", 0, 0, &errmsg, name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete device_status_info!");
        goto out;
    }

    // 删除设备桥接口信息
    rc = sqlite3_exec_printf(Db_Store, "delete from interface_info where device_name='%s'", 0, 0, &errmsg, name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete interface_info!");
        goto out;
    }

    ret = 0;
out:

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (result1) {
        sqlite3_free_table(result1);
        result1 = NULL;
    }

    return ret;
}

int db_set_flavor_info(FLAVOR_INFO_T flavor_info) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into flavor_info (flavor, cpu,mem,disk)"
        " values ('%d','%d','%d', '%d') ",
        0,
        0,
        &errmsg,
        flavor_info.flavor,
        flavor_info.cpu,
        flavor_info.mem,
        flavor_info.disk);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

    return rc;
}

int db_get_device_init_status(void) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(Db_Store, "select * from device_init", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (row > 0) {
        ret = atoi(result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_get_internal_algid(int stand_index) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select internal_alg_id from alg_id_map where standard_alg_id='%d'",
        &result,
        &row,
        &col,
        &errmsg,
        stand_index);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        ret = atoi(result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

/*策略管理*/
long str_ascll(char* str) {
    char* p           = NULL;
    char* p1          = NULL;
    int   len         = 0;
    char  str_num[32] = {0};
    p                 = str;

    p = strstr(p, ".");
    p = p + 1;

    p1 = p;

    while (p) {
        p1 = strstr(p, ".");
        if (p1 == NULL) {
            memcpy(str_num + len, p, strlen(p));
            break;
        } else {
            memcpy(str_num + len, p, p1 - p);
        }
        len += (p1 - p);
        p = p1 + 1;
    }

    return atol(str_num);
}

int _check_ipv4(char* start_ip, char* end_ip, char* ip_range) {
    char* range_m     = strstr(ip_range, "-");
    char  range_s[32] = {0};
    char  range_e[32] = {0};
    snprintf(range_s, range_m - ip_range + 1, "%s", ip_range);
    sprintf(range_e, "%s", range_m + 1);

    long star_num = str_ascll(start_ip);
    long end_num  = str_ascll(end_ip);
    long rs_num   = str_ascll(range_s);
    long re_num   = str_ascll(range_e);

    if (rs_num <= end_num && end_num <= re_num) {
        DEBUG_CPS_CLI(ERR_DEBUG, "end_ip in range!");
        return 1;
    }

    if (rs_num <= star_num && star_num <= re_num) {
        DEBUG_CPS_CLI(ERR_DEBUG, "start_ip in range!");
        return 1;
    }

    if (star_num < rs_num && re_num < end_num) {
        DEBUG_CPS_CLI(ERR_DEBUG, "include range!");
        return 1;
    }

    return 0;
}

int _check_ipv6(char* start_ip, char* end_ip, char* ip_range) {
    char* range_m      = strstr(ip_range, "-");
    char  range_s[128] = {0};
    char  range_e[128] = {0};
    snprintf(range_s, range_m - ip_range + 1, "%s", ip_range);
    sprintf(range_e, "%s", range_m + 1);
    char star_num[8][5] = {0};
    char end_num[8][5]  = {0};
    char rs_num[8][5]   = {0};
    char re_num[8][5]   = {0};

    string_replace(start_ip, start_ip, "::", ":");
    string_replace(end_ip, end_ip, "::", ":");
    string_replace(range_s, range_s, "::", ":");
    string_replace(range_e, range_e, "::", ":");

    sscanf(
        start_ip,
        "%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]",
        star_num[0],
        star_num[1],
        star_num[2],
        star_num[3],
        star_num[4],
        star_num[5],
        star_num[6],
        star_num[7]);
    sscanf(
        end_ip,
        "%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]",
        end_num[0],
        end_num[1],
        end_num[2],
        end_num[3],
        end_num[4],
        end_num[5],
        end_num[6],
        end_num[7]);
    sscanf(
        range_s,
        "%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]",
        rs_num[0],
        rs_num[1],
        rs_num[2],
        rs_num[3],
        rs_num[4],
        rs_num[5],
        rs_num[6],
        rs_num[7]);
    sscanf(
        range_e,
        "%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]:%[^:]",
        re_num[0],
        re_num[1],
        re_num[2],
        re_num[3],
        re_num[4],
        re_num[5],
        re_num[6],
        re_num[7]);

    for (size_t k = 0; k < 8; k++) {
        int rs_num_len   = strlen(rs_num[k]);
        int end_num_len  = strlen(end_num[k]);
        int re_num_len   = strlen(re_num[k]);
        int star_num_len = strlen(star_num[k]);

        if (rs_num_len <= end_num_len && end_num_len <= re_num_len) {
            if (strcmp(rs_num[k], end_num[k]) < 0 && strcmp(end_num[k], re_num[k]) < 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "end_ip in range!");
                return 1;
            }
        }

        if (rs_num_len <= star_num_len && star_num_len <= re_num_len) {
            if (strcmp(rs_num[k], star_num[k]) < 0 && strcmp(star_num[k], re_num[k]) < 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "start_ip in range!");
                return 1;
            }
        }

        if (star_num_len <= rs_num_len && re_num_len <= end_num_len) {
            if (strcmp(star_num[k], rs_num[k]) < 0 && strcmp(re_num[k], end_num[k]) < 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "include range!");
                return 1;
            }
        }
    }

    if (strcmp(start_ip, range_s) == 0 || strcmp(start_ip, range_e) == 0 || strcmp(end_ip, range_s) == 0 ||
        strcmp(end_ip, range_e) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "start_ip or end_ip is the same!");
        return 1;
    }

    return 0;
}

int _check_local_addr(char* start_ip, char* end_ip, char* subnet, char* tenant_name) {
    // 查询地址信息表中相同网段的数据
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret            = -1;
    int    ma             = 0;
    char*  p              = NULL;
    char*  p1             = NULL;
    char   ip_range[256]  = {0};
    int    ip_type        = 0;
    char   subnet_sub[64] = {0};

    if (strstr(subnet, ":") != NULL) {
        ip_type = 1;
    }

    snprintf(subnet_sub, strlen(subnet) - 1, "%s", subnet);

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select ip_range from addr_info where tenant_name!='%s' and subnet like '%%%s%%'",
        &result,
        &row,
        &col,
        &errmsg,
        tenant_name,
        subnet);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        ret = 2;
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            p = result[col * j];
            while (p != NULL) {
                memset(ip_range, 0, 128);
                sscanf(p, "%[^,]", ip_range);
                if (strstr(ip_range, subnet_sub) != NULL) {
                    if (ip_type == 1 && strstr(ip_range, ":") != NULL) {
                        if (1 == _check_ipv6(start_ip, end_ip, ip_range)) {
                            printf(gettext("IP conflict:%s-%s and %s !"), start_ip, end_ip, ip_range);
                            ret = 1;
                            goto out;
                        }
                    }

                    if (ip_type == 0 && strstr(ip_range, ".") != NULL) {
                        if (1 == _check_ipv4(start_ip, end_ip, ip_range)) {
                            printf(gettext("IP conflict:%s-%s and %s !"), start_ip, end_ip, ip_range);
                            ret = 1;
                            goto out;
                        }
                    }
                }

                p1 = p;
                p1 = strstr(p1 + 1, ",");
                if (p1) {
                    p = p1 + 1;
                } else {
                    p = p1;
                }
            }
        }
    }

    ret = 0;
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    return ret;
}

int _get_char_num(char* str, char ch) {
    int num = 0;
    int len = strlen(str);
    for (size_t k = 0; k < len; k++) {
        if (ch == str[k]) {
            num++;
        }
    }

    return num;
}

int db_check_addr_info(char* ip_range, char* subnet, char* tenant_name) {
    int  ret                  = 0;
    char ip_range_se[10][128] = {0};
    char subnet_se[10][64]    = {0};
    char start_ip[128]        = {0};
    char end_ip[128]          = {0};
    int  ip_type              = 0;

    int ip_range_num = _get_char_num(subnet, ',');

    sscanf(
        ip_range,
        "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,]",
        ip_range_se[0],
        ip_range_se[1],
        ip_range_se[2],
        ip_range_se[3],
        ip_range_se[4],
        ip_range_se[5],
        ip_range_se[6],
        ip_range_se[7],
        ip_range_se[8],
        ip_range_se[9]);

    sscanf(
        subnet,
        "%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,],%[^,]",
        subnet_se[0],
        subnet_se[1],
        subnet_se[2],
        subnet_se[3],
        subnet_se[4],
        subnet_se[5],
        subnet_se[6],
        subnet_se[7],
        subnet_se[8],
        subnet_se[9]);

    // 检测ip_range中内部是否有ip段冲突
    for (size_t k = 0; k <= ip_range_num; k++) {
        for (int i = k + 1; i <= ip_range_num; i++) {
            if (strcmp(subnet_se[k], subnet_se[i]) == 0) {
                // 两个ip段子网相同,判断是否冲突
                char* range_m = strstr(ip_range_se[k], "-");
                snprintf(start_ip, range_m - ip_range_se[k] + 1, "%s", ip_range_se[k]);
                sprintf(end_ip, "%s", range_m + 1);

                if (strstr(subnet_se[k], ":") != NULL) {
                    if (1 == _check_ipv6(start_ip, end_ip, ip_range_se[i])) {
                        printf(gettext("IP conflict:%s and %s !"), ip_range_se[k], ip_range_se[i]);
                        ret = 1;
                        goto out;
                    }
                } else {
                    if (1 == _check_ipv4(start_ip, end_ip, ip_range_se[i])) {
                        printf(gettext("IP conflict:%s and %s !"), ip_range_se[k], ip_range_se[i]);
                        ret = 1;
                        goto out;
                    }
                }
            }
        }
    }

    // 检测ip_range中是否与已经分配的ip段冲突
    for (size_t k = 0; k <= ip_range_num; k++) {
        char* range_m = strstr(ip_range_se[k], "-");
        snprintf(start_ip, range_m - ip_range_se[k] + 1, "%s", ip_range_se[k]);
        sprintf(end_ip, "%s", range_m + 1);
        ret = _check_local_addr(start_ip, end_ip, subnet_se[k], tenant_name);
        if (ret) {
            goto out;
        }
    }
out:
    return ret;
}

int db_set_addr_image(char* url, char* err_msg, int (*cb)(char* ip, char* signature, char* url)) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret       = -1;
    int    fail_flag = 0;
    int    len       = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    // 查询所有宿主机的ip与公钥指纹
    rc = sqlite3_get_table_printf(
        Db_Store, "select ip,pub_fingerprint from device_status_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            if (cb) {
                ret = cb(result[col * j], result[col * j + 1], url);
                if (ret == 0) {
                } else {
                    fail_flag = 1;
                    if (err_msg) {
                        sprintf(err_msg + len, " %s", result[col * j]);
                        len = strlen(err_msg);
                    }
                }
            }
        }
    }

    if (!fail_flag) {
        rc = sqlite3_exec_printf(Db_Store, "UPDATE image_addr SET upload_url='%s'", 0, 0, &errmsg, url);
    }

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
    }
    if (fail_flag) {
        ret = -1;
    } else {
        ret = 0;
    }
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    return ret;
}

int db_check_cb_addr(char* ip, char* err_msg, int (*cb)(char* ip, char* cb_ip)) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret       = -1;
    int    fail_flag = 0;
    int    len       = 0;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    // 查询所有宿主机的ip与公钥指纹
    rc = sqlite3_get_table_printf(Db_Store, "select ip from device_status_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            if (cb) {
                ret = cb(result[col * j], ip);
                if (ret == 0) {
                } else {
                    fail_flag = 1;
                    if (err_msg) {
                        sprintf(err_msg + len, " %s", result[col * j]);
                        len = strlen(err_msg);
                    }
                }
            }
        }
    }

    if (fail_flag) {
        ret = -1;
    } else {
        ret = 0;
    }
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    return ret;
}

/*租户管理*/
int _get_account_info_by_session_ip(char* session_id, char* ip, char* out_account) {
    int      rc     = 0;
    char*    errmsg = NULL;
    char**   result = NULL;
    int      row, col;
    int      ret    = -1;
    sqlite3* Db_web = NULL;

    if (sqlite3_open("/usr/local/web/html/database/database.sqlite", &Db_web) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "open db file failed!");
        return ret;
    }

    rc = sqlite3_get_table_printf(
        Db_web,
        "select account from online_users where s_id='%s' and ip='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        session_id,
        ip);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(out_account, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;
out:
    if (Db_web != NULL) {
        sqlite3_close(Db_web);
        Db_web = NULL;
    }
    return ret;
}

// 通过session与ip获取租户id
int db_get_tenant_id_by_session_ip(char* session_id, char* ip) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret          = -1;
    char   account[128] = {0};

    // 1.通过session_id与ip查询账户信息(系统账户)
    if (_get_account_info_by_session_ip(session_id, ip, account)) {
        goto out;
    }

    // 2.通过账户信息查闸租户信息,并获取租户id
    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select tenant_id from tenant_info where account='%s'", &result, &row, &col, &errmsg, account);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        ret = atoi(result[col]);
    } else {
        ret = 0;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_get_tenant_name(int tenant_id, char* tenant_name) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;

    // 2.通过账户信息查闸租户信息,并获取租户id
    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select name from tenant_info where tenant_id='%d'", &result, &row, &col, &errmsg, tenant_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_name, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;
out:

    return ret;
}

int db_tenant_add(TENANT_INFO_T* tenant) {
    int   rc     = 0;
    char* errmsg = NULL;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into tenant_info (name, account,valid_time,corporate_name,telephone,phone_number,mail,regs_date,remark)"
        " values ('%s', '%s','%ld', '%s', '%s','%s','%s','%s','%s') ",
        0,
        0,
        &errmsg,
        tenant->name,
        tenant->account,
        tenant->valid_time,
        tenant->corporate_name,
        tenant->telephone,
        tenant->phone_number,
        tenant->mail,
        tenant->regs_date,
        tenant->remark);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add! %s", errmsg);
        goto out;
    }

    // 往管理员表中写入不完整数据
    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into admin_info (tenant_id, manager_type, "
        "manager_name,manager_id,login_status)"
        " values ('1000', '3','%s','50','0')",
        0,
        0,
        &errmsg,
        tenant->name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

out:

    return rc;
}

int db_tenant_reset_bind_key(char* tenant_name) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        return -1;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select tenant_id from tenant_info where name='%s'", &result, &row, &col, &errmsg, tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    // 删除所有当前租户的管理员
    rc = sqlite3_exec_printf(
        Db_Store,
        "delete from admin_info where tenant_id='%s' or manager_name='%s'",
        0,
        0,
        &errmsg,
        result[col],
        tenant_name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // 往管理员表中写入不完整数据
    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into admin_info (tenant_id, manager_type, "
        "manager_name,manager_id,login_status)"
        " values ('1000', '3','%s','50','0')",
        0,
        0,
        &errmsg,
        tenant_name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

out:

    return rc;
}

int db_tenant_set(TENANT_INFO_T* tenant) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret           = -1;
    int    first_flag    = 1;
    char   sql_str[1024] = "update tenant_info set ";
    int    len           = strlen(sql_str);

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    if (tenant->valid_time != -1) {
        sprintf(sql_str + len, "valid_time='%ld'", tenant->valid_time);
        first_flag = 0;
        len        = strlen(sql_str);
    }

    if (tenant->corporate_name != NULL) {
        if (first_flag == 1) {
            sprintf(sql_str + len, "corporate_name='%s'", tenant->corporate_name);
            first_flag = 0;
        } else {
            sprintf(sql_str + len, ",corporate_name='%s'", tenant->corporate_name);
        }

        len = strlen(sql_str);
    }

    if (tenant->telephone != NULL) {
        if (first_flag == 1) {
            sprintf(sql_str + len, "telephone='%s'", tenant->telephone);
            first_flag = 0;
        } else {
            sprintf(sql_str + len, ",telephone='%s'", tenant->telephone);
        }
        len = strlen(sql_str);
    }

    if (tenant->phone_number != NULL) {
        if (first_flag == 1) {
            sprintf(sql_str + len, "phone_number='%s'", tenant->phone_number);
            first_flag = 0;
        } else {
            sprintf(sql_str + len, ",phone_number='%s'", tenant->phone_number);
        }
        len = strlen(sql_str);
    }

    if (tenant->mail != NULL) {
        if (first_flag == 1) {
            sprintf(sql_str + len, "mail='%s'", tenant->mail);
            first_flag = 0;
        } else {
            sprintf(sql_str + len, ",mail='%s'", tenant->mail);
        }
        len = strlen(sql_str);
    }

    if (tenant->remark != NULL) {
        if (first_flag == 1) {
            sprintf(sql_str + len, "remark='%s'", tenant->remark);
            first_flag = 0;
        } else {
            sprintf(sql_str + len, ",remark='%s'", tenant->remark);
        }
        len = strlen(sql_str);
    }

    sprintf(sql_str + len, " where name='%s'", tenant->name);

    // 更新工单信息
    rc = sqlite3_exec_printf(Db_Store, "%s", 0, 0, &errmsg, sql_str);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
        goto out;
    }

    ret = 0;
out:

    return ret;
}

int _retrieve_pool(int index, char* out_pool) {
    char* have_beside = NULL;
    char  pool[512]   = {0};
    char  pool1[512]  = {0};
    sprintf(pool, "%s", out_pool);
    sprintf(pool1, "%s", out_pool);
    int len = 0;

    int   first_num, last_num = 0;
    char* tmp = strtok(pool, ",");
    char* p   = strstr(pool1, ",");
    if (p == NULL) {
        p = "";
    }

    while (tmp) {
        have_beside = strstr(tmp, "-");
        if (have_beside != NULL) {
            first_num = atoi(tmp);
            last_num  = atoi(have_beside + 1);

            if (last_num == index - 1) {
                if (index + 1 == atoi(p + 1)) {
                    char* p1 = p;
                    p        = strstr(p, "-");
                    if (p == NULL) {
                        sprintf(out_pool + len, "%d-%s", first_num, p1 + 1);
                    } else {
                        sprintf(out_pool + len, "%d%s", first_num, p);
                    }
                } else {
                    sprintf(out_pool + len, "%d-%d%s", first_num, index, p);
                }
                goto out;
            }

            if (index == first_num - 1) {
                sprintf(out_pool + len, "%d-%d%s", index, last_num, p);
                goto out;
            }

            if (index < first_num) {
                sprintf(out_pool + len, "%d,%s", index, p);
                goto out;
            } else {
                sprintf(out_pool + len, "%d-", first_num);
                len = strlen(out_pool);
            }

            for (int j = first_num; j <= last_num; j++) {
                if (index < j) {
                    sprintf(out_pool + len, "%d,%d,%s", index, j, p);
                    goto out;
                }
            }

            sprintf(out_pool + len, "%d,", last_num);
            len = strlen(out_pool);
        } else {
            int num = atoi(tmp);
            if (index == num - 1) {
                sprintf(out_pool + len, "%d-%d%s", index, num, p);
                goto out;
            }

            if (index == num + 1) {
                if (index + 1 == atoi(p + 1)) {
                    char* p1 = p;
                    p        = strstr(p, "-");
                    if (p == NULL) {
                        sprintf(out_pool + len, "%d-%s", num, p1 + 1);
                    } else {
                        sprintf(out_pool + len, "%d%s", num, p);
                    }
                } else {
                    sprintf(out_pool + len, "%d-%d%s", num, index, p);
                }
                goto out;
            }

            if (index < num) {
                sprintf(out_pool + len, "%d,%d%s", index, num, p);
                goto out;
            } else {
                sprintf(out_pool + len, "%d,", num);
                len = strlen(out_pool);
            }
        }
        tmp = strtok(NULL, ",");
        p   = strstr(p + 1, ",");
        if (p == NULL) {
            p = "";
        }
    }

    sprintf(out_pool + len, "%d", index);
out:
    return 0;
}

int db_tenant_del(int tenant_id) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret              = -1;
    char   tenant_name[128] = {0};
    char   account[128]     = {0};
    char   out_pool[512]    = {0};
    char   system_str[1024] = {0};

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select name,account from tenant_info where tenant_id='%d'", &result, &row, &col, &errmsg, tenant_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_name, "%s", result[col]);
        sprintf(account, "%s", result[col + 1]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_exec_printf(
        Db_Store,
        "delete from admin_info where tenant_id='%d' or (tenant_id='1000' and manager_name='%s')",
        0,
        0,
        &errmsg,
        tenant_id,
        tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
        goto out;
    }

    // rc = sqlite3_get_table_printf(
    //     Db_Store,
    //     "select host_ip from work_order_info where tenant_name='%s'",
    //     &result,
    //     &row,
    //     &col,
    //     &errmsg,
    //     tenant_name);
    // if (rc != SQLITE_OK) {
    //     DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
    //     goto out;
    // }

    // if (row > 0) {
    //     char tenant_id_str[16] = {0};
    //     sprintf(tenant_id_str, "%d", tenant_id);
    //     for (int j = 1; j <= row; j++) {
    //         send_del_tenant_src_request(result[j], tenant_id_str);
    //     }
    // }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_exec_printf(
        Db_Store, "delete from  work_order_info where tenant_name='%s'", 0, 0, &errmsg, tenant_name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
        ret = -1;
        goto out;
    }

    rc = sqlite3_exec_printf(Db_Store, "delete from  addr_info where tenant_name='%s'", 0, 0, &errmsg, tenant_name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
        ret = -1;
        goto out;
    }

    rc = sqlite3_exec_printf(Db_Store, "delete from  ip_info where tenant_name='%s'", 0, 0, &errmsg, tenant_name);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
        ret = -1;
        goto out;
    }

    rc = sqlite3_exec_printf(Db_Store, "delete from tenant_info where tenant_id='%d'", 0, 0, &errmsg, tenant_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete!!");
        goto out;
    }

    ret = cps_tenant_info_del_all(tenant_name);
    if (ret != ERR_NONE) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to delete tenant info!");
        goto out;
    }

    sprintf(system_str, "/usr/local/bin/admacct hide name '%s' > /dev/null 2>&1 &", account);
    system(system_str);
    ret = 0;
out:

    return ret;
}

int db_check_tenant_src(int tenant_id) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret              = 0;
    char   tenant_name[128] = {0};

    if (_Open_DbFile() == -1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to open db file!!");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select name from tenant_info where tenant_id='%d'", &result, &row, &col, &errmsg, tenant_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_name, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select * from vsm_manage where tenant_name='%s'", &result, &row, &col, &errmsg, tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        ret = 1;
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return ret;
}

int db_tenant_on_off(void) {
    int      rc               = 0;
    char*    errmsg           = NULL;
    int      ret              = 0;
    char     tenant_name[128] = {0};
    sqlite3* Db_Config        = NULL;
    char**   result           = NULL;
    int      row, col;
    time_t   now = 0;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    if (sqlite3_open("/usr/local/conf/configs.db", &Db_Config) != SQLITE_OK) {
        return -1;
    }

    // 获取当前时间戳
    now = time(0);

    // 查询所有租户的有效时间
    rc = sqlite3_get_table_printf(Db_Store, "select account,valid_time from tenant_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            if (atoi(result[col * j + 1]) > now || atoi(result[col * j + 1]) == 0) {
                rc = sqlite3_exec_printf(
                    Db_Config,
                    "UPDATE accounts SET "
                    "employed=1 where account='%s'",
                    0,
                    0,
                    &errmsg,
                    result[col * j]);
            } else {
                rc = sqlite3_exec_printf(
                    Db_Config,
                    "UPDATE accounts SET "
                    "employed=0 where account='%s'",
                    0,
                    0,
                    &errmsg,
                    result[col * j]);
            }

            if (rc != SQLITE_OK) {
                DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
            }
        }
    }

out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (Db_Config != NULL) {
        sqlite3_close(Db_Store);
        Db_Config = NULL;
    }
    return ret;
}

/*工单管理*/
int _check_vsm(char* host_name, int vsm_cpu, int vsm_mem) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret              = -1;
    int    remain_cpu       = 0;
    int    remain_mem       = 0;
    int    remain_disk      = 0;
    int    allocate_vsm_cpu = 0;
    int    allocate_vsm_mem = 0;

    // 查询设备,剩余资源数
    rc = sqlite3_get_table_printf(
        Db_Store,
        "select cpu_cores,memory_size,disk_size from device_status_info where device_name='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        host_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        remain_cpu  = atoi(result[col]);
        remain_mem  = atoi(result[col + 1]);
        remain_disk = atoi(result[col + 2]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // 查看当前宿主机已经被申请了多少资源
    rc = sqlite3_get_table_printf(
        Db_Store,
        "select allocate_vsm_cpu,allocate_vsm_mem from work_order_info where host_name='%s' and "
        "approval_status='1'",
        &result,
        &row,
        &col,
        &errmsg,
        host_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            allocate_vsm_cpu += atoi(result[col * j]);
            allocate_vsm_mem += atoi(result[col * j + 1]);
        }
    }

    remain_cpu -= allocate_vsm_cpu;
    remain_mem -= allocate_vsm_mem;
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // 判断资源是否足够
    if (remain_cpu < vsm_cpu || remain_mem < vsm_mem) {
        return 1;
    } else {
        return 0;
    }

out:
    return ret;
}

int db_work_order_check_vsm(char* host_name, int vsm_cpu, int vsm_mem) {
    int ret = -1;
    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    ret = _check_vsm(host_name, vsm_cpu, vsm_mem);

out:

    return ret;
}

int db_work_order_get_tenant_src(char* host_name, char* tenant_name, int* allocate_vsm_cpu, int* allocate_vsm_mem) {
    int    ret    = -1;
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    // 查看当前宿主机已经被申请了多少资源
    rc = sqlite3_get_table_printf(
        Db_Store,
        "select allocate_vsm_cpu,allocate_vsm_mem from work_order_info where host_name='%s' and tenant_name='%s' "
        "and "
        "approval_status='1'",
        &result,
        &row,
        &col,
        &errmsg,
        host_name,
        tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            *allocate_vsm_cpu += atoi(result[col * j]);
            *allocate_vsm_mem += atoi(result[col * j + 1]);
        }
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = 0;
out:
    return ret;
}

int db_work_order_get_tenant(int wo_index, char* tenant_name, char* tenant_id) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret = -1;
    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select tenant_name from work_order_info where wo_index='%d'",
        &result,
        &row,
        &col,
        &errmsg,
        wo_index);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_name, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select tenant_id from tenant_info where name='%s'", &result, &row, &col, &errmsg, tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_id, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    ret = 0;
out:

    return ret;
}

int db_work_order_invalid(char* tenant_name) {
    int    rc            = 0;
    char*  errmsg        = NULL;
    char** result        = NULL;
    char   tenant_id[16] = {0};
    int    row, col;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store, "select tenant_id from tenant_info where name='%s'", &result, &row, &col, &errmsg, tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        sprintf(tenant_id, "%s", result[col]);
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select ip_addr from work_order_info where tenant_name='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(
            ERR_DEBUG,
            "sql:select ip_addr from work_order_info where tenant_name='%s' select db file failed!",
            tenant_name);
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            send_del_tenant_src_request(result[j], tenant_id);
        }
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // 作废工单
    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE work_order_info SET "
        "approval_status='3' where approval_status=1 and tenant_name = '%s'",
        0,
        0,
        &errmsg,
        tenant_name);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
        goto out;
    }

out:

    return rc;
}

int db_work_order_update_ip_info(char* tenant_name, char* vsm_id) {
    int    rc            = 0;
    char*  errmsg        = NULL;
    char** result        = NULL;
    char   tenant_id[16] = {0};
    int    row, col;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db_Store,
        "select vsm_ipv4,vsm_ipv6 from vsm_manage  where instr(','||'%s'||',',','||vsm_id||',') >0",
        &result,
        &row,
        &col,
        &errmsg,
        vsm_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        for (int j = 1; j <= row; j++) {
            if (strlen(result[col * j]) != 0) {
                rc = sqlite3_exec_printf(
                    Db_Store,
                    "insert into ip_info (use_status,tenant_name,ip,use)"
                    " values ('1', '%s', '%s','0') ",
                    0,
                    0,
                    &errmsg,
                    tenant_name,
                    result[col * j]);
            }

            if (strlen(result[col * j + 1]) != 0) {
                rc = sqlite3_exec_printf(
                    Db_Store,
                    "insert into ip_info (use_status,tenant_name,ip,use)"
                    " values ('1', '%s', '%s','0') ",
                    0,
                    0,
                    &errmsg,
                    tenant_name,
                    result[col * j + 1]);
            }
        }
    }
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

out:

    return rc;
}

int db_set_work_order_approval(
    int   wo_index,
    int   opinion,
    char* session_id,
    char* annotations,
    char* time_str,
    int   vsm_num) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    ret             = -1;
    char   approver[256]   = {0};
    int    approval_status = 0;

    if (_Open_DbFile() == -1) {
        printf("fail to open db file!\n");
        goto out;
    }

    // 通过sessionid获取审批人
    rc = sqlite3_get_table_printf(
        Db_Store,
        "select manager_name from admin_info where tenant_id='0' and session_id='%s' and manager_type='1'",
        &result,
        &row,
        &col,
        &errmsg,
        session_id);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select db file failed!");
        goto out;
    }

    if (row > 0) {
        int len = 0;
        for (int j = 1; j <= row; j++) {
            if (j == 1) {
                sprintf(approver, "%s", result[col * j]);
            } else {
                sprintf(approver + len, "/%s", result[col * j]);
                len += 1;
            }
            len += strlen(result[col * j]);
        }
    }

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (opinion == 1) {
        approval_status = 1;
    } else {
        approval_status = 2;
    }

    // 更新工单信息
    rc = sqlite3_exec_printf(
        Db_Store,
        "UPDATE work_order_info SET "
        "approver='%s',approval_date='%s',approval_status='%d',allocate_vsm_num='%d',"
        "annotations='%s' where wo_index ='%d'",
        0,
        0,
        &errmsg,
        approver,
        time_str,
        approval_status,
        vsm_num,
        annotations,
        wo_index);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to update!");
    }
    ret = 0;
out:

    return ret;
}

/*ip管理*/

int set_ip(char* tenant_name, char* ip) {
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;

    rc = sqlite3_exec_printf(
        Db_Store,
        "insert into ip_info (use_status,tenant_name,ip)"
        " values ('0', '%s', '%s') ",
        0,
        0,
        &errmsg,
        tenant_name,
        ip);

    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to add!");
    }

    return rc;
}

int db_check_is_administrator(char* ip, char* session_id) {
    int      rc     = 0;
    char*    errmsg = NULL;
    char**   result = NULL;
    int      row, col;
    int      ret = 0;
    sqlite3* Db  = NULL;

    if (sqlite3_open(DB_ACCOUNT_PATH, &Db) != SQLITE_OK) {
        goto out;
    }

    rc = sqlite3_get_table_printf(
        Db,
        "select id from online_users where s_id='%s' and account='administrator' and ip='%s'",
        &result,
        &row,
        &col,
        &errmsg,
        session_id,
        ip);
    if (rc != SQLITE_OK) {
        DEBUG_CLI(ERR_DEBUG, "fail to select!!");
        goto out;
    }

    if (row > 0) {
        ret = 1;
    } else {
        ret = 0;
    }
out:

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    if (Db != NULL) {
        sqlite3_close(Db);
        Db = NULL;
    }
    return ret;
}

/*common*/
int db_del_data(char* table_name, char* format, ...) {
    unsigned int ret              = -1;
    char*        result           = NULL;
    char         sql[SQL_LEN_MAX] = {0};

    if (table_name == NULL || format == NULL) return ret;

    if (_Open_DbFile() == -1) {
        DEBUG_CLI(ERR_DEBUG, "fail to open db file!!");
        return ret;
    }

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX);
    sprintf(sql, "delete  from %s where ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    DEBUG_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec_printf(Db_Store, sql, 0, 0, &result);
    if ((ret != SQLITE_OK)) {
        if (result) {
            DEBUG_CLI(ERR_DEBUG, "sql(%s) exec failed. result(%s)\n", sql, result);
            sqlite3_free(result);
        }
        goto end;
    }
    ret = 0;
end:
    _Close_DbFile();
    return ret;
}

int db_insert_data(char* table_name, char* format, ...) {
    unsigned int ret              = -1;
    char*        result           = NULL;
    char         sql[SQL_LEN_MAX] = {0};

    if (table_name == NULL || format == NULL) return -1;

    if (_Open_DbFile() == -1) {
        DEBUG_CLI(ERR_DEBUG, "fail to open db file!!");
        return ret;
    }

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX);
    sprintf(sql, "insert into %s ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    DEBUG_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec_printf(Db_Store, sql, 0, 0, &result);
    if ((ret != SQLITE_OK)) {
        if (result) {
            DEBUG_CLI(ERR_DEBUG, "sql(%s) exec failed. result(%s)\n", sql, result);
            sqlite3_free(result);
        }
        goto end;
    }
    ret = 0;
end:
    _Close_DbFile();
    return ret;
}

int db_get_num(char* table_name, char* format, ...) {
    unsigned int ret  = 0;
    int          nrow = 0, ncol = 0;
    char**       result           = NULL;
    char         sql[SQL_LEN_MAX] = {0};

    if (table_name == NULL) return ret;

    if (_Open_DbFile() == -1) {
        DEBUG_CLI(ERR_DEBUG, "fail to open db file!!");
        return ret;
    }

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX);
    sprintf(sql, "select count(*) from %s where ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    DEBUG_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(Db_Store, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = 0;
        goto end;
    }

    ret = atoi(result[ncol]);

end:
    if(result){
        sqlite3_free_table(result);
    }
    _Close_DbFile();
    return ret;
}
