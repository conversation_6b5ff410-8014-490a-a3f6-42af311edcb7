/*
 * Project: base
 * Moudle:
 * File: cps_device_monitor.c
 * Created Date: 2023-09-11 14:57:13
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include <stdlib.h>
#include <string.h>

#include "cps_common.h"
#include "cps_device.h"
#include "fwlog.h"
#include "sqlite3.h"

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/* ======================================================================================
 * implementation
 */

int cps_monitor(void) {
    int      ret    = 0;
    int      time   = 20;
    int      rc     = 0;
    char*    errmsg = NULL;
    char**   result = NULL;
    int      row, col;

    while (1) {
        // 检测密码卡状态

        ret = open_cps_db();
        if (ret)
            continue ;

        rc = sqlite3_get_table_printf(cps_db, "select device_name,ip from device_status_info", &result, &row, &col, &errmsg);
        if (rc != SQLITE_OK) {
            if (cps_db) {
            sqlite3_close(cps_db);
            cps_db = NULL;
        }
            continue;
        }

        if (row > 0) {
            for (int j = 1; j <= row; j++) {
                ret = get_device_all_status(result[col * j+1],result[col * j]);
                if (ret == ERR_SRV_CONNECT) {
                    rc = sqlite3_exec_printf(
                        cps_db,
                        "UPDATE device_status_info SET device_status='2' where ip='%s'",
                        0,
                        0,
                        &errmsg,
                        result[col * j+1]);

                        rc = sqlite3_exec_printf(
                        cps_db,
                        "UPDATE vsm_manage SET run_state=5 where host_ip='%s'",
                        0,
                        0,
                        &errmsg,
                        result[col * j+1]);

                        fw_log_write(2, LOG_WARNING, "dsp_msg=\"%s %s\"", gettext("device disconnected,ip is"),result[col * j+1]);
                }
            }
        }

        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }

        close_cps_db();
        sleep(time);
    }
}
