#include "cps_common.h"
#include "cps_msg.h"
#include "cps_image_manage.h"
#include "cps_cluster_manage.h"
#include "key_operate.h"
#include "map_tools.h"
//static int cps_get_vsm_interface_ip(char *vsm_id, char *interface_name, char *ipv4, char *ipv6);
static int cps_get_vsm_interface(char *vsm_id, map_t *map);

int cluster_host_check(char *cluster_id, char *host_ip)
{
    unsigned int ret = -1;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (cluster_id == NULL || host_ip == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id = \'%s\'", CPS_CLUSTER_HOST_TABLE, cluster_id);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)!\n", sql, err_msg);
		ret = ERR_SQL_OPT;
        sqlite3_free(err_msg);
        goto out;
	}

    //如果该cluster_id对应的host_ip只有一个，且是传入的host_ip,则该集群仅属于该主机
    if (nrow == 1) {
        if (strcmp(host_ip, result[1]) == 0)
            ret = ERR_NONE;
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}
int cluster_del_from_device(char *host_ip)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (host_ip == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_id from %s where host_ip = \'%s\'", CPS_CLUSTER_HOST_TABLE, host_ip);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)!\n", sql, err_msg);
		ret = ERR_SQL_OPT;
        sqlite3_free(err_msg);
        goto out;
	}

    for (i = 0; i < nrow; i++) {
        ret = cluster_host_check(result[i + 1], host_ip);
        if (ret)
            continue ;

        ret = cps_del_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id = \'%s\'", result[1]);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");

        //同步删除服务数据
        ret = cps_del_data_from_db(CPS_SRV_MANAGE_TABLE, "cluster_id = \'%s\'", result[1]);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");
        //同步删除集群接口数据
        ret = cps_del_data_from_db(CPS_SRV_INTERFACE_IP_TABLE, "cluster_id = \'%s\'", result[1]);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cluster_del_all(char *tenant_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_id from %s where tenant_name = \'%s\'", CPS_CLUSTER_MANAGE_TABLE, tenant_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)!\n", sql, err_msg);
		ret = ERR_SQL_OPT;
        sqlite3_free(err_msg);
        goto out;
	}

    for (i  = 0; i < nrow; i++) {
        ret = cluster_delete_proc(result[i + 1], true);
        API_CHECK_FUNC(ret, "cluster_delete_proc");
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cluster_vsm_delete(char *vsm_id, int force_flag)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    int cluster_state = 0;
    char sql[SQL_LEN_MAX] = {0};
    char cluster_id[64] = {0};
	char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char **result = NULL;
    char *pData = NULL;
    char *data_base64 = NULL;
    cJSON *root = NULL;
    cJSON *array = NULL;
    msg_header_t *msg_header = NULL;

    memset(cluster_id, 0, sizeof(cluster_id));
    ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id", vsm_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strlen(cluster_id) <= 0)
        return ret;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取集群开启状态
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_state from %s where cluster_id = \'%s\'", CPS_CLUSTER_MANAGE_TABLE, cluster_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    cluster_state = atoi(result[1]);

    //组修改集群接口调用的json数据,即传入参数
    ret = cluster_opt_data_comp(cps_db, request_id, cluster_id, cluster_state, data, sizeof(data), true, vsm_id);
    API_CHECK_FUNC(ret, "cluster_opt_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);
    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    //获取之前创建该集群的宿主机ip
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, cluster_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    for (i = 0; i < nrow; i++) {
        if (force_flag == false) {
            //组tcp传输body数据
            memset(data, 0, sizeof(data));
            body_len = sizeof(data);
            ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

            //组tcp包
            msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
            if (!msg_header) {
                ret = ERR_MALLOC;
                goto out;
            }
            cps_request_init(msg_header, data, body_len, CLUSTER_MAIN_TYPE, CMD_MODIFY);

            //发送tcp请求,并获取返回状态
            ret = cps_request_send(msg_header, callback_status_check, request_id);
            if (ret != 403){
                API_CHECK_FUNC(ret, "cps_request_send");
            }
        }

        //更新集群虚拟机数量
        ret = cps_update_data_to_db(CPS_CLUSTER_MANAGE_TABLE, "vsm_num=vsm_num-1 where cluster_id=\'%s\'", cluster_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");

        //更新集群虚拟机表
        ret = cps_del_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id=\'%s\' and cluster_id = \'%s\'", vsm_id, cluster_id);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    //close_cps_db();
    return ret;
}

int cluster_name_check(char *cluster_name, char *tenant_name)
{
    unsigned int ret  = 0;
    int nrow = 0, ncol = 0;
    char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (cluster_name == NULL || tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select * from %s where tenant_name=\'%s\' and cluster_name=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, tenant_name, cluster_name);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }

    if (nrow > 0)
        ret = ERR_ORDER_DUP;

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cluster_add_data_comp(cJSON *root, char *cluster_id)
{
    unsigned int ret = ERR_NONE;
    char request_id[16];
    cJSON *vsm_array = NULL;

    if (root == NULL || request_id == NULL)
        return ERR_PARAM;


    vsm_array = cJSON_CreateArray();
    //生成8位随机数作为requestid
    memset(request_id, 0, sizeof(request_id));
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "create");
	cJSON_AddStringToObject(root, "clusterId", cluster_id);
	cJSON_AddNumberToObject(root, "status", 0);
    cJSON_AddItemToObject(root, "vsms", vsm_array);
out:
    return ret;
}

int cps_cluster_vrid_maintan(int cluster_type, char *tenant_name, int *vrid)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (vrid == NULL || tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取一个可用的vrid
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_vrid from %s ORDER BY cluster_vrid ASC", CPS_CLUSTER_MANAGE_TABLE);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]nrow:%d\n", __func__,__LINE__,nrow);
    if (nrow == 0) {
        *vrid = 1;
        goto out;
    }
    for (i = 0; i < (nrow - 1); i++) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "value:%d %d\n", atoi(result[i]), atoi(result[i + 1]));
        if (atoi(result[i + 1]) > (atoi(result[i]) + 1)) {
            *vrid = atoi(result[i]) + 1;
            goto out;
        }
    }
    *vrid = atoi(result[i + 1]) + 1;
    DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]vird:%d\n", __func__,__LINE__,*vrid);
    if (*vrid > 255)
        ret = ERR_CLUSTER_VRID;
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_cluster_create(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char cluster_id[16];
    char *tenant_name = argv[4];
    char *cluster_name = argv[6];
    int cluster_type = atoi(argv[8]);
    int vsm_max = 2;
    int vrid = 0;
    char *remark = NULL;

    vsm_max = atoi(argv[10]);
    if (argc == 13)
        remark = argv[12];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    DEBUG_CPS_CLI(COMMON_DEBUG, "vsm_max:%d remark:%s\n", vsm_max, remark);
    ret = cluster_name_check(cluster_name, tenant_name);
    API_CHECK_FUNC(ret, "cluster_name_check");

    //获取随机数作为cluster_id
    memset(cluster_id, 0, sizeof(cluster_id));
    cps_generate_random(cluster_id, sizeof(cluster_id));

    //获取可用的vrid
    ret = cps_cluster_vrid_maintan(cluster_type, tenant_name, &vrid);
    API_CHECK_FUNC(ret, "cps_cluster_vrid_maintan");

    ret = cps_insert_data_to_db(CPS_CLUSTER_MANAGE_TABLE, "(cluster_id,cluster_name,cluster_type,cluster_vrid,tenant_name,vsm_max,remark) values(\'%s\',\'%s\',%d,%d,\'%s\',%d,\'%s\')", cluster_id, cluster_name, cluster_type, vrid, tenant_name, vsm_max, (remark == NULL)? "" : remark);
    API_CHECK_FUNC(ret, "cps_insert_data_to_db");
out:
    print_errmsg(ret);
    return ret;
}


int cluster_vsm_data_comp(cJSON *obj, int *id, int ip_type, char *interface_name,int master_id)
{
    unsigned int ret = ERR_NONE;
    int spec = 0;
    char vsm_id[128];
    char vsm_ipv4[16];
    char vsm_ipv6[64];
    char cpu[16];
    char tmp[16];

    //获取虚拟机vsm_id
    memset(vsm_id, 0, sizeof(vsm_id));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)id, "vsm_id", vsm_id, sizeof(vsm_id), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cJSON_AddStringToObject(obj, "vsmID", vsm_id);
    memset(vsm_ipv6, 0, sizeof(vsm_ipv6)); 
    memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
    ret = cps_get_vsm_interface_ip(vsm_id, interface_name, vsm_ipv4, vsm_ipv6);
    API_CHECK_FUNC(ret, "cps_get_vsm_interface_ip");
    cJSON_AddStringToObject(obj, "vsmIP", (ip_type == IP_TYPE_V4)? vsm_ipv4 : vsm_ipv6);
    //计算权重比
    memset(tmp, 0, sizeof(tmp));
    memset(cpu, 0, sizeof(cpu));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)id, "create_spec", tmp, sizeof(tmp), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    spec = atoi(tmp);
    ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", (void *)&spec, "cpu", cpu, sizeof(cpu), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if(master_id == 0){
        cJSON_AddStringToObject(obj, "vsmWeight", cpu);
    }else{
        if (master_id == *id){
            cJSON_AddStringToObject(obj, "vsmWeight", "2");
        }else{
            cJSON_AddStringToObject(obj, "vsmWeight", "1");
        }
    }
out:
    return ret;
}

int cluster_host_insert(sqlite3 *db, char *cluster_id, char *host_ip)
{
	char sql[SQL_LEN_MAX] = {0};

    if (db == NULL || cluster_id == NULL || host_ip == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "insert into %s (cluster_id,host_ip) values(\'%s\',\'%s\')", CPS_CLUSTER_HOST_TABLE, cluster_id, host_ip);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    return ERR_NONE;
}

int cluster_state_update(sqlite3 *db, char *cluster_id, int state)
{
	char sql[SQL_LEN_MAX] = {0};

    if (db == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "update %s set cluster_state=%d where cluster_id=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, state, cluster_id);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    return ERR_NONE;
}

int cluster_table_info_clear(sqlite3 *db, char *table, char *match_index, char *match_data)
{
	char sql[SQL_LEN_MAX] = {0};

    if (db == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "delete from %s where %s=\'%s\'", table, match_index, match_data);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    return ERR_NONE;
}


int cluster_vsm_num_check(int vsm_num, char *cluster_id)
{
    unsigned int ret = ERR_NONE;
    char vsm_max[16] = {0};

    memset(vsm_max, 0, sizeof(vsm_max));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", (void *)cluster_id, "vsm_max", vsm_max, sizeof(vsm_max), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    if (vsm_num > atoi(vsm_max))
        ret = ERR_VSM_NUM;
out:
    return ret;
}

int cluster_opt_data_comp(sqlite3 *db, char *request_id, char *cluster_id, int state, char *data, int data_len, int del_flag, char *vsm_id)
{
    int ret = ERR_NONE;
    int nrow = 0, ncol = 0;
    int i = 0;
    int vrid = 0, cluster_type = 0;
    char tmp[8] = {0};
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    char *pData = NULL;
    cJSON *root = NULL;
    cJSON *array = NULL;

    if (db == NULL || request_id == NULL || cluster_id == NULL || data == NULL || (del_flag == true && vsm_id == NULL))
        return ERR_PARAM;

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "cluster_vrid", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    vrid = atoi(tmp);

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "cluster_type", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cluster_type = atoi(tmp);

    root = cJSON_CreateObject();
   
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "modify");
	cJSON_AddStringToObject(root, "clusterId", cluster_id);
    cJSON_AddNumberToObject(root, "status", state);
    cJSON_AddNumberToObject(root, "vrid", vrid);
    cJSON_AddNumberToObject(root, "cluster_type", cluster_type);

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select vsm_id,vsm_weight from %s where cluster_id=\'%s\'", CPS_CLUSTER_VSM_TABLE, cluster_id);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
	if ((ret != SQLITE_OK)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
		ret = ERR_SQL_OPT;
        goto out;
	}
    map_t *map = map_create_default();
    cJSON *interface_array= NULL;
    if (!del_flag) {
        for (i = 0; i < nrow; i++) {
            ret = cps_get_vsm_interface(result[ncol * (i + 1)], map);
            if (ret != ERR_NONE) {
                DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_vsm_interface failed\n");
                goto out;
            }
            break;
        }
    }
    if (map_size(map) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "interface is empty\n");
        ret = ERR_PARAM;
        goto out;
    }
    interface_array = cJSON_CreateArray();
    map_iter_t it;
    const void *key;
    char vsm_ipv4[64] = {0};
    char vsm_ipv6[64] = {0};
    for (int ok = map_iter_begin(map, &it, &key, NULL); ok; ok = map_iter_next(&it, &key, NULL)) {

        cJSON *obj_interface = cJSON_CreateObject();
        cJSON_AddStringToObject(obj_interface, "interfaceName", (char *)key);
        array = cJSON_CreateArray();
        
        for (i = 0; i < nrow; i++) {
            if ((del_flag == true) && (strcmp(vsm_id, result[ncol * (i + 1)]) == 0))
                continue ;
            cJSON *obj = cJSON_CreateObject();
            memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
            memset(vsm_ipv6, 0, sizeof(vsm_ipv6));
            ret = cps_get_vsm_interface_ip(result[ncol * (i + 1)], (char *)key, vsm_ipv4, vsm_ipv6);
            API_CHECK_FUNC(ret, "cps_get_vsm_interface_ip");
            if (strlen(vsm_ipv4) > 0) {
                cJSON_AddStringToObject(obj, "vsmIP", vsm_ipv4);
            } else {
                if (strlen(vsm_ipv6) > 0) {
                    cJSON_AddStringToObject(obj, "vsmIP", vsm_ipv6);
                } else {
                    DEBUG_CPS_CLI(ERR_DEBUG, "srv modify vsm_ipv4 and vsm_ipv6 are empty\n");
                    ret = ERR_PARAM;
                    goto out;
                }
            }
            cJSON_AddStringToObject(obj, "vsmID", result[ncol * (i + 1)]);
            //result[ncol * (i + 1) + 2]获取vsm_weight
            const char *weight = result[ncol + i*ncol + 1];
            if (weight != NULL) {
                cJSON_AddStringToObject(obj, "vsmWeight", weight);
            }
            cJSON_AddItemToArray(array, obj);
        }
        cJSON_AddItemToObject(obj_interface, "vsms", array);
        cJSON_AddItemToArray(interface_array, obj_interface);
    }
    
    
    cJSON_AddItemToObject(root, "interface", interface_array);
	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "json data:%s\r\n", pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
    }
out:
    if (map)
        map_destroy(map);
    if(result){
        sqlite3_free_table(result);
    }
    if (pData)
        cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

int cluster_vsm_ref_update(sqlite3 *db, char *cluster_id, int opt_type)
{
    int ret = 0;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (db == NULL || cluster_id == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select vsm_id from %s where cluster_id=\'%s\'", CPS_CLUSTER_VSM_TABLE, cluster_id);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    for (i = 0; i < nrow; i++) {
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "update %s set ref_flag=%d where vsm_id=\'%s\'", CPS_VSM_MANAGE_TABLE, (opt_type == OPT_ENABLE)? 1 : 0, result[i+1]);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
        if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            return ERR_SQL_OPT;
        }
    }

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cluster_opt_proc(int state, char *cluster_id)
{
    unsigned int ret = ERR_NONE;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    int nrow = 0, ncol = 0, i = 0;
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
	char request_id[16] = {0};
	char sql[SQL_LEN_MAX] = {0};
    char *data_base64 = NULL;
    char **result = NULL;
    msg_header_t *msg_header = NULL;

    ret = cluster_opt_check(cluster_id, (state == 1)? OPT_ENABLE : OPT_DISABLE, false);
    API_CHECK_FUNC(ret, "cluster_opt_check");

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //组创建虚拟机接口调用的json数据,即传入参数
    ret = cluster_opt_data_comp(cps_db, request_id, cluster_id, state, data, sizeof(data), false, NULL);
    API_CHECK_FUNC(ret, "cluster_opt_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);
    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //获取之前创建该集群的宿主机ip
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, cluster_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    for (i = 0; i < nrow; i++) {
        //组tcp传输body数据
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, CLUSTER_MAIN_TYPE, CMD_MODIFY);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");
    }
    ret = cluster_state_update(cps_db, cluster_id, state);
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    //close_cps_db();
    return ret;
}

int cluster_opt_check(char *cluster_id, int opt_type, int enforce)
{
    int ret = 0;
    int ref_num = 0;
    int state = 0, vsm_num = 0;
    char tmp[8];
    char srv_id[16];

    memset(srv_id, 0, sizeof(srv_id));
    ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "cluster_id", cluster_id, "srv_id", srv_id, sizeof(srv_id), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    if (opt_type == OPT_DEL) {
        memset(tmp, 0, sizeof(tmp));
        ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "ref_num", tmp, sizeof(tmp), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        ref_num = atoi(tmp);

        if (enforce == false) {
            if ((strlen(srv_id) > 0) || (ref_num == 1)) {
                ret = ERR_CLUSTER_DEL;
                goto out;
            }
        }
    }else if (opt_type == OPT_DISABLE) {
        memset(tmp, 0, sizeof(tmp));
        ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "srv_id", srv_id, "state", tmp, sizeof(tmp), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        state = atoi(tmp);
        ret = (state == 1)? ERR_CLUSTER_STOP : 0;
    }else if (opt_type == OPT_ENABLE) {
        memset(tmp, 0, sizeof(tmp));
        ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "vsm_num", tmp, sizeof(tmp), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        vsm_num = atoi(tmp);
        ret = (vsm_num > 0)? 0 : ERR_CLUSTER_VSM;
    }

out:
    return ret;
}
int cluster_delete_proc(char *cluster_id, int enforce)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0, sign_len = 0;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
	char request_id[16] = {0};
    char *pData = NULL;
    char *data_base64 = NULL;
    char **result = NULL;
    cJSON *root = NULL;
    msg_header_t *msg_header = NULL;

    //集群被服务引用不能删除
    ret = cluster_opt_check(cluster_id, OPT_DEL, enforce);
    API_CHECK_FUNC(ret, "cluster_opt_check");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "destroy");
	cJSON_AddStringToObject(root, "clusterId", cluster_id);
    pData = cJSON_PrintUnformatted(root);
    data_len = strlen(pData);

    //对json数据进行签名
    ret = sign_with_internal_key(pData, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(data_len * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(pData, data_base64, data_len);

    //获取之前创建该集群的宿主机ip
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, cluster_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    for (i = 0; i < nrow; i++) {
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)realloc(msg_header, sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }

        cps_request_init(msg_header, data, body_len, CLUSTER_MAIN_TYPE, CMD_MODIFY);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");
    }
    ret = cluster_vsm_ref_update(cps_db, cluster_id, OPT_DEL);
    API_CHECK_FUNC(ret, "cluster_vsm_ref_update");

    cps_del_data_from_db(CPS_CLUSTER_HOST_TABLE, "cluster_id=\'%s\'", cluster_id);
    cps_del_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id=\'%s\'", cluster_id);
    cps_del_data_from_db(CPS_CLUSTER_VSM_TABLE, "cluster_id=\'%s\'", cluster_id);
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);
    //close_cps_db();
    return ret;

}

int cluster_vsm_opt_proc(sqlite3 *db, int *vsm_id, int vsm_num, char *cluster_id, int ip_type,int master_id)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int flavor = 0;
	char sql[SQL_LEN_MAX] = {0};
	char cpu[16] = {0};
    char **result = NULL;

    //当前cluster_vsm表中虚拟机引用标志恢复
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select vsm_id from %s where cluster_id=\'%s\'", CPS_CLUSTER_VSM_TABLE, cluster_id);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }
    for (i = 0; i < nrow; i++) {
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "ref_flag=0 where vsm_id=\'%s\'", result[i+1]);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

    //清空现有的cluster_vsm表
    cps_del_data_from_db(CPS_CLUSTER_VSM_TABLE, "cluster_id=\'%s\'", cluster_id);
    API_CHECK_FUNC(ret, "cps_delete_data_to_db");

    //添加集群虚拟机信息
    for (i = 0; i < vsm_num; i++) {
        //获取虚拟机名称，虚拟机ip，所属宿主机,宿主机ip
        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "select vsm_id,vsm_name,host_name,host_ip,create_spec from %s where id=%d", CPS_VSM_MANAGE_TABLE, vsm_id[i]);
        ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            return ERR_SQL_OPT;
        }

        memset(cpu, 0, sizeof(cpu));
        flavor = atoi(result[ncol + 4]);
        ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", &flavor, "cpu", cpu, sizeof(cpu), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if(master_id == 0){
            ret = cps_insert_data_to_db(CPS_CLUSTER_VSM_TABLE, "(cluster_id,vsm_id,vsm_name,host_name,host_ip,vsm_weight) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", cluster_id, result[ncol], result[ncol + 1], result[ncol + 2], result[ncol + 3], cpu);
        }else{
            if(master_id == vsm_id[i]){
                ret = cps_insert_data_to_db(CPS_CLUSTER_VSM_TABLE, "(cluster_id,vsm_id,vsm_name,host_name,host_ip,vsm_weight) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", cluster_id, result[ncol], result[ncol + 1], result[ncol + 2], result[ncol + 3], "2");
            }else{
                ret = cps_insert_data_to_db(CPS_CLUSTER_VSM_TABLE, "(cluster_id,vsm_id,vsm_name,host_name,host_ip,vsm_weight) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", cluster_id, result[ncol], result[ncol + 1], result[ncol + 2], result[ncol + 3], "1");
            }
        }
        API_CHECK_FUNC(ret, "cps_insert_data_to_db");

        //更新虚拟机引用标志
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "ref_flag=1 where id=%d", vsm_id[i]);
    }

    //更新虚拟机数量
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "update %s set vsm_num=%d where cluster_id=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, vsm_num, cluster_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cps_cluster_opt_check(char *cluster_id)
{
    unsigned int ret = ERR_NONE;
    char tmp[8] = {0};

    if (cluster_id == NULL)
        return ERR_PARAM;

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "ref_num", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (atoi(tmp) > 0)
        ret = ERR_CLUSTER_OPT;

out:
    return ret;
}

int cps_cluster_vsm_opt(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char **request_id = NULL;
    char vsm_id[64] = {0};
    char req_id[16] = {0};
    char image_name[128] = {0};
    char *tenant_name = argv[4];
    char *cluster_id = argv[6];
    char *id = argv[8];
    int main_id = 0, i = 0;
    int array_num = 0;
    int *id_val = NULL;
    int master_id = 0;
    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);


    if (argc > 9) {
       for (i = 0; i < argc; i++) {
            if (strcmp(argv[i], "master_id") == 0) {
                master_id = atoi(argv[i + 1]);
            }
            if (strcmp(argv[i], "main_id") == 0) {
                main_id = atoi(argv[i + 1]);
            }
       }
    }

    ret = cps_cluster_opt_check(cluster_id);
    API_CHECK_FUNC(ret, "cps_cluster_opt_check");

    if (strcmp(id, "null") == 0)
        goto save;

    id_val = parse_string_to_array(id, &array_num);
    DEBUG_CPS_CLI(COMMON_DEBUG, "array_num: %d\n",array_num);
    //申请内存
    if (array_num > 0) {
        request_id = (char **)malloc(sizeof(char *) * array_num);
        if (request_id == NULL) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "array_num: %d\n",array_num);
            ret = ERR_MALLOC;
            goto out;
        }
        for (i = 0; i < array_num; i++) {
            request_id[i] = (char *)malloc(16);
            if (request_id[i] == NULL) {
                ret = ERR_MALLOC;
                goto out;
            }
            memset(request_id[i], 0, 16);
        }
    }
    memset(vsm_id, 0, sizeof(vsm_id));
    if (main_id != 0) {
        //获取主配置vsm_id
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", &main_id, "vsm_id", vsm_id, sizeof(vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
    }else {
        //选择当前集群中的任意一台作为主节点
        int tmp_id = id_val[0];
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", &tmp_id, "vsm_id", vsm_id, sizeof(vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        //ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "cluster_id", cluster_id, "vsm_id", vsm_id, sizeof(vsm_id), false);
        //API_CHECK_FUNC(ret, "cps_get_data_from_db");
    }

    //NO_SEND
    //导出影像
    memset(req_id, 0, sizeof(req_id));
    ret = cps_image_export_proc(tenant_name, vsm_id, req_id, sizeof(req_id), false);
    API_CHECK_FUNC(ret, "cps_image_export_proc");
    ret = cps_image_export_result_check(vsm_id, req_id);
    API_CHECK_FUNC(ret, "cps_image_export_result_check");

    //导入影像
    memset(image_name, 0, sizeof(image_name));
    sprintf(image_name, "%s_%s.image", vsm_id, req_id);
    ret = cps_image_import_proc(tenant_name, image_name, id, request_id, 16, vsm_id);
    API_CHECK_FUNC(ret, "cps_image_import_proc");
    ret = cps_image_import_result_check(request_id, array_num);
    API_CHECK_FUNC(ret, "cps_image_import_result_check");
  
save:
    //修改集群
    ret = cps_cluster_vsm_opt_proc(tenant_name, cluster_id, id,master_id);
    API_CHECK_FUNC(ret, "cps_cluster_vsm_opt_proc");
    //主备模式下，更新主备vsm_weight
    if (master_id != 0) {
        //获取主节点vsm_id
        char master_vsm_id[64] = {0};
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", &master_id, "vsm_id", master_vsm_id, sizeof(master_vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strlen(master_vsm_id) > 0) {
            //更新CPS_CLUSTER_VSM_TABLE的vsm_weight为2
            ret = cps_update_data_to_db(CPS_CLUSTER_VSM_TABLE, "vsm_weight='2' where vsm_id=\'%s\'", master_vsm_id);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
            //另外一台vsm_weight则为1
            ret = cps_update_data_to_db(CPS_CLUSTER_VSM_TABLE, "vsm_weight='1' where vsm_id!=\'%s\' and cluster_id=\'%s\'",master_vsm_id, cluster_id);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }
    }
out:
    print_errmsg(ret);
    for (i = 0; i < array_num; i++) {
        if (request_id[i] != NULL) {
            free(request_id[i]);
            request_id[i] = NULL;
        }
    }
    if (request_id != NULL)
        free(request_id);
    if (id_val != NULL) {
        free(id_val);
        id_val = NULL;
    }
    return ret;
}

int cps_check_vsm_ip_type(int array_num, int *id_val, int *ip_type)
{
    int ret = ERR_NONE;
    int i = 0;
    char ipv4[16];
    char ipv6[64];
    /*
    for (i = 0; i < array_num; i++) {
        memset(ipv4, 0, sizeof(ipv4));
        memset(ipv6, 0, sizeof(ipv6));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&id_val[i], "vsm_ipv4", ipv4, sizeof(ipv4), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&id_val[i], "vsm_ipv6", ipv6, sizeof(ipv6), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        if ((strlen(ipv4) > 0) && (strlen(ipv6) <= 0)) {
            *ip_type = IP_TYPE_V4;
            break ;
        }else if ((strlen(ipv6) > 0) && (strlen(ipv4) <= 0)) {
            *ip_type = IP_TYPE_V6;
            break ;
        }
    }
    */
   if(array_num > 0){
        int fisrt_id = id_val[0];
        char vsm_id[64] = {0};
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&fisrt_id, "vsm_id", vsm_id, sizeof(vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if(strlen(vsm_id) > 0){
            ret = cps_get_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id", (void *)vsm_id, "vsm_ipv4", ipv4, sizeof(ipv4), true);
            API_CHECK_FUNC(ret, "cps_get_data_from_db");
            if(strlen(ipv4) > 0 && strlen(ipv6) <= 0){
                *ip_type = IP_TYPE_V4;
            }else if(strlen(ipv4) <= 0 && strlen(ipv6) > 0){
                *ip_type = IP_TYPE_V6;
            }else if (strlen(ipv4) > 0 && strlen(ipv6) > 0){
                *ip_type = IP_TYPE_V4;
            }else{
                ret = ERR_PARAM;
                goto out;
            }
        }
   }

out:
    return ret;
}

int cps_get_vsm_interface(char *vsm_id, map_t *map)
{
    int ret = ERR_NONE;
    char sql[SQL_LEN_MAX] = {0};
    sqlite3_stmt *stmt=NULL; 
    char str_query[SQL_LEN_MAX] = {0};
    sprintf(str_query, "select interface_name FROM vsm_ip_info where vsm_id=\'%s\'", vsm_id);
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");
    int rc = sqlite3_prepare_v2(cps_db, str_query, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to prepare statement: %s", sqlite3_errmsg(cps_db));
        ret = ERR_SQL_OPT;
        goto out;
    }
    if(sqlite3_step(stmt) == SQLITE_ROW) {
        char *interface_name = (char *)sqlite3_column_text(stmt, 0);
        map_insert(map, interface_name, interface_name);
    }
    sqlite3_reset(stmt);
    sqlite3_finalize(stmt);
out:
    return ret;
}

int cps_get_vsm_interface_ip(char *vsm_id, char *interface_name, char *ipv4, char *ipv6)
{
    int ret = ERR_NONE;
    char sql[SQL_LEN_MAX] = {0};
    sqlite3_stmt *stmt=NULL; 
    char str_query[SQL_LEN_MAX] = {0};
    sprintf(str_query, "select vsm_ipv4,vsm_ipv6 FROM vsm_ip_info where vsm_id=\'%s\' and interface_name=\'%s\'", vsm_id, interface_name);
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");
    int rc = sqlite3_prepare_v2(cps_db, str_query, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to prepare statement: %s", sqlite3_errmsg(cps_db));
        ret = ERR_SQL_OPT;
        goto out;
    }
    if(sqlite3_step(stmt) == SQLITE_ROW) {  
        char *vsm_ipv4 = (char *)sqlite3_column_text(stmt, 0);
        char *vsm_ipv6 = (char *)sqlite3_column_text(stmt, 1);
        if(strlen(vsm_ipv4) > 0){
            strcpy(ipv4, vsm_ipv4);
        }
        if(strlen(vsm_ipv6) > 0){
            strcpy(ipv6, vsm_ipv6);
        }
    }
    sqlite3_reset(stmt);
    sqlite3_finalize(stmt);
out:
    return ret;
}



int cps_cluster_vsm_opt_proc(char *tenant_name, char *cluster_id, char *id, int master_id)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int array_num = 0, i = 0;
    int nrow = 0, ncol = 0;
    int sign_len = 0;
    int ip_type = IP_TYPE_V4;
    char spec[16] = {0};
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024];
    char host_ip[64];
    char tenant[128];
	char sql[SQL_LEN_MAX] = {0};
	char cluster_state[8] = {0};
	char cluster_type[8] = {0};
	char cluster_vrid[8] = {0};
	char tmp[64] = {0};
    char *pData = NULL;
    char *data_base64 = NULL;
    int *id_val = NULL;
    char **result = NULL;
    cJSON *root = NULL;
    cJSON *vsm_array = NULL;
    cJSON *interface_array = NULL;
    msg_header_t *msg_header = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "id:%s", id);
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s\n",tenant_name, tenant);
        ret = ERR_SUPP_OPT;
        goto out;
    }

    if (strcmp(id, "null") == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm id null\n");
        goto save;
    }else {
        //解析id数据转为整形数组
        id_val = parse_string_to_array(id, &array_num);
        if (id_val == NULL) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "parse_string_to_array failed!\r\n");
            ret = ERR_PARAM;
            goto out;
        }
    }

    memset(sql, 0, sizeof(sql));
    strcat(sql, "select DISTINCT host_ip from vsm_manage where");
    //获取所选虚拟机所属宿主机ip
    for (i = 0; i < array_num; i++) {
        memset(tmp, 0, sizeof(tmp));
        if (i == 0)
            sprintf(tmp, " id=%d", id_val[i]);
        else
            sprintf(tmp, " or id=%d", id_val[i]);
        strcat(sql, tmp);
    }

save:
    ret = cluster_vsm_num_check(array_num, cluster_id);
    API_CHECK_FUNC(ret, "cluster_vsm_num_check");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    if (array_num == 0) {
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, cluster_id);
    }
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    if (nrow == 0)
        goto out;

    //获取启动状态
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", (void *)cluster_id, "cluster_state", cluster_state, sizeof(cluster_state), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //获取集群模式
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", (void *)cluster_id, "cluster_type", cluster_type, sizeof(cluster_type), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //获取集群vrid
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", (void *)cluster_id, "cluster_vrid", cluster_vrid, sizeof(cluster_vrid), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    root = cJSON_CreateObject();
    
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "modify");
	cJSON_AddStringToObject(root, "clusterId", cluster_id);
	cJSON_AddNumberToObject(root, "status", atoi(cluster_state));
	cJSON_AddNumberToObject(root, "vrid", atoi(cluster_vrid));
	cJSON_AddNumberToObject(root, "cluster_type", atoi(cluster_type));
    //添加虚拟机
    ret = cps_check_vsm_ip_type(array_num, id_val, &ip_type);
    API_CHECK_FUNC(ret, "cps_check_vsm_type");
    //查询虚机有几张网卡及网卡名
    map_t *map = map_create(hash_cstr, eq_cstr, dup_cstr, free_std, NULL, NULL);
    if (array_num >0){
        int fisrt_id = id_val[0];
        char vsm_id[64] = {0};
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", (void *)&fisrt_id, "vsm_id", vsm_id, sizeof(vsm_id), true);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if(strlen(vsm_id) > 0){
           cps_get_vsm_interface(vsm_id, map);
        }
    }
    map_iter_t it;
    const void *key;
    interface_array  = cJSON_CreateArray();
    cJSON_AddItemToObject(root, "interface", interface_array);
    for (int ok = map_iter_begin(map, &it, &key, NULL); ok; ok = map_iter_next(&it, &key, NULL)) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "添加集群vsm,interface_name:%s\n", (char *)key);
        cJSON *obj_interface = cJSON_CreateObject();
        cJSON_AddStringToObject(obj_interface, "interfaceName", (char *)key);
        vsm_array = cJSON_CreateArray();
        // 下面的 for 循环会把每个虚机的对象加到 vsms_array 里
        for (i = 0; i < array_num; i++) {
            cJSON *obj = cJSON_CreateObject();
            ret = cluster_vsm_data_comp(obj, &id_val[i], ip_type, (char *)key, master_id);
            API_CHECK_FUNC(ret, "cluster_vsm_data_comp");
            cJSON_AddItemToArray(vsm_array, obj);
        }
        cJSON_AddItemToObject(obj_interface, "vsms", vsm_array);
        cJSON_AddItemToArray(interface_array, obj_interface);
     }
    
	pData = cJSON_PrintUnformatted(root);
    data_len = strlen(pData);
    DEBUG_CPS_CLI(COMMON_DEBUG, "JSON data:%s\n", pData);
    //对json数据进行签名
    ret = sign_with_internal_key(pData, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(data_len * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(pData, data_base64, data_len);

    //清除cluster_host表
    ret = cluster_table_info_clear(cps_db, CPS_CLUSTER_HOST_TABLE, "cluster_id", cluster_id);
    API_CHECK_FUNC(ret, "cluster_table_info_clear");

    //组tcp传输body数据
    for (i = 0; i < nrow; i++) {
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)realloc(msg_header, sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }

        cps_request_init(msg_header, data, body_len, CLUSTER_MAIN_TYPE, CMD_MODIFY);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");

        //更新cluster_host表
        ret = cluster_host_insert(cps_db, cluster_id, result[i + 1]);
        API_CHECK_FUNC(ret, "cluster_host_insert");
    }

    //成功后更新数据库
    ret = cluster_vsm_opt_proc(cps_db, id_val, array_num, cluster_id, ip_type,master_id);
    
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    if (id_val)
        free(id_val);
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);
    if (map)
        map_destroy(map);
    return ret;
}

int cps_cluster_edit(int argc, char *argv[])
{
    unsigned int ret =ERR_NONE;
    int nrow = 0, ncol = 0;
    char *tenant_name = argv[4];
    char *cluster_id = argv[6];
    char *cluster_name = argv[8];
    char *remark = NULL;
    int vsm_max = atoi(argv[10]);
	char tenant[128] = {0};
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (argc == 13)
        remark = argv[12];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s", tenant_name, tenant);
        return ERR_SUPP_OPT;
    }

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select * from %s where cluster_id=\'%s\'", CPS_CLUSTER_VSM_TABLE, cluster_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    if (vsm_max < nrow) {
        ret = ERR_VSM_NUM;
        goto out;
    }

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "update %s set cluster_name=\'%s\',vsm_max=%d,remark=\'%s\' where cluster_id=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, cluster_name, vsm_max, (remark == NULL)? "" : remark, cluster_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql(%s)\n", sql);
    if (sqlite3_exec(cps_db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    print_errmsg(ret);
    //close_cps_db();
    return ret;
}

int cps_cluster_opt(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int cluster_state = 0;
    int enforce = false;
    char *opt = argv[3];
    char *tenant_name = argv[5];
    char *cluster_id = argv[7];
    char tenant[128] = {0};

    if (argc == 9)
        enforce = true;

    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s", tenant_name, tenant);
        return ERR_SUPP_OPT;
    }

    if (strcmp(opt, "delete") == 0) {
        ret = cluster_delete_proc(cluster_id, enforce);
        API_CHECK_FUNC(ret, "cluster_delete_proc");
    }else {
        cluster_state = (strcmp(opt, "start") == 0)? 1 : 0;
        ret = cluster_opt_proc(cluster_state, cluster_id);
        API_CHECK_FUNC(ret, "cluster_opt_proc");
    }

out:
    print_errmsg(ret);
    return ret;
}

int cps_cluster_syn(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char **request_id = NULL;
    char cluster_id[128] = {0};
    char vsm_id[64] = {0};
    char tenant[128] = {0};
    char req_id[16] = {0};
    char tmp[8] = {0};
    char image_name[128] = {0};
    char *tenant_name = argv[4];
    char *syn_id = argv[6];
    int temp_id = atoi(argv[8]);
    int array_num = 0, i = 0;
    int cluster_state = 0;
    int *id_val = NULL;

    //获取cluster_id
    memset(cluster_id, 0, sizeof(cluster_id));
    memset(vsm_id, 0, sizeof(vsm_id));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "id", &temp_id, "vsm_id", vsm_id, sizeof(vsm_id), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id", vsm_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //ret = cps_cluster_opt_check(cluster_id);
    //API_CHECK_FUNC(ret, "cps_cluster_opt_check");

    //获取集群状态
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "cluster_state", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cluster_state = atoi(tmp);


    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s\n",tenant_name, tenant);
        ret = ERR_SUPP_OPT;
        goto out;
    }

    id_val = parse_string_to_array(syn_id, &array_num);
    DEBUG_CPS_CLI(COMMON_DEBUG, "array_num: %d\n",array_num);
    //申请内存
    if (array_num > 0) {
        request_id = (char **)malloc(sizeof(char *) * array_num);
        if (request_id == NULL) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "array_num: %d\n",array_num);
            ret = ERR_MALLOC;
            goto out;
        }
        for (i = 0; i < array_num; i++) {
            request_id[i] = (char *)malloc(16);
            if (request_id[i] == NULL) {
                ret = ERR_MALLOC;
                goto out;
            }
            memset(request_id[i], 0, 16);
        }
    }

    //导出影像
    memset(req_id, 0, sizeof(req_id));
    ret = cps_image_export_proc(tenant_name, vsm_id, req_id, sizeof(req_id), true);
    API_CHECK_FUNC(ret, "cps_image_export_proc");
    ret = cps_image_export_result_check(vsm_id, req_id);
    API_CHECK_FUNC(ret, "cps_image_export_result_check");

    //导入影像
    memset(image_name, 0, sizeof(image_name));
    sprintf(image_name, "%s_%s.image", vsm_id, req_id);
    ret = cps_image_import_proc(tenant_name, image_name, syn_id, request_id, 16, vsm_id);
    API_CHECK_FUNC(ret, "cps_image_import_proc");
    ret = cps_image_import_result_check(request_id, array_num);
    API_CHECK_FUNC(ret, "cps_image_import_result_check");

    ret = cluster_opt_proc(cluster_state, cluster_id);
    API_CHECK_FUNC(ret, "cluster_opt_proc");
out:
    print_errmsg(ret);
    for (i = 0; i < array_num; i++) {
        if (request_id[i] != NULL) {
            free(request_id[i]);
            request_id[i] = NULL;
        }
    }
    if (request_id != NULL)
        free(request_id);
    if (id_val != NULL) {
        free(id_val);
        id_val = NULL;
    }
    return ret;
}
