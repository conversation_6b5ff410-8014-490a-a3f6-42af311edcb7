/*
 * Project: base
 * Moudle:
 * File: cps_device.h
 * Created Date: 2023-08-29 14:38:21
 * Author: caohongfa
 * Description: caohongfa
 * Copyright (c) Inc
 */
#ifndef _cps_DEVICE_H
#define _cps_DEVICE_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
int cps_init(int argc, char* argv[]);
int cps_get_status(int argc, char* argv[]);
int cps_clean(int argc, char* argv[]);
int cps_self_check(int argc, char* argv[]);
int cps_device_add(int argc, char* argv[]);
int cps_device_del(int argc, char* argv[]);
int cps_get_sign_pub_key(int argc, char* argv[]);
int cps_get_sign_pri_key(int argc, char* argv[]);
int get_device_all_status(char* ip,char* host_name);
#endif  // _cps_DEVICE_H