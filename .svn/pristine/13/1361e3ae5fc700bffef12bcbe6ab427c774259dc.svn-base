#pragma once
#pragma pack(push, 1)
#define TCP_SERVER_PORT  31080
#define HTTP_SERVER_PORT 33080
// 包头
typedef struct msg_header {
    unsigned int   len;       // 整个msg的长度包含body的长度,网络字节序
    unsigned short maintype;  // 命令主类型
    unsigned short subtype;   // 命令子类型
    unsigned short flag;      // 标志默认为8227
    unsigned short
        status;  // 标志与服务交互状态(0:正常;1:解析消息失败;2:发送http请求失败或连接云服务器失败;3:不支持的操作类型)
    unsigned int  extended1;  // 扩展字段1
    unsigned int  extended2;  // 扩展字段2
    unsigned char body[0];    // body消息
} msg_header_t;
#pragma pack(pop)
// body内容采用json格式，按云密码机api接口规范字段,添加一个header头对象标识公钥指纹、算法及摘要
// 例子配置云密码机网络信息请求如下：
/*

//body
{
        "header":{
                "serverip":"************",
                "serverport":33080,
                "requestId":"12311",
                "CHSM-AuthPK":"abc123",
                "CHSM-SignatureAlg":"SM2WithSM3",
                "CHSM-Signature":"123400999"
        },
        "requestBody":{
                "data":"****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
                "length":311
        }

}
//requestBody中的data为云密码机网络信息json如下所示内容的base64编码，长度为json内容的长度
{
        "requestId":"5c48319b",
        "netAddrs":[
                {
                "name":"eth0",
                "ip":"***********",
                "mask":"*************",
                "gateway":"*************"
                },
                {
                "name":"eth0",
                "ip":"***********",
                "mask":"*************",
                "gateway":"*************"
                }
        ],
        "dnsList":[
        "*********",
        "********"
        ]
}
*/
// 主命令类型
enum MESSAGE_MAIN_TYPE {
    CHSM_MAIN_TYPE    = 1,  // 操作云服务器密码机
    VSM_MAIN_TYPE     = 2,  // 操作虚拟机
    CLUSTER_MAIN_TYPE = 3,  // 集群操作
    SERVICE_MAIN_TYPE = 4,  // 服务操作
    PUBKEY_MAIN_TYPE  = 5   // 公钥配置及获取操作
};
// 云密码机配置子命令类型
enum MESSAGE_SUB_TYPE {
    CMD_INFO = 1,          // 获取详细信息
    CMD_STATUS,            // 获取状态
    CMD_ALLSTATUS,         // 获取所有状态
    CMD_NETWORK,           // 网络配置
    CMD_NTP,               // 配置NTP命令
    CMD_IMAGE,             // 配置影像上传地址命令
    CMD_LOG,               // 配置日志上传地址命令
    CMD_TOKEN,             // Token配置
    CMD_EXPORT_IMAGE,      // 导出影像
    CMD_IMPORT_IMAGE,      // 导入影像
    CMD_UPGRADE,           // 升级命令
    CMD_RESTART,           // 重启命令
    CMD_BACKUP,            // 备份命令
    CMD_RESTORE,           // 恢复命令
    CMD_START,             // 启动命令
    CMD_STOP,              // 停止命令
    CMD_RESET,             // 重置命令
    CMD_CREATE,            // 创建命令(虚拟机/集群/服务)
    CMD_DELETE,            // 删除命令(虚拟机/集群/服务)
    CMD_MODIFY,            // 修改命令(集群/服务)
    CMD_PUBKEY_GET,        // 获取公钥指纹信息
    CMD_PUBKEY_SET,        // 设置公钥指纹信息
    CMD_EXPORT_ALL_IMAGE,  // 导出所有虚拟机影像
    CMD_IMPORT_ALL_IMAGE,  // 导入所有虚拟机影像
    CMD_TENANT_REQUEST,    // 租户资源申请
    CMD_TENANT_DELETE,     /// 租户删除
    CMD_TENANT_CLEAN,      // 删除所有租户
    CMD_CONFIG_SAVE,       // 配置保存
    CMD_INIT_DEV,          // 初始化
    CMD_DIRECT_OPT,        // 直接操作命令
    CMD_DATA_SYNC,         // 云平台数据同步命令
    CMD_DATA_RESTORE       // 云平台数据恢复命令
};

enum VSM_NET_CONFIG_OPT_TYPE {
    CMD_INTERFACE_ADD = 70,
    CMD_INTERFACE_DEL = 71,
    CMD_INTERFACE_SET = 72,
    CMD_ROUTE_ADD = 73,
    CMD_ROUTE_DEL = 74,
    CMD_ROUTE_SET = 75
};