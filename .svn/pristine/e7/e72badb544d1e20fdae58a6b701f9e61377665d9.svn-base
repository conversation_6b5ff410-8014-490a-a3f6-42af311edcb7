#ifndef __cps_VSM_MANAGE_H__
#define __cps_VSM_MANAGE_H__

#include "cps_common.h"

#define VSM_MANAGE_TABLE "vsm_manage"

#define OPT_DESTROY "destroy"
#define OPT_NETCONF "netconf"
#define OPT_STOP "stop"
#define OPT_RESTART "restart"
#define OPT_UPGRADE "upgrade"
#define OPT_IMAGE_IMPORT "image import"
#define OPT_DESTROY "destroy"

typedef enum
{
    VSM_OPT_CREATE  = 0,
    VSM_OPT_EDIT    = 1,
    VSM_OPT_DEL     = 2
}VSM_OPT;

typedef enum
{
    CPU_MODEL_EXCL  = 0,
    CPU_MODEL_SHARE = 1
}CPU_MODEL;

typedef enum
{
    VSM_TYPE_HSM    = 1,    //密码机
    VSM_TYPE_SVS    = 2,    //签名验签服务器
    VSM_TYPE_TSA    = 3,    //时间戳系统
    VSM_TYPE_CSS    = 4,    //协同签名服务器
    VSM_TYPE_EDB    = 5,    //数据库加密系统
    VSM_TYPE_KMS    = 6,    //密钥管理系统
    VSM_TYPE_SAG    = 7,    //身份认证网关
    VSM_TYPE_VPN    = 8,    //VPN安全网关
    VSM_TYPE_ESIG   = 9,    //电子签章系统
    VSM_TYPE_EFS    = 10    //文件加解密系统
}VSM_TYPE;

typedef enum
{
    STATE_CREATING  = 0,    //创建中
    STATE_STARTED,          //已启动
    STATE_CLOSING,          //关闭中
    STATE_CLOSED,           //已关闭
    STATE_RESET,            //重置中
    STATE_RESTARTING,       //重启中
    STATE_DELETING,         //删除中
    STATE_EXPORTING,        //影像导出中
    STATE_STARTING,         //启动中
    STATE_IMPORTING,        //影像导入中
    STATE_CONF_SETTING,     //配置应用中
    STATE_UPGRADING,        //升级中
    STATE_UPGRADE_DONE,     //升级完成
    STATE_UNSTART,          //未启动
    STATE_MAX
}STATE;

typedef struct vsn_netconf_st
{
    char *vsm_ipv4;
    char *vsm_maskv4;
    char *vsm_gatewayv4;
    char *vsm_ipv6;
    char *vsm_maskv6;
    char *vsm_gatewayv6;
}vsm_netconf;

int cps_set_vsm_drift(int argc, char *argv[]);
int  cps_vsm_source_check(char* vsm_id, int spec);
int  update_vsm_state(int opt_state, int run_state, char* match_key, char* match_val);
void vsm_state_maintan(char *vsm_id, int run_state, int opt_state, char *cluster_state);
int get_vsminfo_callback(char *data, char *requestId);
int get_vsm_info_data_comp(char *request_id, char *vsm_id, char *data, unsigned int data_len);
int cps_vsm_opt_proc(char *opt, char *tenant_name, char *vsm_id);
int vsm_opt_state_check(char *vsm_id, char *opt, char *tenant_name);
int vsm_opt_tenant_ip_check(char *vsm_id, char *tenant_name);
int vsm_del_all(char *tenant_name);
int vsm_create_data_comp(char *request_id, char *vsm_name_str, unsigned int cpu_model, unsigned int spec, char *image_url, char *sign, vsm_netconf *netconf, char *callback_ip, char *data, unsigned int data_len);
int vsm_netconf_set(char *vsm_id, vsm_netconf *netconf);
int vsm_netconfig_data_comp(char *request_id, char *vsm_id, vsm_netconf *netconf, char *data, unsigned int data_len);
int cps_vsm_info_get(int argc, char *argv[]);
int cps_vsm_create(int argc, char *argv[]);
int cps_vsm_net_config(int argc, char *argv[]);
int cps_vsm_opt(int argc, char *argv[]);
int cps_vsm_del(int argc, char *argv[]);
int cps_vsm_image_export(int argc, char *argv[]);
int cps_get_host_source_use(sqlite3* db, char *host_name, unsigned int *cpu_use, unsigned int *mem_use, unsigned int *vsm_num);
int cps_get_host_source(sqlite3* db, char *host_name, unsigned int *cpu_total, unsigned int *mem_total);
int cps_host_show(int argc, char *argv[]);
int cps_image_show(int argc, char *argv[]);
int cps_asymm_proc(int argc, char *argv[]);
int cps_vsm_image_upgrade(int argc, char *argv[]);
int cps_vsm_pkg_upgrade(int argc, char *argv[]);
int cps_vsm_data_update(char *request_id, char *tenant_name, char *image_dig, char *image_ver, char *host_name, char *host_ip, char *vsm_name, int spec, char *remark);
int vsm_spec_set_data_comp(char *request_id, char *vsm_id, unsigned int spec, char *callback_id, char *data, unsigned int data_len);
int cps_set_vsm_opt_state(char *vsm_id, char *request_id, unsigned int opt_type);
int cps_vsm_remark_set(int argc, char *argv[]);
int cps_vsm_spec_set(int argc, char *argv[]);
int cps_get_vsm_source_cur(sqlite3* db, char *vsm_id, unsigned int *cpu_cur, unsigned int *mem_cur);
int vsm_del_from_device(char *device_name);
int cps_get_spec_source(sqlite3 *db, unsigned int *cpu, unsigned int *mem, int spec);
int vsm_share_num_maintan(char *request_id, int opt, int state);
void vsm_info_pull(char* vsm_id, char* host_ip, char* host_name);
#endif
