# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-01 15:08-0700\n"
"Last-Translator: wanghai <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: cps_common.c:72 cps_common.c:90 cps_vsm_manage.c:1096
msgid "success"
msgstr "success"

#: cps_common.c:91
msgid "sql operate failed"
msgstr "set tenant failed!"

#: cps_common.c:92
msgid "parameter error"
msgstr "parameter error"

#: cps_common.c:93
msgid "duplicate work orders"
msgstr "duplicate work orders"

#: cps_common.c:94
msgid "malloc failed"
msgstr "malloc failed"

#: cps_common.c:95
msgid "connect to the server failed"
msgstr "connect to the server failed"

#: cps_common.c:96
msgid "request id did not match"
msgstr "request id did not match"

#: cps_common.c:97
msgid "data parse failed"
msgstr "data parse failed"

#: cps_common.c:98
msgid "number of vms is incorrect"
msgstr "number of vms is incorrect"

#: cps_common.c:99
msgid "cluster can be referenced by only one service"
msgstr "cluster can be referenced by only one service"

#: cps_common.c:100
msgid "cluster is still referenced, cannot be deleted"
msgstr "cluster has been referenced, cannot be deleted"

#: cps_common.c:101
msgid "cluster is still referenced, cannot be stoped"
msgstr "cluster has been referenced, cannot be stoped"

#: cps_common.c:102
msgid "cluster vrid error"
msgstr "cluster vrid error"

#: cps_common.c:103
msgid "unsupported operation"
msgstr "unsupported operation"

#: cps_common.c:104
msgid "no operation authority"
msgstr "no operation authorty"

#: cps_common.c:105
msgid "get authpk failed"
msgstr "get authpk failed!"

#: cps_common.c:106
msgid "cluster is disabled"
msgstr "the cluster is not started and services cannot be started"

#: cps_common.c:107
msgid "please add vsm first"
msgstr "please add vsm first"

#: cps_common.c:108
msgid "cluster has been referenced by service, delete the service first"
msgstr "cluster has been referenced by service, delete the service first"

#: cps_common.c:109
msgid "vsm is referenced by cluster, unable to operate"
msgstr "vsm is referenced by cluster, unable to operate"

#: cps_common.c:110
msgid "administrator needs to add a callback address"
msgstr "administrator needs to add a callback address"

#: cps_common.c:111
msgid "image upload address error"
msgstr "image upload address error"

#: cps_common.c:112
msgid "vsm is not ready, image cannot be exported"
msgstr "vsm is not ready, image cannot be exported"

#: cps_common.c:113
msgid "vsm resources are insufficient to support this operation"
msgstr "vsm resources are insufficient to support this operation"

#: cps_common.c:114
msgid "vsm synchronization failed"
msgstr "vsm synchronization failed"

#: cps_common.c:115
msgid "vsm with the same name exists"
msgstr "vsm with the same name exists"

#: cps_common.c:116
msgid "read data failure"
msgstr "read data failure"

#: cps_common.c:117
msgid "insufficient disk resources"
msgstr "insufficient disk resources"

#: cps_common.c:118
msgid ""
"ip addresses of the vsm to be added to the cluster must be of the same type"
msgstr ""
"ip addresses of the vsm to be added to the cluster must be of the same type"

#: cps_common.c:119
msgid "vsm ip not within address_range,please change ip first"
msgstr "vsm ip not within address_range,please change ip first"

#: cps_common.c:120
msgid "recv error message follow"
msgstr "recv error message follow"

#: cps_common.c:121
msgid "image type unauthorized"
msgstr "image type unauthorized"

#: cps_common.c:122
msgid "vsm type unauthorized"
msgstr "vsm type not matching"

#: cps_common.c:123 cps_work_order.c:333
#, c-format
msgid "vsm not exist"
msgstr "vsm not exist"

#: cps_common.c:124
msgid "unknown error"
msgstr "unknown error"

#: cps_device.c:343 cps_device_monitor.c:97
msgid "device disconnected,ip is"
msgstr "device disconnected,ip is"

#: cps_device.c:735 cps_device.c:797
#, c-format
msgid "need delete device!"
msgstr "need delete device!"

#: cps_device.c:741 cps_device.c:803
#, c-format
msgid "need delete tenant!"
msgstr "need delete tenant!"

#: cps_device.c:751 cps_device.c:757
#, c-format
msgid "device init device file failed!"
msgstr "device init device file failed!"

#: cps_device.c:763 cps_device.c:769
#, c-format
msgid "device init usr file failed!"
msgstr "device init usr file failed!"

#: cps_device.c:775 cps_device.c:782
#, c-format
msgid "device init failed!"
msgstr "device init failed!"

#: cps_device.c:810
#, c-format
msgid "device clean failed!"
msgstr "device clean failed!"

#: cps_device.c:854
msgid "self check failed!"
msgstr "self check failed!"

#: cps_device.c:856
msgid "self check successful!"
msgstr "self check successful!"

#: cps_device.c:1020
#, c-format
msgid "having the same name or ip!"
msgstr "having the same name or ip!"

#: cps_device.c:1033 cps_device.c:1037 cps_device.c:1052
#, c-format
msgid "add device faild!"
msgstr "add device faild!"

#: cps_device.c:1041
#, c-format
msgid "device dose not exist,ip:%s!"
msgstr "device dose not exist,ip:%s!"

#: cps_device.c:1046
#, c-format
msgid "restore device info faild!!"
msgstr "restore device info faild!"

#: cps_device.c:1063
#, c-format
msgid "get device info faild!"
msgstr "get device info faild!"

#: cps_device.c:1081
#, c-format
msgid "device added successfully,invalid callback addr,please modify it!"
msgstr "device added successfully,invalid callback addr,please modify it!"

#: cps_device.c:1103 cps_device.c:1116 cps_device.c:1123
#, c-format
msgid "delete device faild!"
msgstr "delete device faild!"

#: cps_rights.c:542
#, c-format
msgid "sn error,not found device!"
msgstr "sn error,not found device!"

#: cps_rights.c:615 cps_rights.c:799 cps_rights.c:2250
#, c-format
msgid "pin code error,remaining retry count:%d!"
msgstr "pin code error,remaining retry count:%d!"

#: cps_rights.c:618 cps_rights.c:802 cps_rights.c:2253
#, c-format
msgid "pin code locked!"
msgstr "USBKey locked!"

#: cps_rights.c:621 cps_rights.c:805 cps_rights.c:2256
#, c-format
msgid "pin code invalid,remaining retry count:%d!"
msgstr "pin code invalid,remaining retry count:%d!"

#: cps_rights.c:624 cps_rights.c:808 cps_rights.c:2259
#, c-format
msgid "pin len error!"
msgstr "pin len error!"

#: cps_rights.c:627 cps_rights.c:811 cps_rights.c:2262
#, c-format
msgid "pin error,remaining retry count:%d!"
msgstr "pin error,remaining retry count:%d!"

#: cps_rights.c:697 cps_rights.c:749
#, c-format
msgid "failed to write to file!"
msgstr "failed to write to file!"

#: cps_rights.c:774 cps_rights.c:2223
#, c-format
msgid "device not found!"
msgstr "device not found!"

#: cps_rights.c:827 cps_rights.c:834 cps_rights.c:1751
#, c-format
msgid "user not registered!"
msgstr "user not registered!"

#: cps_rights.c:866 cps_rights.c:896
#, c-format
msgid "can not get user key!"
msgstr "can not get user key!"

#: cps_rights.c:976
#, c-format
msgid "device list not obtained!"
msgstr "device list not obtained!"

#: cps_rights.c:1089 cps_rights.c:1226 cps_rights.c:1744
#, c-format
msgid "no corresponding tenant!"
msgstr "no corresponding tenant!"

#: cps_rights.c:1102 cps_rights.c:1239 cps_rights.c:1463 cps_rights.c:1533
#, c-format
msgid "there are enough administrators!"
msgstr "there are enough administrators!"

#: cps_rights.c:1110 cps_rights.c:1131 cps_rights.c:1159 cps_rights.c:1182
#: cps_rights.c:1245 cps_rights.c:1265 cps_rights.c:1293 cps_rights.c:1316
#: cps_rights.c:1471 cps_rights.c:1491 cps_rights.c:1500 cps_rights.c:1539
#: cps_rights.c:1558 cps_rights.c:1567
#, c-format
msgid "add user failed!"
msgstr "add user failed!"

#: cps_rights.c:1591
#, c-format
msgid "parameter error!"
msgstr "parameter error!"

#: cps_rights.c:1597 cps_rights.c:1604
#, c-format
msgid "not allowed to delete the last admin!"
msgstr "not allowed to delete the last admin!"

#: cps_rights.c:1612
#, c-format
msgid "delete user failed!"
msgstr "delete user failed!"

#: cps_rights.c:1652 cps_rights.c:1672 cps_rights.c:1764 cps_rights.c:1773
#: cps_rights.c:1780 cps_rights.c:1800 cps_rights.c:1809
#, c-format
msgid "login failed!"
msgstr "login failed!"

#: cps_rights.c:1700
#, c-format
msgid "get random faild"
msgstr "get random faild"

#: cps_rights.c:1827 cps_rights.c:1833 cps_rights.c:1840 cps_rights.c:1930
#, c-format
msgid "login out failed!"
msgstr "login out failed!"

#: cps_rights.c:1871 cps_rights.c:2106
#, c-format
msgid "open db failed!"
msgstr "open db failed!"

#: cps_rights.c:2131
#, c-format
msgid "check failed!"
msgstr "check failed!"

#: cps_rights.c:2146
#, c-format
msgid "super administrator permissions required!"
msgstr "super administrator permissions required!"

#: cps_rights.c:2157
#, c-format
msgid "administrator permissions required!"
msgstr "administrator permissions required!"

#: cps_rights.c:2168
#, c-format
msgid "operator permissions required!"
msgstr "operator permissions required!"

#: cps_rights.c:2179
#, c-format
msgid "auditor permissions required!"
msgstr "auditor permissions required!"

#: cps_rights.c:2189
#, c-format
msgid "tenant permissions required!"
msgstr "tenant permissions required!"

#: cps_rights.c:2242
#, c-format
msgid "open device failed!"
msgstr "open device failed!"

#: cps_rights.c:2293
#, c-format
msgid "failed to modify comments!"
msgstr "failed to modify comments!"

#: cps_strategy.c:296
#, c-format
msgid "the addr_name or tenant has already been assigned!"
msgstr "the addr_name or tenant has already been assigned!"

#: cps_strategy.c:304 cps_strategy.c:320 cps_strategy.c:351 cps_strategy.c:376
#: cps_strategy.c:404
#, c-format
msgid "set addr faild!"
msgstr "set addr faild!"

#: cps_strategy.c:344
#, c-format
msgid "no addr info of tenant!"
msgstr "no addr info of tenant!"

#: cps_strategy.c:390
#, c-format
msgid "vsm in use,please delete them!"
msgstr "vsm in use,please delete them!"

#: cps_strategy.c:397
#, c-format
msgid "the previous address has already been assigned!"
msgstr "the previous address has already been assigned!"

#: cps_strategy.c:411
#, c-format
msgid "del addr faild!"
msgstr "del addr faild!"

#: cps_strategy.c:435
#, c-format
msgid "set image url faild,device ip is:%s"
msgstr "set image url faild,device ip is:%s"

#: cps_strategy.c:479
#, c-format
msgid "invalid callback addr,host ip is:%s!"
msgstr "invalid callback addr,host ip is:%s!"

#: cps_strategy.c:486
#, c-format
msgid "set callback addr faild!"
msgstr "self check failed!"

#: cps_strategy.c:502
#, c-format
msgid "failed to set image drift switch!"
msgstr "failed to set image drift switch!"

#: cps_tenant.c:114
#, c-format
msgid "having the same name or account!"
msgstr "having the same name or account!"

#: cps_tenant.c:146
#, c-format
msgid "add tenant failed!"
msgstr "add tenant failed!"

#: cps_tenant.c:186
#, c-format
msgid "set tenant failed!"
msgstr "set tenant failed!"

#: cps_tenant.c:216
#, c-format
msgid "reset admin faild!"
msgstr "reset admin faild!"

#: cps_tenant.c:256
#, c-format
msgid "tenant has virtual machines in use!"
msgstr "tenant has virtual machines in use!"

#: cps_tenant.c:264
#, c-format
msgid "delete tenant failed!"
msgstr "delete tenant failed!"

#: cps_vsm_manage.c:1096
msgid "upgrade failed"
msgstr "upgrade failed"

#: cps_work_order.c:166
#, c-format
msgid "vsm type not matching"
msgstr "vsm type not matching"

#: cps_work_order.c:285
#, c-format
msgid "invalid command"
msgstr "invalid command"

#: cps_work_order.c:293
#, c-format
msgid "no corresponding tenant found!"
msgstr "no corresponding tenant found!"

#: cps_work_order.c:304
#, c-format
msgid "address information not configured!"
msgstr "address information not configured!"

#: cps_work_order.c:318 cps_work_order.c:370
#, c-format
msgid "approval faild!"
msgstr "approval faild!"

#: cps_work_order.c:340
#, c-format
msgid "insufficient memory and CPU resources!"
msgstr "insufficient memory and CPU resources!"

#: cps_work_order.c:346
#, c-format
msgid "need to shut down the virtual machine!"
msgstr "need to shut down the virtual machine!"

#: database_api.c:210
#, c-format
msgid "the sn has already registered!"
msgstr "the sn has already registered!"

#: database_api.c:236
#, c-format
msgid "the name has already registered!"
msgstr "the name has already registered!"

#: database_api.c:579
#, c-format
msgid "sn and name not corresponding!"
msgstr "sn and name not corresponding!"

#: database_api.c:583
#, c-format
msgid "sn not register!"
msgstr "sn not register!"

#: database_api.c:1674 database_api.c:1682
#, c-format
msgid "IP conflict:%s-%s and %s !"
msgstr "IP conflict:%s-%s and %s !"

#: database_api.c:1771 database_api.c:1777
#, c-format
msgid "IP conflict:%s and %s !"
msgstr "IP conflict:%s and %s !"

#~ msgid "the device has been add by other cps!"
#~ msgstr "the device has been add by other cps!"

#~ msgid "not enough disk space!"
#~ msgstr "not enough disk space!"

#~ msgid "unapproved CPU or memory resources!"
#~ msgstr "unapproved CPU or memory resources!"

#~ msgid "start"
#~ msgstr "start"

#~ msgid "shutdown"
#~ msgstr "shutdown"

#~ msgid "ready"
#~ msgstr "ready"

#~ msgid "error"
#~ msgstr "error:"

#~ msgid "restart"
#~ msgstr "restart"

#~ msgid "close"
#~ msgstr "close"

#~ msgid "administrator needs to add a image upload address"
#~ msgstr "administrator needs to add a image upload address"

#~ msgid "http request send failed"
#~ msgstr "http request send failed"

#~ msgid "cpu exclusive resources are insufficient"
#~ msgstr "cpu exclusive resources are insufficient"

#~ msgid "vsm start success"
#~ msgstr "vsm start success"

#~ msgid "the ip has already been used!"
#~ msgstr "the ip has already been used!"

#~ msgid "have not the subnet!"
#~ msgstr "have not the subnet!"

#~ msgid "there are no devices to set!"
#~ msgstr "there are no devices to set!"
