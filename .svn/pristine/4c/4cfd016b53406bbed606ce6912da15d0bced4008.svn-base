#ifndef __cps_SRV_MANAGE_H__
#define __cps_SRV_MANAGE_H__

enum
{
    OPT_CREATE = 0,
    OPT_EDIT 
};

enum
{
    WEIGHT_POLICY_RR = 0,
    WEIGHT_POLICY_SR,
    WEIGHT_POLICY_WRR
};


typedef struct cluster_srv_info_st
{
    char *srv_id;
    char *srv_name;
    char *tenant_name;
    char *cluster_name;
    char *cluster_id;
    char *cluster_ip;
    int srv_port;
    int srv_http_port;
    int load_mode;      //0-轮询模式 1-源ip哈希 2-加权轮询
    int weight_policy;  //0-资源权重 1-平均权重
    char *remark;
    char *interface_json;
}cluster_srv_info;


int srv_conf_proc(char *action, char *tenant_name, char *srv_id);
int srv_del_all(char *tenant_name);
int srv_cluster_ref_update(sqlite3 *db, char *srv_id, int opt_type);
int srv_conf_db_update(sqlite3 *db, char *srv_id, char *tenant_name, char *action, int state);
int srv_opt_db_update(cluster_srv_info *srv, int opt);
int srv_del_data_comp(char *request_id, char *srv_id, char *data, int data_len);
int srv_opt_data_comp(char *request_id, cluster_srv_info *srv, int state, int opt, char *data, int data_len);
int cps_srv_check(sqlite3 *db, char *name, char *tenant_name);
int cps_srv_add(int argc, char *argv[]);
int cps_srv_edit(int argc, char *argv[]);
int cps_srv_conf(int argc, char *argv[]);

#endif
