#ifndef __cps_CLUSTER_MANAGE_H__
#define __cps_CLUSTER_MANAGE_H__

#include <sqlite3.h>

typedef enum
{
    CLUSTER_MODE_MB         = 1,    //主备模式
    CLUSTER_MODE_CLUSTER    = 2     //集群模式
}CLUSTER_MODE;

int cluster_del_from_device(char *host_ip);
int cluster_delete_proc(char *cluster_id, int enforce);
int cluster_del_all(char *tenant_name);
int cluster_vsm_delete(char *vsm_id, int force_flag);
int cps_cluster_create(int argc, char *argv[]);
int cps_cluster_opt(int argc, char *argv[]);
int cps_cluster_del(int argc, char *argv[]);
int cps_cluster_vsm_opt(int argc, char *argv[]);
int cps_cluster_syn(int argc, char *argv[]);
int cps_cluster_edit(int argc, char *argv[]);
int cluster_opt_check(char *cluster_id, int opt_type, int enforce);
int cluster_opt_data_comp(sqlite3 *db, char *request_id, char *cluster_id, int state, char *data, int data_len, int del_flag, char *vsm_id);
int cps_cluster_vsm_opt_proc(char *tenant_name, char *cluster_id, char *id, int master_id);
int cps_get_vsm_interface_ip(char *vsm_id, char *interface_name, char *ipv4, char *ipv6);
#endif
