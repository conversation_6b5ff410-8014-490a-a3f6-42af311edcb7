#include "cjson.h"
#include "cps_common.h"
#include "cps_msg.h"
#include "message.h"
/* 默认使用10K栈空间，避免频繁的内存分配 */
#define DEFAULT_MSG_BUFFER_SIZE (10 * 1024)
int cps_requet_pre_proc(
    char*         orig_data,
    int           data_len,
    char*         host_ip,
    char*         request_id,
    char*         body_data,
    unsigned int* body_len) {
    unsigned int ret         = ERR_NONE;
    char*        data_base64 = NULL;

    if (orig_data == NULL || host_ip == NULL || request_id == NULL || body_data == NULL || body_len == NULL)
        return ERR_PARAM;

    // 对数据进行base64编码
    data_base64 = (unsigned char*)malloc(data_len * 2);
    if (!data_base64) {
        return ERR_MALLOC;
    }
    base64_encode(orig_data, data_base64, data_len);

    // 对base64数据进行签名

    // 组传输body数据
    ret = cps_msg_body_comp(host_ip, request_id, NULL /*签名值*/, data_base64, data_len, body_data, body_len);

end:
    if (data_base64) {
        free(data_base64);
        data_base64 = NULL;
    }
    return ret;
}

/**
 * @brief 初始化请求
 * @param	msg_header 请求头结构
 * @param	data body数据,即header+requestBody数据
 * @param	main_type 命令住类型枚举值
 * @param	sub_type 子令住类型枚举值
 * @return
 *   @retval	0		成功
 *   @retval	非0		失败，返回错误代码
 */
void cps_request_init(
    msg_header_t*  msg_header,
    char*          data,
    unsigned int   data_len,
    unsigned short main_type,
    unsigned short sub_type) {
    int    ret     = ERR_NONE;
    cJSON* pRoot   = NULL;
    cJSON* pheader = NULL;
    pRoot          = cJSON_CreateObject();
    pheader        = cJSON_CreateObject();

    if (msg_header == NULL) return;

    msg_header->len       = htonl(sizeof(msg_header_t) + data_len);
    msg_header->maintype  = htons(main_type);
    msg_header->subtype   = htons(sub_type);
    msg_header->flag      = htons(8227);
    msg_header->status    = 0;
    msg_header->extended1 = 0;
    msg_header->extended2 = 0;
    memcpy(msg_header->body, data, data_len);
}

/**
 * @brief 组body数据
 * @param	srv_ip 宿主机ip
 * @param	request_id 请求的request id
 * @param	signature data数据签名值的base64编码
 * @param	data_len 入参json数据的长度
 * @param	body_data 传出的body数据缓冲区
 * @param	data_len 数据缓冲区长度
 * @return
 *   @retval	0		成功
 *   @retval	非0		失败，返回错误代码
 */
int cps_msg_body_comp(
    char*         srv_ip,
    char*         request_id,
    char*         signature,
    char*         data,
    unsigned int  data_len,
    char*         body_data,
    unsigned int* body_len) {
    int    ret          = ERR_NONE;
    char   auth_pk[128] = {0};
    char*  pData        = NULL;
    cJSON* root         = NULL;
    cJSON* header       = NULL;
    cJSON* req_body     = NULL;

    if (srv_ip == NULL || request_id == NULL || data == NULL || body_data == NULL || body_len == NULL) return ERR_PARAM;
    ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "ip", (void *)srv_ip, "pub_fingerprint", auth_pk, sizeof(auth_pk), false);
    if (ret) {
        ret = ERR_GET_AUTHPK;
        DEBUG_CPS_CLI(COMMON_DEBUG, "get authpk failed!\r\n");
        goto end;
    }

    root     = cJSON_CreateObject();
    header   = cJSON_CreateObject();
    req_body = cJSON_CreateObject();

    cJSON_AddStringToObject(header, "serverip", srv_ip);
    cJSON_AddNumberToObject(header, "serverport", HTTP_SERVER_PORT + 1);
    cJSON_AddStringToObject(header, "requestId", request_id);
    cJSON_AddStringToObject(header, "CHSM-AuthPK", auth_pk);
    cJSON_AddStringToObject(header, "CHSM-SignatureAlg", "SM2WithSM3");
    cJSON_AddStringToObject(header, "CHSM-Signature", signature);
    cJSON_AddItemToObject(root, "header", header);

    cJSON_AddStringToObject(req_body, "data", data);
    cJSON_AddNumberToObject(req_body, "length", data_len);
    cJSON_AddItemToObject(root, "requestBody", req_body);

    pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n", __func__, __LINE__, pData);
        if (strlen(pData) > *body_len) {
            ret = ERR_PARAM;
            goto end;
        }
        memcpy(body_data, pData, strlen(pData));
        body_data[strlen(pData)] = '\0';
        *body_len           = strlen(pData);
    }
end:
    if (pData) cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

/**
 * @brief 组body数据,不用签名与指纹
 * @param	srv_ip 宿主机ip
 * @param	request_id 请求的request id
 * @param	signature data数据签名值的base64编码
 * @param	data_len 入参json数据的长度
 * @param	body_data 传出的body数据缓冲区
 * @param	data_len 数据缓冲区长度
 * @return
 *   @retval	0		成功
 *   @retval	非0		失败，返回错误代码
 */
int cps_msg_body_comp_spcial(
    char*         srv_ip,
    char*         request_id,
    char*         data,
    unsigned int  data_len,
    char*         body_data,
    unsigned int* body_len) {
    int    ret      = ERR_NONE;
    char*  pData    = NULL;
    cJSON* root     = NULL;
    cJSON* header   = NULL;
    cJSON* req_body = NULL;

    if (srv_ip == NULL || request_id == NULL || data == NULL || body_data == NULL || body_len == NULL) return ERR_PARAM;

    root     = cJSON_CreateObject();
    header   = cJSON_CreateObject();
    req_body = cJSON_CreateObject();

    cJSON_AddStringToObject(header, "serverip", srv_ip);
    cJSON_AddNumberToObject(header, "serverport", HTTP_SERVER_PORT + 1);
    cJSON_AddStringToObject(header, "requestId", request_id);
    cJSON_AddStringToObject(header, "CHSM-AuthPK", "");
    cJSON_AddStringToObject(header, "CHSM-SignatureAlg", "SM2WithSM3");
    cJSON_AddStringToObject(header, "CHSM-Signature", "");
    cJSON_AddItemToObject(root, "header", header);

    cJSON_AddStringToObject(req_body, "data", data);
    cJSON_AddNumberToObject(req_body, "length", data_len);
    cJSON_AddItemToObject(root, "requestBody", req_body);

    pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n", __func__, __LINE__, pData);
        if (strlen(pData) > *body_len) {
            ret = ERR_PARAM;
            goto end;
        }
        memcpy(body_data, pData, strlen(pData));
        body_data[strlen(pData)] = '\0';
        *body_len = strlen(pData);
    }
end:
    if (pData) cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

int cps_sock_connect(int sockfd, const struct sockaddr *addr, socklen_t addrlen)
{
    int flags, n, error;
    socklen_t len;
    fd_set wset;
    struct timeval tval;

    /* 调用fcntl把套接字设置为非阻塞 */
    flags = fcntl(sockfd, F_GETFL, 0);
    fcntl(sockfd, F_SETFL, flags | O_NONBLOCK);

    /* 发起非阻塞connect。期望的错误是EINPROGRESS，表示连接建立已经启动但是尚未完成  */
    error = 0;
    if ((n = connect(sockfd, addr, addrlen)) < 0) {
        if (errno != EINPROGRESS) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "connect failed!");
            return(-1);
        }
    }

    /* 如果非阻塞connect返回0，那么连接已经建立。当服务器处于客户端所在主机时这种情况可能发生 */
    if (n == 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "connect completed immediately");
        goto done; /* connect completed immediately */
    }

    /* 调用select等待套接字变为可写，如果select返回0，那么表示超时 */
    #if 1
    FD_ZERO(&wset);
    FD_SET(sockfd, &wset);
    tval.tv_sec = 3;
    tval.tv_usec = 0;

    if ((n = select(sockfd+1, NULL, &wset, NULL, &tval)) == 0) {
        close(sockfd); /* timeout */
        errno = ETIMEDOUT;
        return(-1);
    }

    /* 检查可写条件，调用getsockopt取得套接字的待处理错误，如果建立成功，该值将为0 */
    if (FD_ISSET(sockfd, &wset)) {
        len = sizeof(error);
        if (getsockopt(sockfd, SOL_SOCKET, SO_ERROR, &error, &len) < 0)
            return(-1); /* Solaris pending error */
    }else {
        DEBUG_CPS_CLI(COMMON_DEBUG, "select error: sockfd not set\r\n");
        return -1;
    }
    #endif

    /* 恢复套接字的文件状态标志并返回 */
done:
    fcntl(sockfd, F_SETFL, flags); /* restore file status flags */
    if (error) {
        close(sockfd); /* just in case */
        errno = error;
        return(-1);
    }

    return(0);
}

int cps_msg_rcv(int sockfd, void *buff, int buff_len)
{
    int rcv_bytes = 0;
    int tmp_len = 0;
    fd_set read_fds;

    while(1) {
        FD_ZERO(&read_fds);
        FD_SET(sockfd, &read_fds);
        struct timeval timeout;
        timeout.tv_sec = 15; // 设置超时时间为 5 秒
        timeout.tv_usec = 0;

        int ready_fds = select(sockfd + 1, &read_fds, NULL, NULL, &timeout);
        if (ready_fds == -1) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "select failed!");
            return -1;
        }else if (ready_fds == 0) {
            return -1;
        }

        if (FD_ISSET(sockfd, &read_fds)) {
            // 套接字就绪，可以进行读取操作
            rcv_bytes = recv(sockfd, buff, buff_len, 0);
            if (rcv_bytes == -1) {
                DEBUG_CPS_CLI(COMMON_DEBUG, "read failed!");
                return -1;
            }else if(rcv_bytes == 0) {
                DEBUG_CPS_CLI(COMMON_DEBUG, "Connection closed by remote host.");
                break;
            }
            tmp_len += rcv_bytes;
            if (tmp_len == buff_len)
                break;
        }
    }

    return tmp_len;
}

/**
 * @brief 组body数据
 * @param	msg_header_t 请求结构
 * @param	cb  回调函数指针，解析返回数据，即处理结果
 * @return
 *   @retval	0		成功
 *   @retval	非0		失败，返回错误代码
 */
int cps_request_send(msg_header_t* msg_header, call_back cb, char* request_id) {
    int            ret = ERR_NONE;
    int            fd;
    int            wr_len, rcv_len, msg_len, body_len;
    unsigned short status = 0;
    msg_header_t   header;
    

    char           default_msg_buffer[DEFAULT_MSG_BUFFER_SIZE];
    char*          msg = NULL;
    int            need_free = 0;
    
    struct timeval tv;
    char*          err_msg[] = {"data parse failed!", "http request or connect service failed!"};

    DEBUG_CPS_CLI(COMMON_DEBUG, "socket create 111!");
    // 1.创建通信socket
    fd = socket(AF_INET, SOCK_STREAM, 0);
    if (fd < 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "socket create failed!");
        return ERR_SRV_CONNECT;
    }

    struct sockaddr_in local_addr = {0};
    local_addr.sin_family         = AF_INET;  // 使用IPv4协议
    local_addr.sin_port           = htons(TCP_SERVER_PORT);
    local_addr.sin_addr.s_addr    = inet_addr("127.0.0.1");

    if (cps_sock_connect(fd, (struct sockaddr*)&local_addr, sizeof(local_addr)) != 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "cps_sock_connect failed!");
        ret = ERR_SRV_CONNECT;
        goto end;
    }

    wr_len = send(fd, msg_header, ntohl(msg_header->len), 0);
    memset(&header, 0, sizeof(header));
    // 接受头部
    rcv_len = cps_msg_rcv(fd, &header, sizeof(header));
    if (rcv_len <= 0) {
        ret = ERR_READ;
        goto end;
    }

    // 接收body
    if (rcv_len) {
        msg_len = ntohl(header.len);
        status  = ntohs(header.status);
        if (status != 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "err msg:%s\r\n",err_msg[status - 1]);
            ret = status;
            if (ret == 2) {
                ret = ERR_SRV_CONNECT;
            }
            goto end;
        }

        if (msg_len > sizeof(msg_header_t)) {
            body_len = msg_len - sizeof(msg_header_t);
            
            /* 判断是否需要动态分配内存 */
            if (body_len <= DEFAULT_MSG_BUFFER_SIZE) {
                /* 使用默认栈空间 */
                msg = default_msg_buffer;
                need_free = 0;
                DEBUG_CPS_CLI(COMMON_DEBUG, "Using default buffer (10K) for message body");
            } else {
                /* 消息体超过10K，需要动态分配内存 */
                msg = (char*)calloc(1, body_len + 1);
                if (!msg) {
                    ret = ERR_MALLOC;
                    goto end;
                }
                need_free = 1;
                DEBUG_CPS_CLI(COMMON_DEBUG, "Message body exceeds 10K, allocated %d bytes dynamically", body_len);
            }
            
            rcv_len = cps_msg_rcv(fd, msg, body_len);
            if (rcv_len <= 0) {
                ret = ERR_READ;
            } else if (rcv_len == body_len) {
                if (cb) {
                    ret = cb(msg, request_id);
                    goto end;
                }
            }
        }
    }
end:
    /* 只有动态分配的内存才需要释放 */
    if (msg && need_free == 1) {
        free(msg);
        msg = NULL;
    }
    close(fd);
    return ret;
}
