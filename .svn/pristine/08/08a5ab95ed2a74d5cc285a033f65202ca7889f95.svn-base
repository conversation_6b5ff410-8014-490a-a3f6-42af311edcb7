
#ifndef _cps_COMMON_H
#define _cps_COMMON_H

/* ======================================================================================
 * includes
 */
#include <arpa/inet.h>
#include <cliframe/cliframe.h>
#include <netinet/in.h>
#include <sqlite3.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <time.h>
#include <unistd.h>
#include <fcntl.h>

#include "cjson.h"
#include "cli_public.h"

/* ======================================================================================
 * macros
 */
#define CPS_VSM_IMAGE_PATH      "/media/hd/data/chsm/image"
#define CPS_DB_PATH                 "/usr/local/conf/cps.db"
#define CPS_FLAVOR_INFO_TABLE       "flavor_info"
#define CPS_IMG_ADDR_TABLE          "image_addr"
#define CPS_ADDR_INFO_TABLE         "addr_info"
#define CPS_DEV_STATUS_TABLE        "device_status_info"
#define CPS_WORK_ORDER_TABLE        "work_order_info"
#define CPS_VSM_MANAGE_TABLE        "vsm_manage"
#define CPS_ADMIN_INFO_TABLE        "admin_info"
#define CPS_TENANT_INFO_TABLE       "tenant_info"
#define CPS_IP_INFO_TABLE           "ip_info"
#define CPS_ADDR_INFO_TABLE         "addr_info"
#define CPS_INTERFACE_INFO_TABLE    "interface_info"
#define CPS_CALLBACKADDR_INFO_TABLE "callback_addr"
#define CPS_DRIFT_OPTION_TABLE "drift_option"
#define CPS_IMAGE_EXPORT_TABLE      "image_export"
#define CPS_IMAGE_IMPORT_TABLE      "image_import"
#define CPS_CLUSTER_MANAGE_TABLE    "cluster_manage"
#define CPS_CLUSTER_VSM_TABLE       "cluster_vsm"
#define CPS_CLUSTER_VSM_TABLE_BAK   "cluster_vsm_bak"
#define CPS_CLUSTER_HOST_TABLE      "cluster_host"
#define CPS_CLUSTER_HOST_TABLE_BAK  "cluster_host_bak"
#define CPS_SRV_MANAGE_TABLE        "srv_manage"
#define CPS_VSM_UPGRADE_TABLE       "upgrade_history"
#define CPS_SHARE_VSM_MAX_TABLE     "shared_vsm_create_max"
#define CPS_IMAGE_MAP_TABLE         "image_map"

#define DATA_MAX_LEN 4096

#define USER_NAME_MAX_LEN            65
#define DEFAULT_LOG_INFO_LEN         1024
#define DEFAULT_IP_STR_LEN           46
#define DEFAULT_SESSION_ID_STR_LEN   256
#define DEFAULT_TIME_STR_LEN         32
#define DEFAULT_SN_STR_LEN           32
#define DEFAULT_PRI_KEY_ACCESS_RIGHT "11111111"  // 私钥访问授权码
#define DEFAULT_ACCESS_RIGHT_LEN     8           // 私钥访问授权码的长度
#define DEFAULT_KEK_INDEX            1           // 默认密钥加密密钥索引

#define OLD_DEBUG_FLAG     0
#define OLD_DEBUG_DEL_FLAG 1
#define CLI_DBG            1
#define COMMON_DEBUG       1
#define WARNING_DEBUG      2
#define WATCH_DEBUG        3
#define ERR_DEBUG          4
#define IGNORE_DEBUG       5  // 记录日志
#ifndef false
    #define false 0
#endif
#ifndef true
    #define true 1
#endif

#define CPS_DEBUG_FILE "/tmp/cli_cps_debug"

#define DEBUG_CPS_CLI(tag, fmt...)                                                  \
    do {                                                                            \
        if (!access(CPS_DEBUG_FILE, 0) && tag && tag != IGNORE_DEBUG) {             \
            FILE* fp;                                                               \
            fp = fopen(CPS_DEBUG_FILE, "a");                                        \
            if (NULL == fp) {                                                       \
                fprintf(stderr, "Create debug file error!\n");                      \
            } else {                                                                \
                time_t    now = time(NULL);                                         \
                struct tm tm_now;                                                   \
                localtime_r(&now, &tm_now);                                         \
                char time_str[64];                                                  \
                strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", &tm_now); \
                                                                                    \
                fprintf(fp, "[%s] %s[%d] ", time_str, __FILE__, __LINE__);          \
                switch (tag) {                                                      \
                    case COMMON_DEBUG:                                              \
                        fprintf(fp, "debug --> ");                                  \
                        break;                                                      \
                    case WARNING_DEBUG:                                             \
                        fprintf(fp, "warning --> ");                                \
                        break;                                                      \
                    case WATCH_DEBUG:                                               \
                        fprintf(fp, "watch --> ");                                  \
                        break;                                                      \
                    case ERR_DEBUG:                                                 \
                        fprintf(fp, "error --> ");                                  \
                        break;                                                      \
                    default:                                                        \
                        break;                                                      \
                }                                                                   \
                fprintf(fp, ##fmt);                                                 \
                fprintf(fp, "\n");                                                  \
                fclose(fp);                                                         \
            }                                                                       \
        }                                                                           \
    } while (0)

#define DEBUG_CPS_CLI_SHOW()                                    \
    int i = 0;                                                  \
    for (i = 0; i < argc; i++) {                                \
        DEBUG_CPS_CLI(COMMON_DEBUG, "cli [%d]:%s", i, argv[i]); \
    }

#define API_CHECK_FUNC(ret, func)                                     \
    do {                                                              \
        if (ret) {                                                    \
            DEBUG_CPS_CLI(ERR_DEBUG, func " failed! error=%0x", ret); \
            goto out;                                                 \
        }                                                             \
    } while (0)

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
enum {
    IP_TYPE_V4 = 0,
    IP_TYPE_V6 = 1
};

enum {
    DEV_STATUS_INIT     = 0,
    DEV_STATUS_READY    = 1,
    DEV_STATUS_DROP     = 2
};

enum {
    OPT_ADD     = 0,
    OPT_DEL     = 1,
    OPT_ENABLE  = 2,
    OPT_DISABLE = 3
};

typedef enum {
    VSM_STATE_NORMAL   = 0,
    VSM_STATE_INITIAL  = 1,
    VSM_STATE_ERR      = 2,
    VSM_STATE_SHUTDOWN = 3,
    VSM_STATE_RESTART  = 4
} VSM_STATE;

typedef enum {
    ERR_NONE            = 0,
    ERR_SQL_OPT         = 1,   // 数据库操作失败
    ERR_PARAM           = 2,   // 参数错误
    ERR_ORDER_DUP       = 3,   // 重复的工单
    ERR_MALLOC          = 4,   // 内存分配失败
    ERR_SRV_CONNECT     = 5,   // 连接服务失败
    ERR_REQUEST_ID      = 6,   // request_id不匹配
    ERR_CJSON           = 7,   // JSON数据解析失败
    ERR_VSM_NUM         = 8,   // 虚拟机数量超过最大限制
    ERR_CLUSTER_REF     = 9,   // 集群只能被一个服务所引用
    ERR_CLUSTER_DEL     = 10,   // 集群被服务引用，无法删除
    ERR_CLUSTER_STOP    = 11,  // 集群被服务引用，无法关闭
    ERR_CLUSTER_VRID    = 12,  // 集群vrid错误
    ERR_SUPP_OPT        = 13,  // 不支持的操作
    ERR_OPT_AUTH        = 14,  // 无操作权限
    ERR_GET_AUTHPK      = 15,  // 获取公钥指纹失败
    ERR_CLUSTER_ENABLE  = 16,  // 集群未开启,无法启动服务
    ERR_CLUSTER_VSM     = 17,  // 集群内没有虚拟机，无法启动
    ERR_CLUSTER_OPT     = 18,  // 集群已被服务引用，无法修改，请先删除服务
    ERR_VSM_OPT         = 19,  // 虚拟机被集群引用，无法操作
    ERR_CALLBACK_ADDR   = 20,  // 请管理员添加回调地址
    ERR_IMAGE_ADDR      = 21,  // 请管理员添加影像上传地址
    ERR_VSM_INIT        = 22,  // 虚拟机未初始化，不允许导出影像
    ERR_VSM_RESOURCE    = 23,  // 租户资源不够，不支持扩容，请申请资源
    ERR_VSM_SYMM        = 24,  // 虚拟机同步失败
    ERR_VSM_NAME        = 25,  // 已存在相同的虚拟机
    ERR_READ            = 26,  // 读取数据失败
    ERR_DISK_RESOURCE   = 27,  // 磁盘空间不足
    ERR_VSM_IP          = 28,  // 加入集群的虚拟机ip类型必须相同
    ERR_ADDR_RANGE      = 29,  // 虚机ip不在地址池范围内,请先修改ip地址
    ERR_RECV_MESSAGE    = 30,  // "空的，需要显示回复的信息"
    ERR_IMAGE_TYPE      = 31,  // 镜像类型未授权
    ERR_VSM_TYPE        = 32,  // 虚拟机类型未授权  "与上面的 镜像类型未授权 是同样的错误"
    ERR_VSM_NO_EXIST    = 33,  // 虚拟机不存在
    ERR_UNKNOWN         = 34,  // 未知错误
    ERR_MAX
} ERR_CPS_TANENT;

extern sqlite3* cps_db;

int open_cps_db();
void close_cps_db();
const char *queryerrmsg(int errcode);
void cps_url_comp(char *ip, int port, char *sub, char *url, int url_len);
int ip_status_check(char *ip);
void gettext_test();
void print_errmsg(int errcode);
int* parse_string_to_array(char* string, int* length);
void convert_to_string(int num, char* str);
int  cps_get_time_now(char* time_data, unsigned int data_len);
int  cps_generate_random(char* data, unsigned int data_len);
int  cps_get_callback_addr(char* addr_data, unsigned int data_len);
int  cps_get_image_addr(char* addr_data, unsigned int data_len);
int  callback_status_check(char* data, char* request_id);
int  cps_get_data_from_db(
     char*        table_name,
     char*        match_index,
     void*        match_data,
     char*        ind_data,
     char*        targ_data,
     unsigned int data_len,
     int          dig_flag);
int cps_del_data_from_db(char* table_name, char* format, ...);
int cps_insert_data_to_db(char* table_name, char* format, ...);
int cps_update_data_to_db(char* table_name, char* format, ...);
int cps_get_num_from_db(char* table_name, char* format, ...);
int callback_db(void *ptr, int count);
void cps_close_db(void);

void printHex(unsigned char* pdata, unsigned int length);
// 生成伪随机数
int cps_get_random_soft(char* random, int random_len);
#endif  // _cps_COMMON_H
