/*
 * Project: base
 * Moudle: cps
 * File: cps_strategy.h
 * Created Date: 2023-08-29 14:53:30
 * Author: caohongfa
 * Description: caohongfa
 * -----
 * todo: modified
 * -----
 * Copyright (c) Inc
 */
#ifndef _cps_STRATEGY_H
#define _cps_STRATEGY_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
int cps_addr_add(int argc,char* argv[]);
int cps_addr_set(int argc,char* argv[]);
int cps_addr_del(int argc,char* argv[]);
int cps_addr_check(int argc,char* argv[]);
int cps_img_addr_set(int argc,char* argv[]);
int cps_callback_addr_set(int argc,char* argv[]);
int cps_cb_addr_check_at_add_device(void);
int cps_image_drift_option(int argc, char* argv[]);
#endif  // _cps_STRATEGY_H