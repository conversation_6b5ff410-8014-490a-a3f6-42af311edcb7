#include "cps_common.h"
#include "cps_msg.h"
#include "cps_image_manage.h"
#include "cps_cluster_manage.h"
#include "cps_vsm_manage.h"
#include "key_operate.h"
#include "message.h"
#include "fwlog.h"
#include "cjson.h"
static char* g_host_ip = NULL;
static char* g_host_name = NULL;

static void free_net_config(net_config_t* cfg);
static int cps_vsm_route_del(char *vsm_id, net_config_t *cfg);
static int cps_vsm_route_add(char *vsm_id, net_config_t *cfg);
/* 静态函数声明 */
static int parse_and_insert_vsm_manage(cJSON *result, cJSON *extensions, const char *request_id);
static int parse_and_insert_interface_info(cJSON *result, cJSON *interfaceInfo);
static int parse_and_insert_routes(const char *vsm_id, cJSON *interfaceInfo);
static int insert_routes_for_interface(const char *vsm_id, const char *interface_name, 
                                      cJSON *routes, int route_type);
static const char* get_json_string(cJSON *obj, const char *key, const char *default_value);
static int get_json_int(cJSON *obj, const char *key, int default_value);
/* 将 JSON 中的字符串复制为新分配内存的 C 字符串，返回 NULL 表示为空 */
static char* dup_cjson_string(cJSON* obj, const char* key) {
    cJSON* item = cJSON_GetObjectItem(obj, key);
    if (item && cJSON_IsString(item) && item->valuestring) {
        size_t len = strlen(item->valuestring);
        char* buf = (char*)malloc(len + 1);
        if (!buf) return NULL;
        memcpy(buf, item->valuestring, len + 1);
        return buf;
    }
    return NULL;
}

int  update_vsm_state(int opt_state, int run_state, char* match_key, char* match_val) {
    unsigned int ret = ERR_NONE;
    int i = 0;
    char *sql = NULL;
    sqlite3_stmt *stmt = NULL;

    if (match_key == NULL || match_val == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    if ((opt_state >= STATE_MAX) || (run_state > VSM_STATE_RESTART)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm state param error!");
        ret = ERR_PARAM;
        goto out;
    }

    if ((opt_state != -1) && (run_state != -1)) {
        if (strcmp(match_key, "vsm_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set opt_state=?,run_state=? where vsm_id=?;");

        }else if (strcmp(match_key, "request_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set opt_state=?,run_state=? where request_id=?;");
        }
        ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sqlite prepare failed");
            goto out;
        }

        sqlite3_bind_int(stmt, 1, opt_state);
        sqlite3_bind_int(stmt, 2, run_state);
        sqlite3_bind_text(stmt, 3, match_val, -1, SQLITE_STATIC);
    }else if (opt_state == -1) {
        if (strcmp(match_key, "vsm_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set run_state=? where vsm_id=?;");
        }else if (strcmp(match_key, "request_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set run_state=? where request_id=?;");
        }
        ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sqlite prepare failed");
            ret = ERR_SQL_OPT;
            goto out;
        }

        sqlite3_bind_int(stmt, 1, run_state);
        sqlite3_bind_text(stmt, 2, match_val, -1, SQLITE_STATIC);
    }else if (run_state == -1) {
        if (strcmp(match_key, "vsm_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set opt_state=? where vsm_id=?;");
        }else if (strcmp(match_key, "request_id") == 0) {
            sql = sqlite3_mprintf("update vsm_manage set opt_state=? where request_id=?;");
        }
        ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sqlite prepare failed");
            ret = ERR_SQL_OPT;
            goto out;
        }

        sqlite3_bind_int(stmt, 1, opt_state);
        sqlite3_bind_text(stmt, 2, match_val, -1, SQLITE_STATIC);
    }else {
        DEBUG_CPS_CLI(ERR_DEBUG, "param error!");
        ret = ERR_PARAM;
        goto out;
    }

    ret = sqlite3_step(stmt);
    if (ret != SQLITE_DONE) {
        for (i = 0; i < 4; i++) {
            usleep(500000);
            if ((ret = sqlite3_step(stmt)) == SQLITE_DONE) {
                break ;
            }
        }
    }
    ret = (ret == SQLITE_DONE)? ERR_NONE : ret;
    API_CHECK_FUNC(ret, "sqlite3_step");

out:
    if (sql)
        sqlite3_free(sql);
    if (stmt)
        sqlite3_reset(stmt);
    if (stmt)
        sqlite3_finalize(stmt);
    ////close_cps_db();
    return ret;
}

int vsm_del_from_device(char *device_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int dev_status = 0;
    char vsm_ipv4[16] = {0};
    char vsm_ipv6[64] = {0};
    char tenant_name[64] = {0};
    char tmp[8] = {0};
    char ip[64] = {0};
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (device_name == NULL)
        return ERR_PARAM;

    //获取设备连接状态
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "device_name", device_name, "device_status", tmp, sizeof(tmp), false);
    dev_status = atoi(tmp);

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select vsm_id from %s where host_name = \'%s\'", CPS_VSM_MANAGE_TABLE, device_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if ((ret != SQLITE_OK)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
		ret = ERR_SQL_OPT;
        goto out;
	}

    //判断虚拟机是否被集群引用
    if (dev_status != DEV_STATUS_DROP) {
        for (i = 0; i < nrow; i++) {
            memset(tmp, 0, sizeof(tmp));
            ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", result[i + 1], "ref_flag", tmp, sizeof(tmp), false);
            API_CHECK_FUNC(ret, "cps_get_data_from_db");
            DEBUG_CPS_CLI(COMMON_DEBUG, "vsm_id[%s] ref_flag:%d\n", result[i + 1], atoi(tmp));
            if (atoi(tmp)) {
                ret = ERR_VSM_OPT;
                goto out;
            }
        }
    }

    memset(ip, 0, sizeof(ip));
    ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "device_name", device_name, "ip", ip, sizeof(ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = cps_del_data_from_db(CPS_SHARE_VSM_MAX_TABLE, "host_name=\'%s\'", device_name);
    API_CHECK_FUNC(ret, "cps_delete_data_from_db");

    for (i = 0; i < nrow; i++) {
        //获取vsm_ipv4
        memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", result[i + 1], "vsm_ipv4", vsm_ipv4, sizeof(vsm_ipv4), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //获取vsm_ipv6
        memset(vsm_ipv6, 0, sizeof(vsm_ipv6));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", result[i + 1], "vsm_ipv6", vsm_ipv6, sizeof(vsm_ipv6), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //获取租户名称
        memset(tenant_name, 0, sizeof(tenant_name));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", result[i + 1], "tenant_name", tenant_name, sizeof(tenant_name), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //删除ip
        if (strlen(vsm_ipv4) >0 ) {
            ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv4);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }

        if (strlen(vsm_ipv6) >0 ) {
            ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv6);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }

        //更新虚拟机管理数据库
        ret = cps_del_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id=\'%s\'", result[i + 1]);
        API_CHECK_FUNC(ret, "cps_delete_data_from_db");

        //集群维护虚拟机
        ret = cluster_vsm_delete(result[i + 1], true);
    }
    //删除设备对应的集群,服务数据
    ret = cluster_del_from_device(ip);
    API_CHECK_FUNC(ret, "cluster_del_from_device");

    ret = cps_del_data_from_db(CPS_CLUSTER_HOST_TABLE, "host_ip=\'%s\'", ip);
    API_CHECK_FUNC(ret, "cps_del_data_from_db");
out:
    if(result){
            sqlite3_free_table(result);
    }
    return ret;
}

void vsm_state_maintan(char *vsm_id, int run_state, int opt_state, char *cluster_state)
{
    int ret = 0;
    int nrow = 0, ncol = 0;
	char sql[SQL_LEN_MAX] = {0};
    char str[64] = {0};
    char **result = NULL;

    if (vsm_id == NULL)
        return ;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select run_state,opt_state from %s where vsm_id = \'%s\'", CPS_VSM_MANAGE_TABLE, vsm_id);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
	if ((ret != SQLITE_OK) || (nrow <= 0)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        goto out;
	}

    memset(str, 0, sizeof(str));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "cluster_status", str, sizeof(str), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(cluster_state, str)) {
		DEBUG_CPS_CLI(COMMON_DEBUG, "cluster_state:%s %s\n", cluster_state, str);
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "cluster_status=\'%s\' where vsm_id=\'%s\'", cluster_state, vsm_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

    if (run_state != atoi(result[ncol])) {
        ret = update_vsm_state(-1, run_state, "vsm_id", vsm_id);
        API_CHECK_FUNC(ret, "update_vsm_state");
    }

    #if 1
    if (opt_state != atoi(result[ncol + 1])) {
        ret = update_vsm_state(opt_state, -1, "vsm_id", vsm_id);
        API_CHECK_FUNC(ret, "update_vsm_opt_state_by_vsmid");
    }
    #endif

out:
    if(result){
            sqlite3_free_table(result);
    }
    //close_cps_db();
    return ;
}

/**
 * @brief 解析从服务器拉取的VSM信息并存入数据库
 * 
 * @param data JSON数据字符串
 * @param requestId 请求ID
 * @return int 成功返回0，失败返回错误码
 */
int pull_vsminfo_callback(char *data, char *requestId)
{
    int ret = 0;
    cJSON *root = NULL;
    cJSON *status = NULL;
    cJSON *request_id = NULL;
    cJSON *result = NULL;
    cJSON *extensions = NULL;
    cJSON *interfaceInfo = NULL;
    cJSON *interface_item = NULL;
    cJSON *staticRouteV4 = NULL;
    cJSON *staticRouteV6 = NULL;
    cJSON *route_item = NULL;
    
    /* 输入参数验证 */
    if (data == NULL || requestId == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid input parameters");
        return ERR_PARAM;
    }

    /* 解析JSON数据 */
    root = cJSON_Parse(data);
    if (!root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to parse JSON data");
        return ERR_PARAM;
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "resp data:%s\r\n", data);

    /* 检查状态码 */
    status = cJSON_GetObjectItem(root, "status");
    if (status && status->valueint != 200) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Server returned error status: %d", status->valueint);
        ret = status->valueint;
        goto out;
    }

    /* 验证请求ID */
    request_id = cJSON_GetObjectItem(root, "requestId");
    if (!request_id || !request_id->valuestring) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Missing requestId in response");
        ret = ERR_PARAM;
        goto out;
    }

    if (strcmp(requestId, request_id->valuestring) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "RequestId mismatch: expected[%s] received[%s]", 
                     requestId, request_id->valuestring);
        ret = ERR_REQUEST_ID;
        goto out;
    }

    /* 获取result对象 */
    result = cJSON_GetObjectItem(root, "result");
    if (!result) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Missing result in response");
        ret = ERR_PARAM;
        goto out;
    }

    /* 获取extensions对象 */
    extensions = cJSON_GetObjectItem(result, "extensions");
    if (!extensions) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Missing extensions in result");
        ret = ERR_PARAM;
        goto out;
    }
    /* 解析并插入VSM管理表数据 */
    ret = parse_and_insert_vsm_manage(result, extensions, request_id->valuestring);
    if (ret != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert vsm_manage data: %d", ret);
        goto out;
    }

    /* 解析并插入网卡信息 */
    interfaceInfo = cJSON_GetObjectItem(extensions, "interfaceInfo");
    if (interfaceInfo && cJSON_IsArray(interfaceInfo)) {
        ret = parse_and_insert_interface_info(result, interfaceInfo);
        if (ret != 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert interface info: %d", ret);
            goto out;
        }
        const char *vsm_id = get_json_string(result, "id", "");
        ret = parse_and_insert_routes(vsm_id, interfaceInfo);
        if (ret != 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert route info: %d", ret);
            goto out;
        }
    }
    DEBUG_CPS_CLI(COMMON_DEBUG, "Successfully processed VSM info for requestId: %s", requestId);

out:
    if (root) {
        cJSON_Delete(root);
    }
    return ret;
}

/**
 * @brief 解析并插入VSM管理表数据
 */
static int parse_and_insert_vsm_manage(cJSON *result, cJSON *extensions, const char *request_id)
{
    int ret = 0;
    cJSON *item = NULL;
    
    /* 提取基本字段 */
    const char *vsm_id = get_json_string(result, "id", "");
    const char *version = get_json_string(result, "version", "");
    const char *ip = get_json_string(result, "ip", "");
    const char *mask = get_json_string(result, "mask", "");
    const char *gateway = get_json_string(result, "gateway", "");
    const char *digest = get_json_string(result, "digest", "");
    int communication = get_json_int(result, "communication", 0);
    
    /* 提取扩展字段 */
    const char *ipv6 = get_json_string(extensions, "ipV6", "");
    const char *maskv6 = get_json_string(extensions, "maskV6", "");
    const char *gatewayv6 = get_json_string(extensions, "gatewayV6", "");
    int cpu_model = get_json_int(extensions, "cpuModel", 0);
    int vsm_type = get_json_int(extensions, "vsmType", 1);
    int create_spec = get_json_int(extensions, "createSpec", 1);
    const char *image_ver = get_json_string(extensions, "imageVer", "");
    const char *image_dig = get_json_string(extensions, "imageDig", "");
    const char *create_time = get_json_string(extensions, "createTime", "");
    const char *vsm_name = get_json_string(extensions, "vsmName", "");
    int opt_status = get_json_int(extensions, "optStatus", 0);
    int run_status = get_json_int(extensions, "runStatus", 1);

    /* 检查是否已存在该VSM */
    if (cps_get_num_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id='%s'", vsm_id) > 0) {
        /* 更新现有记录 
        DEBUG_CPS_CLI(COMMON_DEBUG, "VSM %s already exists, updating...", vsm_id);
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE,
            "request_id='%s',host_name='%s',host_ip='%s',vsm_type=%d,vsm_name='%s',"
            "run_state=%d,opt_state=%d,cpu_model=%d,create_time='%s',create_spec=%d,"
            "image_ver='%s',image_dig='%s' where vsm_id='%s'",
            request_id, g_host_name ? g_host_name : "", g_host_ip ? g_host_ip : "",
            vsm_type, vsm_name, run_status, opt_status, cpu_model, create_time,
            create_spec, image_ver, image_dig, vsm_id);
        */
    } else {
        /* 插入新记录 */
        ret = cps_insert_data_to_db(CPS_VSM_MANAGE_TABLE,
            "(vsm_id,request_id,host_name,host_ip,vsm_type,vsm_name,run_state,opt_state,"
            "cpu_model,create_time,create_spec,image_ver,image_dig) "
            "values('%s','%s','%s','%s',%d,'%s',%d,%d,%d,'%s',%d,'%s','%s')",
            vsm_id, request_id, g_host_name ? g_host_name : "", g_host_ip ? g_host_ip : "",
            vsm_type, vsm_name, run_status, opt_status, cpu_model, create_time,
            create_spec, image_ver, image_dig);
    }

    return ret;
}

/**
 * @brief 解析并插入网卡信息
 */
static int parse_and_insert_interface_info(cJSON *result, cJSON *interfaceInfo)
{
    int ret = 0;
    int array_size = cJSON_GetArraySize(interfaceInfo);
    const char *vsm_id = get_json_string(result, "id", "");
    
    if (strlen(vsm_id) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "VSM ID is empty");
        return ERR_PARAM;
    }

    /* 检查VSM是否已存在网卡信息，如果存在则跳过插入 */
    int existing_interface_count = cps_get_num_from_db("vsm_ip_info", "vsm_id='%s'", vsm_id);
    if (existing_interface_count > 0) {
        //DEBUG_CPS_CLI(COMMON_DEBUG, "VSM %s already has interface info, skipping insertion", vsm_id);
        return 0;
    }

    //DEBUG_CPS_CLI(COMMON_DEBUG, "Inserting interface info for new VSM %s", vsm_id);

    /* 遍历网卡信息数组 */
    for (int i = 0; i < array_size; i++) {
        cJSON *interface_item = cJSON_GetArrayItem(interfaceInfo, i);
        if (!interface_item) continue;

        /* 提取网卡信息 */
        const char *interface_name = get_json_string(interface_item, "interfaceName", "");
        const char *bridge_name = get_json_string(interface_item, "brg", "");
        const char *ovs_instance = get_json_string(interface_item, "ovsInstance", "");
        int bind_type = get_json_int(interface_item, "bindType", 0);
        int vlan_id = get_json_int(interface_item, "vlan", 0);
        int is_manage = get_json_int(interface_item, "manage", 1);
        int is_enable = get_json_int(interface_item, "isEnable", 1);
        int ping = get_json_int(interface_item, "ping", 1);
        
        const char *ipv4 = get_json_string(interface_item, "ipv4", "");
        const char *maskv4 = get_json_string(interface_item, "maskv4", "");
        const char *gatewayv4 = get_json_string(interface_item, "gatewayv4", "");
        const char *ipv6 = get_json_string(interface_item, "ipv6", "");
        const char *maskv6 = get_json_string(interface_item, "maskv6", "");
        const char *gatewayv6 = get_json_string(interface_item, "gatewayv6", "");

        /* 确定桥接口名称 */
        const char *bridge_interface = (bind_type == 1) ? ovs_instance : bridge_name;

        /* 插入网卡信息 */
        ret = cps_insert_data_to_db("vsm_ip_info",
            "(vsm_id,interface_name,vsm_ipv4,vsm_ipv6,vsm_maskv4,vsm_maskv6,"
            "vsm_gatewayv4,vsm_gatewayv6,bindType,is_enabled,manage,bridge_interface,"
            "vlan_id,ping) values('%s','%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,'%s',%d,%d)",
            vsm_id, interface_name, ipv4, ipv6, maskv4, maskv6, gatewayv4, gatewayv6,
            bind_type, is_enable, is_manage, bridge_interface, 
            (vlan_id > 0) ? vlan_id : 0, ping);

        if (ret != 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert interface info for %s", interface_name);
            continue;
        }
    }

    return 0;
}

/**
 * @brief 解析并插入路由信息
 */
static int parse_and_insert_routes(const char *vsm_id, cJSON *interfaceInfo)
{
    int ret = 0;
    int array_size = cJSON_GetArraySize(interfaceInfo);
    
    if (strlen(vsm_id) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "VSM ID is empty");
        return ERR_PARAM;
    }

   

    //DEBUG_CPS_CLI(COMMON_DEBUG, "Inserting route info for new VSM %s", vsm_id);

    /* 遍历网卡信息数组 */
    for (int i = 0; i < array_size; i++) {
        cJSON *interface_item = cJSON_GetArrayItem(interfaceInfo, i);
        if (!interface_item) continue;

        const char *interface_name = get_json_string(interface_item, "interfaceName", "");
        if (strlen(interface_name) == 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "Skipping interface with empty name");
            continue;
        }
        /* 检查VSM的网卡及interface_name是否已存在路由信息，如果存在则跳过插入 */
        int existing_route_count = cps_get_num_from_db("tb_vsm_route", "vsmId='%s' and interface_name='%s'", vsm_id, interface_name );
        if (existing_route_count > 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "VSM %s already has route info, skipping insertion", vsm_id);
            continue;
        }
        /* 处理IPv4静态路由 */
        cJSON *staticRouteV4 = cJSON_GetObjectItem(interface_item, "staticRouteV4");
        if (staticRouteV4 && cJSON_IsArray(staticRouteV4)) {
            ret = insert_routes_for_interface(vsm_id, interface_name, staticRouteV4, 1);
            if (ret != 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert IPv4 routes for %s", interface_name);
            }
        }
        /* 处理IPv6静态路由 */
        cJSON *staticRouteV6 = cJSON_GetObjectItem(interface_item, "staticRouteV6");
        if (staticRouteV6 && cJSON_IsArray(staticRouteV6)) {
            ret = insert_routes_for_interface(vsm_id, interface_name, staticRouteV6, 2);
            if (ret != 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert IPv6 routes for %s", interface_name);
            }
        }
    }

    return 0;
}

/**
 * @brief 为指定网卡插入路由信息
 */
static int insert_routes_for_interface(const char *vsm_id, const char *interface_name, 
                                      cJSON *routes, int route_type)
{
    int ret = 0;
    int route_count = cJSON_GetArraySize(routes);

    for (int i = 0; i < route_count; i++) {
        cJSON *route_item = cJSON_GetArrayItem(routes, i);
        if (!route_item) continue;

        const char *dest = get_json_string(route_item, "dest", "");
        const char *next = get_json_string(route_item, "next", "");

        if (strlen(dest) == 0 || strlen(next) == 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "Skipping invalid route: dest=%s, next=%s", dest, next);
            continue;
        }

        ret = cps_insert_data_to_db("tb_vsm_route",
            "(vsmId,interface_name,route_type,dest,next) values('%s','%s',%d,'%s','%s')",
            vsm_id, interface_name, route_type, dest, next);

        if (ret != 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert route: %s -> %s", dest, next);
        } else {
            DEBUG_CPS_CLI(COMMON_DEBUG, "Inserted route: %s -> %s via %s", dest, next, interface_name);
        }
    }

    return 0;
}

/**
 * @brief 安全获取JSON字符串值
 */
static const char* get_json_string(cJSON *obj, const char *key, const char *default_value)
{
    cJSON *item = cJSON_GetObjectItem(obj, key);
    if (item && cJSON_IsString(item) && item->valuestring) {
        return item->valuestring;
    }
    return default_value;
}

/**
 * @brief 安全获取JSON整数值
 */
static int get_json_int(cJSON *obj, const char *key, int default_value)
{
    cJSON *item = cJSON_GetObjectItem(obj, key);
    if (item && cJSON_IsNumber(item)) {
        return item->valueint;
    }
    return default_value;
}

/* 解析 interface_json 到 net_config_t 结构体（含数组）*/
int parse_interface_json(const char* json, net_config_t* out_cfg) {
    if (!json || !out_cfg) return ERR_PARAM;
    memset(out_cfg, 0, sizeof(*out_cfg));

    cJSON* root = cJSON_Parse(json);
    if (!root) return ERR_CJSON;

    cJSON* interface_arr = cJSON_GetObjectItem(root, "interface");
    if (!interface_arr || !cJSON_IsArray(interface_arr)) {
        cJSON_Delete(root);
        return ERR_PARAM;
    }

    int count = cJSON_GetArraySize(interface_arr);
    if (count <= 0) {
        cJSON_Delete(root);
        return ERR_PARAM;
    }
    interface_cfg_t* list = (interface_cfg_t*)calloc((size_t)count, sizeof(interface_cfg_t));
    if (!list) {
        cJSON_Delete(root);
        return ERR_MALLOC;
    }

    for (int i = 0; i < count; i++) {
        cJSON* itf = cJSON_GetArrayItem(interface_arr, i);
        if (!itf || !cJSON_IsObject(itf)) {
            continue;
        }
        interface_cfg_t* cfg = &list[i];
        cfg->interface_name = dup_cjson_string(itf, "interfaceName");
        cfg->bindType = get_json_int(itf, "bindType", 0);
        cfg->ovs_instance = dup_cjson_string(itf, "ovsInstance");
        cfg->bridge = dup_cjson_string(itf, "brg");
        cfg->vlan = get_json_int(itf, "vlan", 0);
        cfg->manage = get_json_int(itf, "manage", 0);
        cfg->is_enable = get_json_int(itf, "isEnable", 1);
        cfg->ipv4 = dup_cjson_string(itf, "ipv4");
        cfg->maskv4 = dup_cjson_string(itf, "maskv4");
        cfg->gatewayv4 = dup_cjson_string(itf, "gatewayv4");
        cfg->ipv6 = dup_cjson_string(itf, "ipv6");
        cfg->maskv6 = dup_cjson_string(itf, "maskv6");
        cfg->gatewayv6 = dup_cjson_string(itf, "gatewayv6");
        cfg->ping = get_json_int(itf, "ping", 1);

        // routes v4
        cJSON* r4 = cJSON_GetObjectItem(itf, "staticRoutev4");
        if (r4 && cJSON_IsArray(r4)) {
            int n = cJSON_GetArraySize(r4);
            if (n > 0) {
                cfg->static_route_v4 = (route_entry_t*)calloc((size_t)n, sizeof(route_entry_t));
                if (!cfg->static_route_v4) { cJSON_Delete(root); free(list); return ERR_MALLOC; }
                cfg->static_route_v4_count = n;
                for (int j = 0; j < n; j++) {
                    cJSON* r = cJSON_GetArrayItem(r4, j);
                    if (!r) continue;
                    cfg->static_route_v4[j].dest = dup_cjson_string(r, "dest");
                    cfg->static_route_v4[j].next = dup_cjson_string(r, "next");
                }
            }
        }

        // routes v6
        cJSON* r6 = cJSON_GetObjectItem(itf, "staticRoutev6");
        if (r6 && cJSON_IsArray(r6)) {
            int n = cJSON_GetArraySize(r6);
            if (n > 0) {
                cfg->static_route_v6 = (route_entry_t*)calloc((size_t)n, sizeof(route_entry_t));
                if (!cfg->static_route_v6) { cJSON_Delete(root); free(list); return ERR_MALLOC; }
                cfg->static_route_v6_count = n;
                for (int j = 0; j < n; j++) {
                    cJSON* r = cJSON_GetArrayItem(r6, j);
                    if (!r) continue;
                    cfg->static_route_v6[j].dest = dup_cjson_string(r, "dest");
                    cfg->static_route_v6[j].next = dup_cjson_string(r, "next");
                }
            }
        }
    }
    //深拷贝interface_array
    cJSON *interface_array_copy = cJSON_Duplicate(interface_arr, 1);
    out_cfg->interface_array = interface_array_copy;
    out_cfg->interfaces = list;
    out_cfg->interface_count = count;
    cJSON_Delete(root);
    return 0;
}

static void free_net_config(net_config_t* cfg) {
    if (!cfg) return;
    if (cfg->interfaces) {
        for (int i = 0; i < cfg->interface_count; i++) {
            interface_cfg_t* c = &cfg->interfaces[i];
            free(c->interface_name);
            free(c->ovs_instance);
            free(c->bridge);
            free(c->ipv4);
            free(c->maskv4);
            free(c->gatewayv4);
            free(c->ipv6);
            free(c->maskv6);
            free(c->gatewayv6);
            if (c->static_route_v4) {
                for (int j = 0; j < c->static_route_v4_count; j++) {
                    free(c->static_route_v4[j].dest);
                    free(c->static_route_v4[j].next);
                }
                free(c->static_route_v4);
            }
            if (c->static_route_v6) {
                for (int j = 0; j < c->static_route_v6_count; j++) {
                    free(c->static_route_v6[j].dest);
                    free(c->static_route_v6[j].next);
                }
                free(c->static_route_v6);
            }
        }
        free(cfg->interfaces);
    }
    memset(cfg, 0, sizeof(*cfg));
}

void vsm_info_pull(char *vsm_id,char * host_ip,char* host_name){ 
    int ret = 0;
    int data_len = 0, body_len = 0;
     int sign_len = 0;
    char sign_data[1024] = {0};
    int nrow = 0, ncol = 0;
	char sql[SQL_LEN_MAX] = {0};
    char str[64] = {0};
    char **result = NULL;
    char data[DATA_MAX_LEN] = {0};
    char request_id[16] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    g_host_ip                        = host_ip;
    g_host_name                        = host_name;

    if (vsm_id == NULL)
        return ;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select count(*) from %s where vsm_id = \'%s\'", CPS_VSM_MANAGE_TABLE, vsm_id);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        goto out;
	}

    if(atoi(result[ncol]) ==0){
        // 生成8位随机数作为requestid
        ret = cps_generate_random(request_id, sizeof(request_id));
        API_CHECK_FUNC(ret, "cps_generate_random");

        // 组获取虚拟机详细信息接口调用的json数据,即传入参数
        ret = get_vsm_info_data_comp(request_id, vsm_id, data, sizeof(data));
        API_CHECK_FUNC(ret, "get_vsm_info_data_comp");
        data_len = strlen(data);
        DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\n", data);

        //对数据进行base64编码
        data_base64 = (unsigned char *)malloc(strlen(data) * 2);
        if (!data_base64) {
            ret = ERR_MALLOC;
            goto out;
        }
        base64_encode(data, data_base64, data_len);

        //对json数据进行签名
        ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
        API_CHECK_FUNC(ret, "sign_with_internal_key");

        //组tcp传输body数据
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_INFO);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, pull_vsminfo_callback, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ;
}

int cps_vsm_info_get(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    char sign_data[1024] = {0};
	char data[DATA_MAX_LEN] = {0};
    char host_ip[64] = {0};
    char request_id[16] = {0};
    char *tenant_name = argv[5];
    char *vsm_id = argv[7];
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);

    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取宿主机ip
    memset(host_ip, 0, sizeof(host_ip));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //组获取虚拟机详细信息接口调用的json数据,即传入参数
    ret = get_vsm_info_data_comp(request_id, vsm_id, data, sizeof(data));
    API_CHECK_FUNC(ret, "get_vsm_info_data_comp");
    data_len = strlen(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\n", data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_INFO);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, get_vsminfo_callback, request_id);
    API_CHECK_FUNC(ret, "cps_request_send");

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int vsm_del_all(char *tenant_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select vsm_id from %s where tenant_name = \'%s\'", CPS_VSM_MANAGE_TABLE, tenant_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
	if ((ret != SQLITE_OK)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
		ret = ERR_SQL_OPT;
        goto out;
	}

    for (i  = 0; i < nrow; i++) {
        ret = cps_vsm_opt_proc("destroy", tenant_name, result[i + 1]);
        API_CHECK_FUNC(ret, "cps_vsm_opt_proc");
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int vsm_del_proc(char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    char vsm_ipv4[16] = {0};
    char vsm_ipv6[64] = {0};
    char vsm_id[64] = {0};
    char tmp[8];

    //维护共享虚拟机数
    ret = vsm_share_num_maintan(request_id, VSM_OPT_DEL, state);
    API_CHECK_FUNC(ret, "cps_vsm_share_num_maintan");

    if ((state == 0) || ((strstr(resp_msg, "Not Exists") != NULL || strstr(resp_msg, "不存在") != NULL))) {
        //获取vsm_id
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "vsm_id", vsm_id, sizeof(vsm_id), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        //获取vsm_ipv4
        //查询vsm_ip_info表中vsm_id对应的ipv4,ipv6,interface_name
        char sql[256] = {0};
        char **result = NULL;
        int nrow = 0, ncol = 0;
        sprintf(sql, "select ipv4,ipv6,interface_name from %s where vsm_id = \'%s\'", CPS_VSM_IP_INFO_TABLE, vsm_id);
        sqlite3_busy_handler(cps_db, callback_db, NULL);
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
        if (ret != SQLITE_OK) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }

        for (int i = 0; i < nrow; i++) {
            strcpy(vsm_ipv4, result[(i+1)*ncol]);
            strcpy(vsm_ipv6, result[(i+1)*ncol+1]);
            //更新虚拟机管理数据库
            if (strlen(vsm_ipv4) > 0) {
                ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv4);
                API_CHECK_FUNC(ret, "cps_del_data_from_db");
            }

            if (strlen(vsm_ipv6) > 0) {
                ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv6);
                API_CHECK_FUNC(ret, "cps_del_data_from_db");
            }
        }
        //删除vsm_manage表数据
        ret = cps_del_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id=\'%s\'", vsm_id);
        API_CHECK_FUNC(ret, "cps_delete_data_from_db");

        //删除vsm_ip_info表数据
        ret = cps_del_data_from_db(CPS_VSM_IP_INFO_TABLE, "vsm_id=\'%s\'", vsm_id);
        API_CHECK_FUNC(ret, "cps_delete_data_from_db");
        //删除升级记录
        ret = cps_del_data_from_db(CPS_VSM_UPGRADE_TABLE, "vsm_id=\'%s\'", vsm_id);
        API_CHECK_FUNC(ret, "cps_delete_data_from_db");

        //集群维护
        ret = cluster_vsm_delete(vsm_id, false);
    }else {
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", STATE_CLOSED, request_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

out:
    return ret;
}

int vsm_modify_spec_proc(char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    int spec_cur = 0, spec_dil = 0;
    int cpu_cur = 0, cpu_dil = 0;
    char tmp[8] = {0};
    char *str = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "modify vsm spec resp msg:%s\r\n", resp_msg);
    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", STATE_CLOSED, request_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");
    if (state)
        goto out;

    if ((str = strrchr(resp_msg, '-')) != NULL) {
        spec_dil = atoi(str + 1);
        DEBUG_CPS_CLI(COMMON_DEBUG, "modify spec:%d, str:%s\r\n", spec_dil, str);
        if (spec_dil == 0) {
            DEBUG_CPS_CLI(ERR_DEBUG, "resp msg error!\r\n");
            return ERR_UNKNOWN;
        }
    }
    //获取扩容规格对应cpu
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", &spec_dil, "cpu", tmp, sizeof(tmp), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cpu_dil = atoi(tmp);


    //获取当前规格cpu
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "create_spec", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    spec_cur = atoi(tmp);

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", &spec_cur, "cpu", tmp, sizeof(tmp), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cpu_cur = atoi(tmp);

    DEBUG_CPS_CLI(COMMON_DEBUG, "cpu_cur:%d, cpu_dil:%d\r\n", cpu_cur, cpu_dil);
    if (cpu_cur != cpu_dil) {
        //删除一台当前cpu规格共享虚拟机
        ret = vsm_share_num_maintan(request_id, VSM_OPT_DEL, 0);
        API_CHECK_FUNC(ret, "vsm_share_num_maintan");

        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "create_spec=%d where request_id=\'%s\'", spec_dil, request_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");

        //增加一台扩容规格cpu共享虚拟机
        ret = vsm_share_num_maintan(request_id, VSM_OPT_CREATE, 0);
        API_CHECK_FUNC(ret, "vsm_share_num_maintan");
    }else {
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "create_spec=%d where request_id=\'%s\'", spec_dil, request_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "update_flag=0 where request_id=\'%s\'", request_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");
out:
    return ret;
}

//  ipv4地址是否在范围内
int vsm_opt_tenant_ipv4_check(char *ipv4, char *start_ipv4, char *end_ipv4)
{
    struct in_addr ipv4_addr, start_addr, end_addr;
    if (inet_pton(AF_INET, ipv4, &ipv4_addr) != 1 ||
        inet_pton(AF_INET, start_ipv4, &start_addr) != 1 ||
        inet_pton(AF_INET, end_ipv4, &end_addr) != 1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "ipv4 str2in_addr fail\n");
        return -1;
    }

    if ((ntohl(ipv4_addr.s_addr) >= ntohl(start_addr.s_addr)) && (ntohl(ipv4_addr.s_addr) <= ntohl(end_addr.s_addr)))
        return 0;

    return -1;
}

// ipv6地址是否在范围内
int vsm_opt_tenant_ipv6_check(char *ipv6, char *start_ipv6, char *end_ipv6)
{
    struct in6_addr ipv6_addr, start_addr, end_addr;
    if (inet_pton(AF_INET6, ipv6, &ipv6_addr) != 1 ||
        inet_pton(AF_INET6, start_ipv6, &start_addr) != 1 ||
        inet_pton(AF_INET6, end_ipv6, &end_addr) != 1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "ipv6 str2in6_addr fail\n");
        return -1;
    }

    int cmp_start = memcmp(&ipv6_addr, &start_addr, sizeof(struct in6_addr));
    int cmp_end = memcmp(&ipv6_addr, &end_addr, sizeof(struct in6_addr));

    if ((cmp_start >= 0) && (cmp_end <= 0))
        return 0;

    return -1;
}

// 判断虚拟机ip 是否在租户地址池范围中。
int vsm_opt_tenant_ip_check(char *vsm_id, char *tenant_name,int use_argv_ip,char*ip_v4,char*ip_v6,char*interface_name)
{
    if (vsm_id == NULL || tenant_name == NULL)  return ERR_PARAM;

    unsigned int ret = ERR_ADDR_RANGE;
    char vsm_ipv4[16] = {0};
    char vsm_ipv6[40] = {0};
    char vsm_ip_range[512] = {0};
    char *token = NULL;
    char **result = NULL;
    int nrow = 0, ncol = 0;

    if(use_argv_ip){
        if(ip_v4 != NULL && strlen(ip_v4) > 0){
            strcpy(vsm_ipv4, ip_v4);
        }
        if(ip_v6 != NULL && strlen(ip_v6) > 0){
            strcpy(vsm_ipv6, ip_v6);
        }
    }else{
        /*
        char sql[256] = {0};
        sprintf(sql, "select ipv4,ipv6,interface_name from %s where vsm_id = \'%s\'", CPS_VSM_IP_INFO_TABLE, vsm_id);
        sqlite3_busy_handler(cps_db, callback_db, NULL);
        ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
        if(ret != SQLITE_OK){
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "vsm_ipv4", vsm_ipv4, sizeof(vsm_ipv4), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "vsm_ipv6", vsm_ipv6, sizeof(vsm_ipv6), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        // 未设置ip时，可以直接启动
        if ((strlen(vsm_ipv4) <= 0) && (strlen(vsm_ipv6) <= 0)) {
            ret = ERR_NONE;
            goto out;
        }
        */
    }
    
    //获取CPS_TENANT_ADDR_INFO_TABLE中网卡名为interface_name，租户为tenant_name的ip地址池
    char sql[256] = {0};
    sprintf(sql, "select ip_range from %s where interface_name = \'%s\' and tenant_name = \'%s\'", CPS_TENANT_ADDR_INFO_TABLE, interface_name, tenant_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    //获取CPS_TENANT_ADDR_INFO_TABLE中网卡名为interface_name，租户为tenant_name的ip地址池
    for(int i = 0; i < nrow; i++){
        strcpy(vsm_ip_range, result[i + 1]);
    }

    //如果ip地址池为空，则直接返回
    if (strlen(vsm_ip_range) <= 0) {
        ret = ERR_PARAM;
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm_ip_range is empty\n");
        goto out;
    }

    token = strtok(vsm_ip_range, ",");
    while (token != NULL) {
        char *start_ip = token;
        char *end_ip   = strchr(token, '-');
        if (end_ip != NULL) {
            *end_ip = '\0';
            end_ip ++;
        }else
            continue;

        if (start_ip != NULL && end_ip != NULL) {
            if ((strchr(start_ip, ':') != NULL ) && (strlen(vsm_ipv6) > 0))
                ret = vsm_opt_tenant_ipv6_check(vsm_ipv6, start_ip, end_ip);        // ipv6 check
            else if ((strchr(start_ip, '.') != NULL) && (strlen(vsm_ipv4) > 0))
                ret = vsm_opt_tenant_ipv4_check(vsm_ipv4, start_ip, end_ip);        // ipv4 check
            else
                ret = ERR_ADDR_RANGE;

            if (!ret) {
                ret = ERR_NONE;
                goto out;
            } else
                ret =  ERR_ADDR_RANGE;
        }

        token = strtok(NULL, ",");
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int vsm_opt_state_check(char *vsm_id, char *opt, char *tenant_name)
{
    int ret = 0;
    int ref_flag = 0;
    int vsm_state = 0;
    int opt_state = 0;
    char tmp[8];
    char image_addr[128];

    //管理员只能在虚拟机未分配时进行删除操作
    //if ((tenant_name == NULL) && (strcmp(opt, "destroy") != 0))
    //    return ERR_SUPP_OPT;

    //虚拟机不是开启或关闭不允许操作
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "opt_state", tmp, sizeof(tmp), false);
    opt_state = atoi(tmp);
    if (((opt_state != STATE_STARTED) && (opt_state != STATE_CLOSED) && (opt_state != STATE_UNSTART) && (opt_state != STATE_UPGRADE_DONE)) && (strcmp(opt, "destroy") != 0))
        return ERR_SUPP_OPT;


    //集群开启时不允许删除和重置
    memset(tmp, 0, sizeof(tmp));
    memset(image_addr, 0, sizeof(image_addr));
    if ((strcmp(opt, "destroy") == 0) ||
        (strcmp(opt, "netconf") == 0) ||
        (strcmp(opt, "stop") == 0) ||
        (strcmp(opt, "restart") == 0) ||
        (strcmp(opt, "upgrade") == 0) ||
        (strcmp(opt, "image import") == 0)) {
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "ref_flag", tmp, sizeof(tmp), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        ref_flag = atoi(tmp);
        ret = (ref_flag == 1)? ERR_VSM_OPT : 0;
    }else if(strcmp(opt, "export") == 0) {
        //判断是否设置影像上传地址
        ret = cps_get_image_addr(image_addr, sizeof(image_addr));
        API_CHECK_FUNC(ret, "cps_get_image_addr");

        //判断虚拟机是否就绪
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "run_state", tmp, sizeof(tmp), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        vsm_state = atoi(tmp);
        if (vsm_state != VSM_STATE_NORMAL) {
            ret = ERR_VSM_INIT;
        }
    }

    // 存在租户的虚拟机ip不在地址池范围内，不允许操作
    if (tenant_name != NULL && (strcmp(opt, "start") == 0)) {
        //ret = vsm_opt_tenant_ip_check(vsm_id, tenant_name,0,NULL,NULL,NULL);
        //API_CHECK_FUNC(ret, "vsm_opt_tenant_ip_check");
    }

out:
    return ret;
}

int cps_get_shared_vsm_create_num(int cpu, char *host_name, int *created_num, int *create_max)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int cpu_share_max = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (host_name == NULL || created_num == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql)-1, "select created,create_max from %s where cpu=%d and host_name=\'%s\'", CPS_SHARE_VSM_MAX_TABLE, cpu, host_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
		ret = ERR_SQL_OPT;
        goto out;
	}

    if (nrow > 0) {
        *created_num = atoi(result[ncol]);
        *create_max = atoi(result[ncol + 1]);
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int vsm_share_num_maintan(char *request_id, int opt, int state)
{
    unsigned int ret = ERR_NONE;
    int cpu = 0;
    int create_max = 0;
    int num = 0, created_num = 0;
    int flavor = 0;
    char host_name[128];
    char tenant_name[128];
    char tmp[8];

    if (request_id == NULL)
        return ERR_PARAM;

    //判断操作的虚拟机是否为共享模式
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "cpu_model", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (atoi(tmp) == CPU_MODEL_EXCL)
        return ret;

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "create_spec", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    flavor = atoi(tmp);

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", &flavor, "cpu", tmp, sizeof(tmp), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cpu = atoi(tmp);

    memset(host_name, 0, sizeof(host_name));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "host_name", host_name, sizeof(host_name), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = cps_get_shared_vsm_create_num(cpu, host_name, &created_num, &create_max);
    API_CHECK_FUNC(ret, "cps_get_shared_vsm_create_num");
    DEBUG_CPS_CLI(COMMON_DEBUG, "created_num:%d create_max:%d\r\n", created_num, create_max);

    if ((opt == VSM_OPT_CREATE) && (state == 0)) {
        num = cps_get_num_from_db(CPS_SHARE_VSM_MAX_TABLE, "cpu=%d and host_name=\'%s\'", cpu, host_name);
        API_CHECK_FUNC(ret, "cps_get_num_from_db");
        DEBUG_CPS_CLI(COMMON_DEBUG, "num:%d cpu:%d\r\n", num, cpu);
        if (num == 0) {
            ret = cps_insert_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "(host_name,cpu,created,create_max) values(\'%s\',%d,%d,%d)", host_name, cpu, 1, 3);
            API_CHECK_FUNC(ret, "cps_insert_data_to_db");
        }else {
            if (create_max == 0) {
                //若之前没创建过该规格的共享虚拟机或该规格共享虚拟机台数已创建4台，则插入表，且该规格共享虚拟机最多可再创建3台
                ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "create_max=3 where cpu=%d and host_name=\'%s\'", cpu, host_name);
                API_CHECK_FUNC(ret, "cps_update_data_to_db");
            }else {
                //若该规格的共享虚拟机还可以创建，则最多可创建数减1
                ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "create_max=create_max-1 where cpu=%d and host_name=\'%s\'", cpu, host_name);
                API_CHECK_FUNC(ret, "cps_update_data_to_db");
            }
            //已创建加1
            ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "created=created+1 where cpu=%d and host_name=\'%s\'", cpu, host_name);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }
    }else if (((opt == VSM_OPT_CREATE) && (state != 0)) ||
                ((opt == VSM_OPT_DEL) && (state == 0))) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "create_max:%d created_num:%d\r\n", create_max, created_num);
        if (create_max == 3) {
            //若当前该规格可创建共享虚拟机台数为3,则代表已创建完4台，删除后还可创建0台,即该共享cpu核数释放
            ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "create_max=0 where cpu=%d and host_name=\'%s\'", cpu, host_name);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }else {
            //否则可创建台数加1
            ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "create_max=create_max+1 where cpu=%d and host_name=\'%s\'", cpu, host_name);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }
        //已创建减1
        if (created_num == 1) {
            ret = cps_del_data_from_db(CPS_SHARE_VSM_MAX_TABLE, "cpu=%d and host_name=\'%s\'", cpu, host_name);
            API_CHECK_FUNC(ret, "cps_del_data_from_db");
        }else {
            ret = cps_update_data_to_db(CPS_SHARE_VSM_MAX_TABLE, "created=created-1 where cpu=%d and host_name=\'%s\'", cpu, host_name);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }
    }
out:
    return ret;
}

int vsm_create_proc(char *request_id, int state)
{
    int ret = 0;
    char vsm_ipv4[16];
    char vsm_ipv6[64];

    //创建失败则删除数据库记录
    if (state) {
        ret = vsm_share_num_maintan(request_id, VSM_OPT_CREATE, state);
        API_CHECK_FUNC(ret, "vsm_opt_share_num_maintan");

        memset(vsm_ipv4, 0, sizeof(vsm_ipv4));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "vsm_ipv4", vsm_ipv4, sizeof(vsm_ipv4), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strlen(vsm_ipv4)) {
            ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv4);
            API_CHECK_FUNC(ret, "cps_del_data_from_db");
        }

        memset(vsm_ipv6, 0, sizeof(vsm_ipv6));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "vsm_ipv6", vsm_ipv6, sizeof(vsm_ipv6), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strlen(vsm_ipv6)) {
            ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'", vsm_ipv6);
            API_CHECK_FUNC(ret, "cps_del_data_from_db");
        }

        ret = cps_del_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id=\'%s\'", request_id);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");
    }else {
        ret = update_vsm_state(STATE_UNSTART, -1, "request_id", request_id);
        API_CHECK_FUNC(ret, "update_vsm_state");
    }
out:
    return ret;
}

int cps_reset_opt_proc(char *request_id)
{
    int ret = 0;
    char vsm_id[64];

    memset(vsm_id, 0, sizeof(vsm_id));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "vsm_id", vsm_id, sizeof(vsm_id), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //更新ip
    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", STATE_UNSTART, request_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");
out:
    return ret;
}

int vsm_upgrade_proc(char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    int run_state = 0;
    char up_desc[256];
    char pkg_name[128] = {0};
    char pkg_path[256] = {0};
    char image_sign[256];
    char image_version[128];
    char tmp[8] = {0};
    cJSON *root = NULL;
    cJSON *version = NULL;
    cJSON *version_desc = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "vsm upgrade resp msg:%s\r\n", resp_msg);

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "run_state", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    run_state = atoi(tmp);

    if (state == 0) {
        //更新镜像签名值与版本号
        memset(image_version, 0, sizeof(image_version));
        memset(image_sign, 0, sizeof(image_sign));
        memset(up_desc, 0, sizeof(up_desc));
        if ((root = cJSON_Parse(resp_msg)) != NULL) {
            if ((version = cJSON_GetObjectItem(root, "up_version")) != NULL) {
                strncpy(image_version, version->valuestring, sizeof(image_version)-1);
            }
            if ((version_desc = cJSON_GetObjectItem(root, "del_bugs")) != NULL) {
                strncpy(up_desc, version_desc->valuestring, sizeof(up_desc)-1);
            }

            ret = cps_update_data_to_db(CPS_VSM_UPGRADE_TABLE, "up_version=\'%s\',up_desc=\'%s\' where request_id=\'%s\'", image_version, up_desc, request_id);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");

            ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", STATE_UPGRADE_DONE, request_id);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }else {
            ret = cps_get_data_from_db(CPS_VSM_UPGRADE_TABLE, "request_id", request_id, "up_version", image_version, sizeof(image_version), false);
            API_CHECK_FUNC(ret, "cps_get_data_from_db");

            ret = cps_get_data_from_db(CPS_VSM_UPGRADE_TABLE, "request_id", request_id, "image_sign", image_sign, sizeof(image_sign), false);
            API_CHECK_FUNC(ret, "cps_get_data_from_db");

            ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d,image_ver=\'%s\',image_dig=\'%s\' where request_id=\'%s\'", STATE_STARTED, image_version, image_sign, request_id);
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
        }
    }else {
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", (run_state == VSM_STATE_NORMAL || run_state == VSM_STATE_INITIAL)? STATE_STARTED : STATE_CLOSED, request_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }
    ret = cps_update_data_to_db(CPS_VSM_UPGRADE_TABLE, "up_flag=%d,up_result=\'%s\' where request_id=\'%s\'", 1, (state == 0)? gettext("success") : gettext("upgrade failed"), request_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");

    ret = cps_get_data_from_db(CPS_VSM_UPGRADE_TABLE, "request_id", request_id, "pkg_name", pkg_name, sizeof(pkg_name), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
out:
    //操作完成后删除该文件
    if (strlen(pkg_name)) {
        memset(pkg_path, 0, sizeof(pkg_path));
        snprintf(pkg_path, sizeof(pkg_path)-1, "%s/%s", CPS_VSM_IMAGE_PATH, pkg_name);
        unlink(pkg_path);
    }
    if (root)
        cJSON_Delete(root);
    return ret;
}

int asymm_vsm_opt_proc(int sub_type, char *request_id, int state, char *resp_msg)
{
    unsigned int ret = ERR_NONE;
    char tmp[8];
    //const char *action[] = {""};

    switch(sub_type) {
        case CMD_RESTART:
        case CMD_START:
            if (state == 0) {
                memset(tmp, 0, sizeof(tmp));
                ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "request_id", request_id, "run_state", tmp, sizeof(tmp), false);
                API_CHECK_FUNC(ret, "update_vsm_opt_state_by_requestid");

                ret = update_vsm_state(STATE_STARTED, (atoi(tmp) == VSM_STATE_NORMAL)? VSM_STATE_NORMAL : VSM_STATE_INITIAL, "request_id", request_id);
            }else {
                ret = update_vsm_state(STATE_CLOSED, -1, "request_id", request_id);
            }
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
            break;
        case CMD_CREATE:
            ret = vsm_create_proc(request_id, state);
            API_CHECK_FUNC(ret, "vsm_create_proc");
            break;
        case CMD_RESET:
            if (state == 0) {
                ret = cps_reset_opt_proc(request_id);
            }else {
                ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d where request_id=\'%s\'", STATE_CLOSED, request_id);
            }
            API_CHECK_FUNC(ret, "cps_update_data_to_db");
            break;
        case CMD_STOP:
            if (state == 0) {
                ret = update_vsm_state(STATE_CLOSED, VSM_STATE_SHUTDOWN, "request_id", request_id);
            }else {
                ret = update_vsm_state(STATE_STARTED, -1, "request_id", request_id);
            }
            API_CHECK_FUNC(ret, "update_vsm_state");
            break;
        case CMD_DELETE:
            ret = vsm_del_proc(request_id, state, resp_msg);
            API_CHECK_FUNC(ret, "vsm_del_proc");
            break;
        case CMD_MODIFY:
            ret = vsm_modify_spec_proc(request_id, state, resp_msg);
            API_CHECK_FUNC(ret, "vsm_modify_spec_proc");
            break;
        case CMD_UPGRADE:
            ret = vsm_upgrade_proc(request_id, state, resp_msg);
            API_CHECK_FUNC(ret, "vsm_upgrade_proc");
            break;
        case CMD_INIT_DEV:
            if (state) {
                DEBUG_CPS_CLI(ERR_DEBUG, "asymm_init_dev_proc failed! state:%d err_msg:%s\r\n", state, resp_msg);
                state = 1;
            }else{
                state = 0;
            }
            ret = update_vsm_state(STATE_STARTED, state, "request_id", request_id);
            API_CHECK_FUNC(ret, "update_vsm_state");
            break;
        default:
            DEBUG_CPS_CLI(ERR_DEBUG, "command sub_type is incorrect!");
            break;
    }
    if (state != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "asymm proc failed! state:%d err_msg:%s\r\n", state, resp_msg);
    }
out:
    return ret;
}

int cps_vsm_remark_set(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *tenant_name = "";
    char *vsm_id = NULL;
    char *remark = NULL;
    char tenant[128];

    if (argc == 10) {
        tenant_name = argv[5];
        vsm_id = argv[7];
        remark = argv[9];
    }
    else if (argc == 8) {
        vsm_id = argv[5];
        remark = argv[7];
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "cps_vsm_remark_set start\r\n");
    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s Incoming:%s comp fail \n", tenant, tenant_name);
        return ERR_SUPP_OPT;
    }

    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "remark = \'%s\' where vsm_id = \'%s\'", (strcmp(remark, "null") == 0)? "" : remark, vsm_id);
    API_CHECK_FUNC(ret, "cps_update_data_to_db");

out:
    print_errmsg(ret);
    return ret;
}

int cps_vsm_get_cpu_share_max(char *host_name, unsigned int *cpu_max)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
    int cpu_share_max = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (host_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取最大规格的共享模式虚拟机
    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql)-1, "select cpu from %s LEFT JOIN vsm_manage on flavor_info.flavor = vsm_manage.create_spec where vsm_manage.cpu_model = 1 and vsm_manage.host_name = \'%s\' ORDER BY cpu DESC ", CPS_FLAVOR_INFO_TABLE, host_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
		ret = ERR_SQL_OPT;
        goto out;
	}
    if (nrow > 0)
        cpu_share_max = atoi(result[1]);

    *cpu_max = cpu_share_max;
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_vsm_cpu_excl_check(char *host_name, int spec, int cpu_model)
{
    unsigned int ret = ERR_NONE;
    unsigned int cpu_total = 0, mem_total = 0;
    unsigned int cpu_use = 0, mem_use = 0;
    int cpu = 0, mem = 0;
    int vsm_num = 0;
    int create_max = 0;
    int created_num = 0;
	char tmp[8] = {0};
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (host_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    cpu_total = mem_total = cpu_use = mem_use = 0;

    //获取宿主机的资源总和
    ret = cps_get_host_source(cps_db, host_name, &cpu_total, &mem_total);
    API_CHECK_FUNC(ret, "cps_get_host_source");

    //获取宿主机已用资源
    ret = cps_get_host_source_use(cps_db, host_name, &cpu_use, &mem_use, &vsm_num);
    API_CHECK_FUNC(ret, "cps_get_host_source_use");

    //获取最大规格的共享模式虚拟机
    //ret = cps_vsm_get_cpu_share_max(host_name, &cpu_share_max);
    //API_CHECK_FUNC(ret, "cps_vsm_get_cpu_share_max");

    //获取创建规格对应的资源大小
    ret = cps_get_spec_source(cps_db, &cpu, &mem, spec);
    API_CHECK_FUNC(ret, "cps_get_spec_source");

    //租户申请工单减去独占虚拟机cpu-共享cpu最大核数，即当前能创建的最大的独享方案
    DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]cpu_total:%d mem_total:%d cpu_use:%d mem_use:%dcpu_flavor %d mem_flavor %d\r\n",__func__,__LINE__,
                                    cpu_total, mem_total, cpu_use, mem_use, cpu, mem);
    if (cpu_model == CPU_MODEL_EXCL) {
        if (((cpu_total - cpu_use) < cpu) || ((mem_total - mem_use) < mem))
            ret = ERR_VSM_RESOURCE;
    }else {
        //获取该方案可创建的共享虚拟机台数
        memset(tmp, 0, sizeof(tmp));
        ret = cps_get_shared_vsm_create_num(cpu, host_name, &created_num, &create_max);
        API_CHECK_FUNC(ret, "cps_get_shared_vsm_create_num");

        if (((cpu_total - cpu_use < cpu) && (create_max <= 0))  || ((mem_total - mem_use) < mem))
            ret = ERR_VSM_RESOURCE;
    }

out:
    //close_cps_db();
    return ret;
}

int vsm_create_check(char *host_name, int cpu, int *flag)
{
    unsigned int ret = ERR_NONE;
    int create_max = 0, created_num = 0;

    if (host_name == NULL)
        return -1;

    //获取当前cpu对应的规格是否还可创建共享虚拟机
    ret = cps_get_shared_vsm_create_num(cpu, host_name, &created_num, &create_max);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //如果当前规格共享虚拟机可创建台数为3台，则该规格cpu可释放出来用于扩容新规格虚拟机
    if (create_max == 3) {
        if (flag)
            *flag = true;
    }
out:
   return (create_max > 0)? ERR_NONE : -1;
}

int cps_vsm_source_check(char *vsm_id, int spec)
{
    unsigned int ret = ERR_NONE;
    unsigned int rc = ERR_NONE;
    unsigned int created_num = 0, create_max = 0;
    unsigned int cpu_total = 0, mem_total = 0;
    unsigned int cpu_use = 0, mem_use = 0;
    unsigned int cpu_ava = 0, mem_ava = 0;
    unsigned int cpu_cur = 0, mem_cur = 0;
    unsigned int cpu = 0, mem = 0;
    unsigned int cpu_model = 0;
    unsigned int vsm_num = 0;
    int flag = false;
    char tmp[8];
    char host_name[128];

    if (vsm_id == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取虚拟机所属宿主机名称
    memset(host_name, 0, sizeof(host_name));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "host_name", host_name, sizeof(host_name), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");


	//获取虚拟机cpu模式
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "cpu_model", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cpu_model = atoi(tmp);

    cpu_total = mem_total = cpu_use = mem_use = 0;
    //获取宿主机的资源总和
    ret = cps_get_host_source(cps_db, host_name, &cpu_total, &mem_total);
    API_CHECK_FUNC(ret, "cps_get_host_source");

    //获取宿主机已用资源
    ret = cps_get_host_source_use(cps_db, host_name, &cpu_use, &mem_use, &vsm_num);
    API_CHECK_FUNC(ret, "cps_get_host_source_use");

    //判断资源是否足够支持扩容
    cpu_ava = cpu_total - cpu_use;
    mem_ava = mem_total - mem_use;

    //获取虚拟机当前规格
    ret = cps_get_vsm_source_cur(cps_db, vsm_id, &cpu_cur, &mem_cur);
    API_CHECK_FUNC(ret, "cps_get_vsm_source_cur");

    //获取修改规格对应的资源大小
    ret = cps_get_spec_source(cps_db, &cpu, &mem, spec);
    API_CHECK_FUNC(ret, "cps_get_spec_source");

    DEBUG_CPS_CLI(COMMON_DEBUG, "cpu_ava %d mem_ava %d spec %d\n",cpu_ava, mem_ava, spec);
    if (cpu_model == CPU_MODEL_EXCL) {
        if (((cpu_ava + cpu_cur) < cpu) || ((mem_ava + mem_cur) < mem))
            ret = ERR_VSM_RESOURCE;
    }else {
        rc = vsm_create_check(host_name, cpu_cur, &flag);
        DEBUG_CPS_CLI(COMMON_DEBUG, "ret %d cpu_ava %d cpu_cur %d cpu %d\n", ret, cpu_ava, cpu_cur, cpu);

        ret = cps_get_shared_vsm_create_num(cpu, host_name, &created_num, &create_max);
        API_CHECK_FUNC(ret, "cps_get_shared_vsm_create_num");
        if ((mem_ava + mem_cur) < mem) {
            ret = ERR_VSM_RESOURCE;
            goto out;
        }

        if ((create_max <= 0) &&
            (((flag == false) && (cpu_ava < cpu))  || ((flag == true) && ((cpu_ava + cpu_cur) < cpu)))
            ) {
            ret = ERR_VSM_RESOURCE;
        }
    }
out:
    //close_cps_db();
    return ret;
}

int cps_vsm_spec_set(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    char callback_addr[128] = {0};
    char tenant[128] = {0};
	char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char request_id[16] = {0};
    char host_ip[64] = {0};
    char *tenant_name = NULL;
    char *vsm_id = NULL;
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    int spec = 0;

    if (argc == 8) {
        vsm_id = argv[5];
        spec   = atoi(argv[7]);
    } else if (argc == 10) {
        tenant_name = argv[5];
        vsm_id = argv[7];
        spec = atoi(argv[9]);
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    //存在租户时才检查:获取租户名称，不一致不允许操作
    if (tenant_name != NULL) {
        memset(tenant, 0, sizeof(tenant));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strcmp(tenant, tenant_name) != 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "tenant name:%s %s\n", tenant, tenant_name);
            return ERR_SUPP_OPT;
        }
    }

    //判断资源是否支持扩容
    ret = cps_vsm_source_check(vsm_id, spec);
    API_CHECK_FUNC(ret, "cps_vsm_source_check");

    //获取回调地址
    memset(callback_addr, 0, sizeof(callback_addr));
    ret = cps_get_callback_addr(callback_addr, sizeof(callback_addr));
    API_CHECK_FUNC(ret, "cps_get_callback_addr");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = vsm_spec_set_data_comp(request_id, vsm_id, spec, callback_addr, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_spec_set_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_MODIFY);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    API_CHECK_FUNC(ret, "cps_request_send");

    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "request_id=\'%s\',opt_state=%d where vsm_id = \'%s\'", request_id, STATE_CONF_SETTING, vsm_id);
out:
    print_errmsg(ret);
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int cps_asymm_proc(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int main_type = atoi(argv[2]);
    int sub_type = atoi(argv[4]);
    int state = atoi(argv[6]);
    char *request_id = argv[8];
    char *resp_msg = argv[10];

    if ((main_type != VSM_MAIN_TYPE) || (sub_type < CMD_INFO) || (sub_type > CMD_DATA_RESTORE)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "command main_type or sub_type is incorrect!");
        return ERR_PARAM;
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "sub_type:%d state:%d request_id:%s", sub_type, state, request_id);
    if (sub_type == CMD_IMPORT_IMAGE) {
        ret = asymm_image_import_proc(request_id, state, resp_msg);
    }else if (sub_type == CMD_EXPORT_IMAGE) {
        ret = asymm_image_export_proc(request_id, state, resp_msg);
    }else {
        ret = asymm_vsm_opt_proc(sub_type, request_id, state, resp_msg);
    }
    return ret;
}

void cps_state_trans(char *cmd_opt, int *in_state, int *ex_state)
{
    if (cmd_opt == NULL || in_state == NULL || ex_state == NULL)
        return ;

    if (strcmp(cmd_opt, "restart") == 0) {
        *in_state =  STATE_RESTARTING;
        *ex_state =  CMD_RESTART;
    }else if (strcmp(cmd_opt, "start") == 0) {
        *in_state =  STATE_STARTING;
        *ex_state =  CMD_START;
    }else if (strcmp(cmd_opt, "stop") == 0) {
        *in_state =  STATE_CLOSING;
        *ex_state =  CMD_STOP;
    }else if (strcmp(cmd_opt, "reset") == 0) {
        *in_state =  STATE_RESET;
        *ex_state =  CMD_RESET;
    }else if (strcmp(cmd_opt, "destroy") == 0) {
        *in_state =  STATE_DELETING;
        *ex_state =  CMD_DELETE;
    }else if (strcmp(cmd_opt, "init") == 0) {
        *in_state =  STATE_IMPORTING;
        *ex_state =  CMD_INIT_DEV;
    }
}

int cps_vsm_netconf_update(char *vsm_id, net_config_t *cfg)
{
    unsigned ret = ERR_NONE;
    char old_ipv4[16] = {0};
    char old_ipv6[64] = {0};
    char old_interface_name[128] = {0};
    char tenant_name[128];

    //获取租户名称
    memset(tenant_name, 0, sizeof(tenant_name));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant_name, sizeof(tenant_name), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue; // 跳过无名接口
        }

        //查询interface->interface_name接口是否已存在数据库
        if (cfg->opt_type == "add_interface") {
            if (cps_get_num_from_db("vsm_ip_info", "vsm_id='%s' and interface_name='%s'", vsm_id, interface->interface_name) > 0) {
                // 接口已存在
                DEBUG_CPS_CLI(ERR_DEBUG, "interface %s already exists", interface->interface_name);
                ret = ERR_PARAM;
                return ret;
            }
        }
        else if (cfg->opt_type == "del_interface") {   
            // 获取旧的IP地址用于清理IP池
            memset(old_ipv4, 0, sizeof(old_ipv4));
            ret = cps_get_data_from_db("vsm_ip_info", "vsm_id", vsm_id, "vsm_ipv4", 
                                      old_ipv4, sizeof(old_ipv4), false);
            if (ret == 0 && strlen(old_ipv4) > 0) {
                ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip='%s'", old_ipv4);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "delete old ipv4 from ip pool failed: %s", old_ipv4);
                }
            }
            memset(old_ipv6, 0, sizeof(old_ipv6));
            ret = cps_get_data_from_db("vsm_ip_info", "vsm_id", vsm_id, "vsm_ipv6", 
                                      old_ipv6, sizeof(old_ipv6), false);
            if (ret == 0 && strlen(old_ipv6) > 0) {
                ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip='%s'", old_ipv6);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "delete old ipv6 from ip pool failed: %s", old_ipv6);
                }
            }

            // 更新接口信息
            ret = cps_update_data_to_db("vsm_ip_info", 
                "vsm_ipv4='%s',vsm_ipv6='%s',vsm_maskv4='%s',vsm_maskv6='%s',"
                "vsm_gatewayv4='%s',vsm_gatewayv6='%s',bindType=%d,is_enabled=%d,"
                "manage=%d,bridge_interface='%s',vlan_id=%d,ping=%d "
                "where vsm_id='%s' and interface_name='%s'",
                interface->ipv4 ? interface->ipv4 : "",
                interface->ipv6 ? interface->ipv6 : "",
                interface->maskv4 ? interface->maskv4 : "",
                interface->maskv6 ? interface->maskv6 : "",
                interface->gatewayv4 ? interface->gatewayv4 : "",
                interface->gatewayv6 ? interface->gatewayv6 : "",
                interface->bindType,
                interface->is_enable,
                interface->manage,
                interface->bridge ? interface->bridge : "",
                interface->vlan,
                interface->ping,
                vsm_id, interface->interface_name);
        } else {
            // 接口不存在，插入新接口
            ret = cps_insert_data_to_db("vsm_ip_info", 
                "(vsm_id,interface_name,vsm_ipv4,vsm_ipv6,vsm_maskv4,vsm_maskv6,"
                "vsm_gatewayv4,vsm_gatewayv6,bindType,is_enabled,manage,"
                "bridge_interface,vlan_id,ping) "
                "values('%s','%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,'%s',%d,%d)",
                vsm_id, interface->interface_name,
                interface->ipv4 ? interface->ipv4 : "",
                interface->ipv6 ? interface->ipv6 : "",
                interface->maskv4 ? interface->maskv4 : "",
                interface->maskv6 ? interface->maskv6 : "",
                interface->gatewayv4 ? interface->gatewayv4 : "",
                interface->gatewayv6 ? interface->gatewayv6 : "",
                interface->bindType,
                interface->is_enable,
                interface->manage,
                interface->bridge ? interface->bridge : "",
                interface->vlan,
                interface->ping);
        }

        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "update/insert vsm_ip_info failed for %s", interface->interface_name);
            return ret;
        }

        // 插入IPv4静态路由
        for (int j = 0; j < interface->static_route_v4_count; j++) {
            if (interface->static_route_v4[j].dest && interface->static_route_v4[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',1,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v4[j].dest,
                    interface->static_route_v4[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert IPv4 route failed: %s -> %s", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next);
                    return ret;
                }
            }
        }

        // 插入IPv6静态路由
        for (int j = 0; j < interface->static_route_v6_count; j++) {
            if (interface->static_route_v6[j].dest && interface->static_route_v6[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',2,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v6[j].dest,
                    interface->static_route_v6[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert IPv6 route failed: %s -> %s", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next);
                    return ret;
                }
            }
        }

        // 更新IP池（仅在有租户时）
        if (strlen(tenant_name) > 0) {
            if (interface->ipv4 && strlen(interface->ipv4) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                    "(use_status,tenant_name,ip,use) values(1,'%s','%s',0)",
                    tenant_name, interface->ipv4);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert ipv4 to ip pool failed: %s", interface->ipv4);
                }
            }
            if (interface->ipv6 && strlen(interface->ipv6) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                    "(use_status,tenant_name,ip,use) values(1,'%s','%s',0)",
                    tenant_name, interface->ipv6);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert ipv6 to ip pool failed: %s", interface->ipv6);
                }
            }
        }
    }

    return ret;
}

int image_show_callback(char *data, char *requestId)
{
    int ret = 0;
    int arrayLen = 0, i = 0;
    //char create_time[64] = {0};
    char *pData = NULL;
    cJSON *res_data = NULL;
    cJSON *res_array = NULL;
    cJSON *root = NULL;
    cJSON *result = NULL;
    cJSON *param = NULL;
    cJSON *extensions = NULL;
    cJSON *imageInfo = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "resp data:%s\n", data);
    if (ret = callback_status_check(data, requestId))
        return ret;

    //获取时间
   // cps_get_time_now(create_time, sizeof(create_time));

    root = cJSON_Parse(data);
    if (!root)
        return ERR_PARAM;

    result = cJSON_GetObjectItem(root, "result");
    if (result == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cjson parse index[%s] failed\n", "result");
        ret = ERR_CJSON;
        goto out;
    }

    extensions = cJSON_GetObjectItem(result, "extensions");
    if (extensions == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cjson parse index[%s] failed\n", "extensions");
        ret = ERR_CJSON;
        goto out;
    }

    imageInfo = cJSON_GetObjectItem(extensions, "imagInfo");
    if (imageInfo == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cjson parse index[%s] failed\n", "imageInfo");
        ret = ERR_CJSON;
        goto out;
    }

    if (cJSON_IsArray(imageInfo)) {
        res_data = cJSON_CreateObject();
        res_array = cJSON_CreateArray();
        arrayLen = cJSON_GetArraySize(imageInfo);
        for (i = 0; i < arrayLen; i++) {
            cJSON *obj = cJSON_GetArrayItem(imageInfo, i);
            cJSON *info = cJSON_CreateObject();
            if (obj == NULL)
                continue ;

            cJSON_AddNumberToObject(info, "id", i);
            cJSON_AddStringToObject(info, "name", ((param = cJSON_GetObjectItem(obj, "name")) == NULL)? "" : param->valuestring);
            cJSON_AddNumberToObject(info, "type", ((param = cJSON_GetObjectItem(obj, "type")) == NULL)? 0                   :
                ((strcmp(param->valuestring, "hsm") == 0) || (strcmp(param->valuestring, "HSM") == 0))? VSM_TYPE_HSM        :
                ((strcmp(param->valuestring, "svs") == 0) || ((strcmp(param->valuestring, "SVS") == 0)))? VSM_TYPE_SVS      :
                ((strcmp(param->valuestring, "tss") == 0) || ((strcmp(param->valuestring, "TSS") == 0)))? VSM_TYPE_TSA      :
                ((strcmp(param->valuestring, "css") == 0) || ((strcmp(param->valuestring, "CSS") == 0)))? VSM_TYPE_CSS      :
                ((strcmp(param->valuestring, "edb") == 0) || ((strcmp(param->valuestring, "EDB") == 0)))? VSM_TYPE_EDB      :
                ((strcmp(param->valuestring, "kms") == 0) || ((strcmp(param->valuestring, "KMS") == 0)))? VSM_TYPE_KMS      :
                ((strcmp(param->valuestring, "sag") == 0) || ((strcmp(param->valuestring, "SAG") == 0)))? VSM_TYPE_SAG      :
                ((strcmp(param->valuestring, "vpn") == 0) || ((strcmp(param->valuestring, "VPN") == 0)))? VSM_TYPE_VPN      :
                ((strcmp(param->valuestring, "esig") == 0) || ((strcmp(param->valuestring, "ESIG") == 0)))? VSM_TYPE_ESIG   :
                VSM_TYPE_EFS);
            cJSON_AddStringToObject(info, "version", ((param = cJSON_GetObjectItem(obj, "version")) == NULL)? "" : param->valuestring);
            cJSON_AddStringToObject(info, "sign", ((param = cJSON_GetObjectItem(obj, "sign")) == NULL)? "" : param->valuestring);
            cJSON_AddStringToObject(info, "date", ((param = cJSON_GetObjectItem(obj, "create_time")) == NULL)? "" : param->valuestring);
            cJSON_AddStringToObject(info, "remark", ((param = cJSON_GetObjectItem(obj, "remark")) == NULL)? "" : param->valuestring);
            cJSON_AddNumberToObject(
                info, "min_cpu", ((param = cJSON_GetObjectItem(obj, "min_cpu")) == NULL) ? 1 : param->valueint);
            cJSON_AddNumberToObject(
                info, "min_mem", ((param = cJSON_GetObjectItem(obj, "min_mem")) == NULL) ? 1 : param->valueint);
            cJSON_AddItemToArray(res_array, info);
        }
        cJSON_AddItemToObject(res_data, "imageInfo", res_array);
        pData = cJSON_PrintUnformatted(res_data);
        printf("%s\r\n",pData);
    }
out:
    if (root) {
        cJSON_Delete(root);
        root = NULL;
    }
    if (res_data) {
        cJSON_Delete(res_data);
        res_data = NULL;
    }
    if (pData) {
        free(pData);
        pData = NULL;
    }
    return ret;
}

int get_vsminfo_callback(char *data, char *requestId)
{
    int ret = 0;
    int vsm_type = 0;
    int sm2_key = 0, sym_key = 0, sign_cert = 0, encrypt_cert = 0;
    int req_record = 0;
    char tmp[8] = {0};
    char *svs_str = NULL;
    char *tsa_str = NULL;
    char *pData = NULL;
    cJSON *root = NULL;
    cJSON *status = NULL;
    cJSON *res_data = NULL;
    cJSON *request_id = NULL;
    cJSON *vsm_id = NULL;
    cJSON *result = NULL;
    cJSON *extensions = NULL;
    cJSON *version = NULL;
    cJSON *svs_status = NULL;
    cJSON *tsa_status = NULL;
    cJSON *param = NULL;

    if (data == NULL || requestId == NULL)
        return ERR_PARAM;

    root = cJSON_Parse(data);
    if (!root)
        return ERR_PARAM;

    DEBUG_CPS_CLI(COMMON_DEBUG, "resp data:%s\r\n", data);
    status = cJSON_GetObjectItem(root, "status");
    if (status) {
        if (status->valueint != 200) {
            ret = status->valueint;
            goto out;
        }
    }

    request_id = cJSON_GetObjectItem(root, "requestId");
    if (request_id == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "recv data err!\n");
        ret = ERR_PARAM;
        goto out;
    }

    if (strcmp(requestId, request_id->valuestring)) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "request id:[%s] [%s]\r\n", requestId, request_id->valuestring);
        ret = ERR_REQUEST_ID;
        goto out;
    }

    result = cJSON_GetObjectItem(root, "result");
    if (result == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "recv data err!\n");
        ret = ERR_PARAM;
        goto out;
    }

    vsm_id = cJSON_GetObjectItem(result, "id");
    extensions = cJSON_GetObjectItem(result, "extensions");
    if (extensions == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "recv data err!\n");
        ret = ERR_PARAM;
        goto out;
    }

    #if 0
    //获取虚拟机类型
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (vsm_id == NULL)? "" : vsm_id->valuestring, "vsm_type", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    vsm_type = atoi(tmp);
    #endif

    svs_status = cJSON_GetObjectItem(extensions, "svsStatus");
    tsa_status = cJSON_GetObjectItem(extensions, "tsaStatus");

    res_data = cJSON_CreateObject();
    if (svs_status) {
        svs_str = (char *)malloc(strlen(svs_status->valuestring));
        if (svs_str == NULL) {
            ret = ERR_MALLOC;
            goto out;
        }
        memset(svs_str, 0, strlen(svs_status->valuestring));
        base64_decode(svs_status->valuestring, svs_str, strlen(svs_status->valuestring));
        DEBUG_CPS_CLI(COMMON_DEBUG, "svs_str:%s\r\n", svs_str);
        cJSON_AddStringToObject(res_data, "svsStatus", svs_str);
    }else {
        cJSON_AddStringToObject(res_data, "svsStatus", "");
    }

    if (tsa_status) {
        tsa_str = (char *)malloc(strlen(tsa_status->valuestring));
        if (tsa_str == NULL) {
            ret = ERR_MALLOC;
            goto out;
        }
        memset(tsa_str, 0, strlen(tsa_status->valuestring));
        base64_decode(tsa_status->valuestring, tsa_str, strlen(tsa_status->valuestring));
        DEBUG_CPS_CLI(COMMON_DEBUG, "tsa_str:%s\r\n", tsa_str);
        cJSON_AddStringToObject(res_data, "tsaStatus", tsa_str);
    }else {
        cJSON_AddStringToObject(res_data, "tsaStatus", "");
    }
    pData = cJSON_PrintUnformatted(res_data);
    printf("%s\r\n",pData);
out:
    if (svs_str)
        free(svs_str);
    if (tsa_str)
        free(tsa_str);
    if (root)
        cJSON_Delete(root);
    if (res_data)
        cJSON_Delete(res_data);
    if (pData) {
        free(pData);
        pData = NULL;
    }
    return ret;
}
int vsm_create_callback(char *data, char *requestId)
{
    int ret = 0;
    cJSON *root = NULL;
    cJSON *status = NULL;
    cJSON *vsm_id = NULL;
    cJSON *request_id = NULL;
    cJSON *message = NULL;

    if (data == NULL || requestId == NULL)
        return ERR_PARAM;

    root = cJSON_Parse(data);
    if (!root)
        return ERR_PARAM;

    DEBUG_CPS_CLI(COMMON_DEBUG, "resp data:%s\r\n", data);
    status = cJSON_GetObjectItem(root, "status");
    if (status) {
        if (status->valueint != 200) {
            message = cJSON_GetObjectItem(root, "message");
            printf("%s", message->valuestring);
            ret = ERR_RECV_MESSAGE;
            goto out;
        }
    }

    vsm_id = cJSON_GetObjectItem(root, "vsmId");
    request_id = cJSON_GetObjectItem(root, "requestId");
    if (vsm_id == NULL || request_id == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "recv data err!\n");
        ret = ERR_PARAM;
        goto out;
    }

    if (strcmp(requestId, request_id->valuestring)) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "request id:[%s] [%s]\r\n", requestId, request_id->valuestring);
        ret = ERR_REQUEST_ID;
        goto out;
    }

    ret = cps_insert_data_to_db(CPS_VSM_MANAGE_TABLE, "(vsm_id,request_id) values(\'%s\',\'%s\')", vsm_id->valuestring, request_id->valuestring);
    API_CHECK_FUNC(ret, "cps_insert_data_to_db");
out:
    if (root)
        cJSON_Delete(root);
    return ret;
}

int vsm_opt_data_comp(char *request_id, char *opt, char *vsm_id, char *callback_ip, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char callback_url[128];
    char *pData = NULL;
    cJSON *root = NULL;

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));
    DEBUG_CPS_CLI(COMMON_DEBUG, "callback_url %s\r\n", callback_url);

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", opt);
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int image_show_data_comp(char *request_id, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int vsm_netconfig_data_comp(char *request_id, char *vsm_id, net_config_t *netconf, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL || data == NULL || netconf == NULL)
        return ERR_PARAM;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
    //从netconf->interfaces解析第一个interface的ip,mask,gateway,ipv6,maskv6,gatewayv6
    if (netconf->interfaces) {
        interface_cfg_t* interface = &netconf->interfaces[0];
        //如果为空字符串或为NULL,则不添加
        if (interface->ipv4 !=  NULL && strcmp(interface->ipv4, "") != 0) {
            cJSON_AddStringToObject(root, "ip", interface->ipv4);
        }
        if (interface->maskv4 != NULL && strcmp(interface->maskv4, "") != 0) {
            cJSON_AddStringToObject(root, "mask", interface->maskv4);
        }
        if (interface->gatewayv4 != NULL && strcmp(interface->gatewayv4, "") != 0) {
            cJSON_AddStringToObject(root, "gateway", interface->gatewayv4);
        }
        if (interface->ipv6 != NULL && strcmp(interface->ipv6, "") != 0) {
            cJSON_AddStringToObject(root, "ipv6", interface->ipv6);
        }
        if (interface->maskv6 != NULL && strcmp(interface->maskv6, "") != 0) {
            cJSON_AddStringToObject(root, "maskv6", interface->maskv6);
        }
        if (interface->gatewayv6 != NULL && strcmp(interface->gatewayv6, "") != 0) {
            cJSON_AddStringToObject(root, "gatewayv6", interface->gatewayv6);
        }
    }
    cJSON_AddItemToObject(root, "interface", netconf->interface_array);
    cJSON_AddStringToObject(root, "oprType", netconf->opt_type);
	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
        DEBUG_CPS_CLI(COMMON_DEBUG, "send body data:<<%s>>\r\n", data);
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int vsm_spec_set_data_comp(char *request_id, char *vsm_id, unsigned int spec, char *callback_ip, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char callback_url[128];
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL || data == NULL)
        return ERR_PARAM;

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "modify");
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
	cJSON_AddNumberToObject(root, "flavor", spec);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;

}

int get_vsm_info_data_comp(char *request_id, char *vsm_id, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL)
        return ERR_PARAM;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "getinfo");
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
    pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int vsm_create_data_comp(char *request_id, char *vsm_name_str, unsigned int cpu_model, unsigned int spec, char *image_url, char *sign, vsm_netconf *netconf, char *callback_ip, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char callback_url[128];
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || sign == NULL || callback_ip == NULL || data == NULL || vsm_name_str == NULL)
        return ERR_PARAM;

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "create");
    cJSON_AddStringToObject(root, "vsmName", vsm_name_str);
	cJSON_AddNumberToObject(root, "cpuModel", cpu_model);
	cJSON_AddStringToObject(root, "imageUrl", (image_url == NULL)? "" : image_url);
	cJSON_AddStringToObject(root, "alg", "SM2WithSM3");
	cJSON_AddStringToObject(root, "sign", sign);
	cJSON_AddNumberToObject(root, "flavor", spec);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);
    cJSON_AddStringToObject(root, "ip", (strcmp(netconf->vsm_ipv4, "null") == 0)? "" : netconf->vsm_ipv4);
	cJSON_AddStringToObject(root, "mask", (strcmp(netconf->vsm_maskv4, "null") == 0)? "" : netconf->vsm_maskv4);
	cJSON_AddStringToObject(root, "gateway", (strcmp(netconf->vsm_gatewayv4, "null") == 0)? "" : netconf->vsm_gatewayv4);
	cJSON_AddStringToObject(root, "ipv6", (strcmp(netconf->vsm_ipv6, "null") == 0)? "" : netconf->vsm_ipv6);
	cJSON_AddStringToObject(root, "maskv6", (strcmp(netconf->vsm_maskv6, "null") == 0)? "" : netconf->vsm_maskv6);
	cJSON_AddStringToObject(root, "gatewayv6", (strcmp(netconf->vsm_gatewayv6, "null") == 0)? "" : netconf->vsm_gatewayv6);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int vsm_upgrade_data_comp(char *request_id, char *vsm_id, char *pkg_name, char *sign, char *version, char *callback_ip, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char callback_url[128];
    char pkg_url[128];
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL || sign == NULL || callback_ip == NULL || data == NULL)
        return ERR_PARAM;

    memset(callback_url, 0, sizeof(callback_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, "callback", callback_url, sizeof(callback_url));

    memset(pkg_url, 0, sizeof(pkg_url));
    cps_url_comp(callback_ip, HTTP_SERVER_PORT, pkg_name, pkg_url, sizeof(pkg_url));

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "upgrade");
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
	cJSON_AddStringToObject(root, "packVersion", (version == NULL)? "" : version);
	cJSON_AddStringToObject(root, "packUrl", (pkg_name == NULL)? "" : pkg_url);
	cJSON_AddStringToObject(root, "alg", "SM2WithSM3");
	cJSON_AddStringToObject(root, "sign", sign);
	cJSON_AddStringToObject(root, "callbackUrl", callback_url);

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    if (root)
        cJSON_Delete(root);

    return ret;
}

int cps_vsm_upgrade(char *vsm_id, char *pkg_name, char *sign, char *version, char *remark)
{
    unsigned int ret = ERR_NONE;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    char *data_base64 = NULL;
	char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char request_id[16] = {0};
	char host_ip[64] = {0};
	char tenant_name[64] = {0};
	char tmp[8] = {0};
    char str[128] = {0};
    char callback_addr[128] = {0};
	char ctime[64] = {0};
    msg_header_t *msg_header = NULL;

    if (vsm_id == NULL || version == NULL || sign == NULL)
        return ERR_PARAM;

    memset(tenant_name, 0, sizeof(tenant_name));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant_name, sizeof(tenant_name), false);

    ret = vsm_opt_state_check(vsm_id, "upgrade", tenant_name);
    API_CHECK_FUNC(ret, "vsm_opt_state_check");

    //获取回调地址
    ret = cps_get_callback_addr(callback_addr, sizeof(callback_addr));
    API_CHECK_FUNC(ret, "cps_get_callback_addr");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取host ip
    memset(host_ip, 0, sizeof(host_ip));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //组升级虚拟机接口调用的json数据,即传入参数
    memset(str, 0, sizeof(str));
    snprintf(str, sizeof(str)-1, "none_package:%s", version);
    ret = vsm_upgrade_data_comp(request_id, vsm_id, (pkg_name == NULL)? NULL : pkg_name, sign, (pkg_name == NULL)? str : version, callback_addr, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_vsm_upgrade_data_comp");
    data_len = strlen(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\n", data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_UPGRADE);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    API_CHECK_FUNC(ret, "cps_request_send");

    //更新数据库
    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "vsm_id=\'%s\',request_id=\'%s\',opt_state=%d where vsm_id = \'%s\'", vsm_id, request_id, STATE_UPGRADING, vsm_id);
    API_CHECK_FUNC(ret, "cps_request_send");

    cps_get_time_now(ctime, sizeof(ctime));
    ret = cps_insert_data_to_db(CPS_VSM_UPGRADE_TABLE, "(vsm_id,request_id,pkg_name,image_sign,up_version,up_time,up_desc) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", vsm_id, request_id, (pkg_name == NULL)? "" : pkg_name, (pkg_name == NULL)? sign : "", (pkg_name == NULL)? version : "-", ctime, (remark == NULL)? "" : remark);

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);

    return ret;
}

int cps_vsm_pkg_upgrade(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int len = 0;
    char *vsm_id = argv[4];
    char *pkg_name = argv[6];
    char *remark = NULL;
    char sign[1024];

    if (argc == 11)
        remark = argv[10];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);

    //计算pkg文件sign值
    memset(sign, 0, sizeof(sign));
    len = sizeof(sign);
    ret = image_file_sign(pkg_name, sign, &len);
    API_CHECK_FUNC(ret, "image_file_sign");

    ret = cps_vsm_upgrade(vsm_id, pkg_name, sign, "package", remark);

out:
    print_errmsg(ret);
    return ret;
}

int cps_vsm_image_upgrade(int argc, char *argv[])
{

    unsigned int ret = ERR_NONE;
    char *vsm_id = argv[4];
    char *image_sign = argv[6];
    char *image_ver = argv[8];
    char *remark = NULL;


    if (argc == 11)
        remark = argv[10];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    ret = cps_vsm_upgrade(vsm_id, NULL, image_sign, image_ver, remark);

    print_errmsg(ret);
    return ret;
}

int cps_vsm_name_check(char *host_name, char *vsm_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (vsm_name == NULL || host_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql)-1, "select vsm_name from %s where host_name = \'%s\'", CPS_VSM_MANAGE_TABLE, host_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
        goto out;
	}

    if (nrow > 0) {
        for(i = 0; i < nrow; i++) {
            if (strcmp(result[i + 1], vsm_name) == 0) {
                ret = ERR_VSM_NAME;
                goto out;
            }
        }
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_vsm_disk_source_check(char *host_name, int spec)
{
    unsigned int ret = ERR_NONE;
    unsigned int remain_disk = 0;
    char tmp[8];

    if (host_name == NULL)
        return ERR_PARAM;

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "device_name", host_name, "remain_disk", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    remain_disk = atoi(tmp);

    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_FLAVOR_INFO_TABLE, "flavor", (void *)&spec, "disk", tmp, sizeof(tmp), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (atoi(tmp) > remain_disk)
        return ERR_DISK_RESOURCE;

out:
    return ret;
}

int cps_gen_vsm_name(char *vsm_name, char *vsm_name_str, int str_len)
{
    int ret = ERR_NONE;
    char random[8] = {0};
    char str[64] = {0};

    if (vsm_name == NULL || vsm_name_str == NULL || str_len <= 0)
        return ERR_PARAM;

    memset(random, 0, sizeof(random));
    ret = cps_generate_random(random, 4);
    API_CHECK_FUNC(ret, "cps_generate_random");

    memset(str, 0, sizeof(str));
    sprintf(str, "%s_%s", vsm_name, random);

    if (str_len < strlen(str))
        return ERR_PARAM;

    memcpy(vsm_name_str, str, strlen(str));
out:
    return ret;
}

int cps_vsm_create(int argc, char *argv[])
{
    //cps vsm create image_dig <string> image_name <string> image_ver <string> host_name <string> host_ip <string> vsm_name <string> vsm_type <number> create_num <number> cpu_model <number> spec <number>  [interface <string>] [remark <strings>]
    unsigned int ret = ERR_NONE;
    int vsm_create = 0;
    int callback_flag = false;
    int spec = 0, cpu_model = 0;
    int data_len = 0, body_len = 0;
    int sign_len = 0;
    char used_flag_str[8] = {0};
    char vsm_max_str[16] = {0};
    char vsm_name_str[64] = {0};
    char *image_dig = argv[4];
    char *image_name = argv[6];
    char *image_ver = argv[8];
    char *host_name = argv[10];
    char *host_ip = argv[12];
    char *vsm_name = argv[14];
    int vsm_type = atoi(argv[16]);
    int create_num = atoi(argv[18]);
    cpu_model = atoi(argv[20]);
    spec = atoi(argv[22]);
    vsm_netconf netconf;
    msg_header_t *msg_header = NULL;
    netconf.vsm_ipv4 = "null";
    netconf.vsm_maskv4 = "null";
    netconf.vsm_gatewayv4 = "null";
    netconf.vsm_ipv6 = "null";
    netconf.vsm_maskv6 = "null";
    netconf.vsm_gatewayv6 = "null";
    
    char *image_url = NULL;
    char *remark = NULL;
    char *data_base64 = NULL;
	char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char request_id[16] = {0};
	char ctime[64] = {0};
	char tmp[8] = {0};
    char callback_addr[128] = {0};
    int i = 0;
    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    char *interface = NULL;
    if (argc >= 25){
        interface = argv[24];
    }
    if (argc >= 27){
        remark = argv[26];
    }
    // 判断虚拟机类型是否授权
    ret = cps_get_data_from_db(CPS_IMAGE_MAP_TABLE, "image_id", (void *)&vsm_type, "used_flag", used_flag_str, sizeof(used_flag_str), true);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (atoi(used_flag_str) != 1) {
        DEBUG_CPS_CLI(ERR_DEBUG, "image type unauthorized! used_flag[%d] ", atoi(used_flag_str));
        ret = ERR_IMAGE_TYPE;
        goto out;
    }

    for (i = 0; i < create_num; i++) {
        strcpy(vsm_name_str, vsm_name);
        if (create_num > 1) {
            memset(vsm_name_str, 0, sizeof(vsm_name_str));
            ret = cps_gen_vsm_name(vsm_name, vsm_name_str, sizeof(vsm_name_str));
            API_CHECK_FUNC(ret, "cps_gen_vsm_name");
        }
        //判断虚拟机创建数量
        memset(vsm_max_str, 0, sizeof(vsm_max_str));
        ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "device_name", (void *)host_name, "max_vsm_num", vsm_max_str, sizeof(vsm_max_str), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        vsm_create = cps_get_num_from_db(CPS_VSM_MANAGE_TABLE, "host_name=\'%s\'", host_name);
        DEBUG_CPS_CLI(COMMON_DEBUG, "vsm_max:%d vsm_create:%d\n", atoi(vsm_max_str), vsm_create);
        if (vsm_create >= atoi(vsm_max_str)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "vsm cerated reach max num:%d", atoi(vsm_max_str));
            ret = ERR_VSM_NUM;
            goto out;
        }

        //判断虚拟机名称
        ret = cps_vsm_name_check(host_name, vsm_name_str);
        API_CHECK_FUNC(ret, "cps_vsm_name_check");

        //判断磁盘资源是否足够
        ret = cps_vsm_disk_source_check(host_name, spec);
        API_CHECK_FUNC(ret, "cps_vsm_disk_source_check");

        //判断cpu模式
        ret = cps_vsm_cpu_excl_check(host_name, spec, cpu_model);
        API_CHECK_FUNC(ret, "cps_vsm_cpu_excl_check");

        //获取回调地址
        ret = cps_get_callback_addr(callback_addr, sizeof(callback_addr));
        API_CHECK_FUNC(ret, "cps_get_callback_addr");

        //生成8位随机数作为requestid
        ret = cps_generate_random(request_id, sizeof(request_id));
        API_CHECK_FUNC(ret, "cps_generate_random");

        //组创建虚拟机接口调用的json数据,即传入参数
        ret = vsm_create_data_comp(request_id, vsm_name_str, cpu_model, spec, image_url, image_dig, &netconf, callback_addr, data, sizeof(data));
        API_CHECK_FUNC(ret, "vsm_create_data_comp");
        data_len = strlen(data);
        DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\n", data);

        //对数据进行base64编码
        data_base64 = (unsigned char *)malloc(strlen(data) * 2);
        if (!data_base64) {
            ret = ERR_MALLOC;
            goto out;
        }
        base64_encode(data, data_base64, data_len);

        //对json数据进行签名
        ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
        API_CHECK_FUNC(ret, "sign_with_internal_key");

        //组tcp传输body数据
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_CREATE);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, vsm_create_callback, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");

        //更新数据库
        cps_get_time_now(ctime, sizeof(ctime));
        ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "opt_state=%d,image_dig=\'%s\',image_ver=\'%s\',host_name=\'%s\',host_ip=\'%s\',vsm_type=%d,vsm_name=\'%s\',create_time=\'%s\',cpu_model=%d,create_spec=%d,remark=\'%s\' where request_id=\'%s\'", STATE_CREATING, image_dig, image_ver, host_name, host_ip, vsm_type, vsm_name_str, ctime, cpu_model, spec, (remark == NULL)? "":remark, request_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");

        ret = vsm_share_num_maintan(request_id, VSM_OPT_CREATE, 0);
        API_CHECK_FUNC(ret, "cps_vsm_share_num_maintan");

        if (data_base64) {
            free(data_base64);
            data_base64 = NULL;
        }
        if (msg_header) {
            free(msg_header);
            msg_header = NULL;
        }
    }
out:
    print_errmsg(ret);
    return ret;
}

int cps_get_spec_source(sqlite3 *db, unsigned int *cpu, unsigned int *mem, int spec)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (db == NULL || cpu == NULL || mem == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT cpu,mem FROM \'%s\' where flavor = %d", CPS_FLAVOR_INFO_TABLE, spec);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, &err_msg);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s).\n", sql, err_msg);
        sqlite3_free(err_msg);
        return ERR_SQL_OPT;
    }
    *cpu = atoi(result[ncol]);
    *mem = atoi(result[ncol + 1]);

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cps_get_vsm_source_cur(sqlite3* db, char *vsm_id, unsigned int *cpu_cur, unsigned int *mem_cur)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (db == NULL || cpu_cur == NULL || mem_cur == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT flavor_info.cpu,flavor_info.mem FROM flavor_info LEFT JOIN vsm_manage on flavor_info.flavor = vsm_manage.create_spec where vsm_id = \'%s\'", vsm_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, &err_msg);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s).\n", sql, err_msg);
        sqlite3_free(err_msg);
        return ERR_SQL_OPT;
    }
    *cpu_cur = atoi(result[ncol]);
    *mem_cur = atoi(result[ncol + 1]);

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cps_share_vsm_resource_cal(char *host_name)
{
    unsigned int ret = ERR_NONE;
    unsigned int cpu_num = 0;
    int share_vsm_num = 0;
    int nrow = 0, ncol = 0, i = 0;
    int tmp = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (host_name == NULL)
        return cpu_num;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql)-1, "select cpu,created,create_max from %s where host_name = \'%s\'", CPS_SHARE_VSM_MAX_TABLE, host_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
        goto out;
	}

    if (nrow > 0) {
        for(i = 1; i <= nrow; i++) {
            share_vsm_num = atoi(result[ncol * i + 1])/4;
            cpu_num += atoi(result[i * ncol]) * (share_vsm_num + ((atoi(result[ncol * i + 2]) > 0)? 1 : 0));
        }
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    ////close_cps_db();
    return cpu_num;
}

int cps_get_host_source_use(sqlite3* db, char *host_name, unsigned int *cpu_use, unsigned int *mem_use, unsigned int *vsm_num)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0;
    int i = 0;
    int share_cpu_num = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (db == NULL || cpu_use == NULL || mem_use == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT flavor_info.cpu FROM flavor_info LEFT JOIN vsm_manage on flavor_info.flavor = vsm_manage.create_spec where vsm_manage.host_name=\'%s\' and cpu_model=%d", host_name, CPU_MODEL_EXCL);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, &err_msg);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s).\n", sql, err_msg);
        sqlite3_free(err_msg);
        return ERR_SQL_OPT;
    }

    if (nrow > 0) {
        for (i = 1; i <= nrow; i++) {
            *cpu_use += atoi(result[i]);
        }
    }
    share_cpu_num = cps_share_vsm_resource_cal(host_name);
    DEBUG_CPS_CLI(COMMON_DEBUG, "share_cpu_num:%d\n", share_cpu_num);
    *cpu_use += share_cpu_num;

    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT flavor_info.mem FROM flavor_info LEFT JOIN vsm_manage on flavor_info.flavor = vsm_manage.create_spec where vsm_manage.host_name=\'%s\'", host_name);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, &err_msg);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s).\n", sql, err_msg);
        sqlite3_free(err_msg);
        return ERR_SQL_OPT;
    }

    if (nrow > 0) {
        for (i = 1; i <= nrow; i++) {
            *mem_use += atoi(result[i]);
        }
    }
    *vsm_num = nrow;

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cps_get_host_source(sqlite3* db, char *host_name, unsigned int *cpu_total, unsigned int *mem_total)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0;
    int i = 0, allocate_num = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;

    if (db == NULL || cpu_total == NULL || mem_total == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    //sprintf(sql, "select remain_cpu,remain_mem from %s where device_name=\'%s\'", CPS_DEV_STATUS_TABLE, host_name);
    sprintf(sql, "select cpu_cores,memory_size from %s where device_name=\'%s\'", CPS_DEV_STATUS_TABLE, host_name);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, &err_msg);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        sqlite3_free(err_msg);
        return ERR_SQL_OPT;
    }

    if (nrow > 0) {
        *cpu_total = atoi(result[ncol]);
        *mem_total = atoi(result[ncol + 1]);
    }

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int cps_image_show(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char *host_ip = argv[4];
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //组获取镜像接口调用的json数据，即传入参数
    ret = image_show_data_comp(request_id, data, sizeof(data));
    API_CHECK_FUNC(ret, "image_show_data_comp");
    data_len = strlen(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "req data:%s\n", data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, CHSM_MAIN_TYPE, CMD_STATUS);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, image_show_callback, request_id);
    API_CHECK_FUNC(ret, "cps_request_send");

out:
    if (ret)
        print_errmsg(ret);
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int cps_get_share_vsm_num(cJSON *obj, char *host_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char *err_msg = NULL;
    char **result = NULL;
    cJSON *array = NULL;
    if (obj == NULL || host_name == NULL) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    snprintf(sql, sizeof(sql)-1, "select * from %s where host_name = \'%s\'", CPS_SHARE_VSM_MAX_TABLE, host_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, &err_msg);
	if (ret != SQLITE_OK) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed(%s)\n", sql, err_msg);
        sqlite3_free(err_msg);
		ret = ERR_SQL_OPT;
        goto out;
	}

    array = cJSON_CreateArray();
    if (nrow > 0) {
        for (i = 1; i <= nrow; i++) {
            cJSON *param = cJSON_CreateObject();
            cJSON_AddNumberToObject(param, "cpu", atoi(result[ncol * i + 1]));
            cJSON_AddNumberToObject(param, "created", atoi(result[ncol * i + 2]));
            cJSON_AddNumberToObject(param, "create_max", atoi(result[ncol * i + 3]));
            cJSON_AddItemToArray(array, param);
        }
    }
    cJSON_AddItemToObject(obj, "flavor_info", array);
out:
    if(result){
        sqlite3_free_table(result);
    }

    //close_cps_db();
    return ret;
}

int cps_host_show(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    unsigned int cpu_total = 0, mem_total = 0;
    unsigned int cpu_use = 0, mem_use = 0;
    unsigned int vsm_num = 0;
    unsigned int cpu_share_max = 0;
    int nrow = 0, ncol = 0, i = 0;
    char *data = NULL;
	char **result = NULL;
	char sql[SQL_LEN_MAX] = {0};
    cJSON *root = NULL;
    cJSON *array = NULL;
    cJSON *flavor_array = NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //获取宿主机信息
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select device_name,ip,device_status from %s",CPS_DEV_STATUS_TABLE);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
	if ((ret != SQLITE_OK)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
		ret = ERR_SQL_OPT;
        goto out;
	}

    root = cJSON_CreateObject();
    array = cJSON_CreateArray();
    if (nrow) {
        for (i = 0; i < nrow; i++) {
            cpu_total = mem_total = cpu_use = mem_use = 0;
            //获取宿主机的资源总和
            DEBUG_CPS_CLI(COMMON_DEBUG, "host_name:%s\n", result[ncol * (i + 1)]);
            ret = cps_get_host_source(cps_db, result[ncol * (i + 1)], &cpu_total, &mem_total);
            API_CHECK_FUNC(ret, "cps_get_host_source");
            // 获取宿主机已用资源
            ret = cps_get_host_source_use(cps_db, result[ncol * (i + 1)], &cpu_use, &mem_use, &vsm_num);
            API_CHECK_FUNC(ret, "cps_get_host_source_use");

            #if 0
            //获取最大规格的共享模式虚拟机
            ret = cps_vsm_get_cpu_share_max(tenant_name, result[ncol * (i + 1)], &cpu_share_max);
            API_CHECK_FUNC(ret, "cps_vsm_get_cpu_share_max");
            #endif

            DEBUG_CPS_CLI(COMMON_DEBUG, "cpu_total:%d mem_total:%d cpu_use:%d mem_use:%d\n", cpu_total, mem_total, cpu_use, mem_use);
            if (cpu_total && mem_total) {
                cJSON *obj = cJSON_CreateObject();
                cJSON_AddStringToObject(obj, "host_name", result[ncol * (i + 1)]);
                cJSON_AddStringToObject(obj, "host_ip", result[ncol * (i + 1) + 1]);
                cJSON_AddStringToObject(obj, "host_status", result[ncol * (i + 1) + 2]);
                cJSON_AddNumberToObject(obj, "cpu_total", cpu_total);
                cJSON_AddNumberToObject(obj, "cpu_ava", cpu_total-cpu_use);
                cJSON_AddNumberToObject(obj, "mem_total", mem_total);
                cJSON_AddNumberToObject(obj, "mem_ava", mem_total-mem_use);
                cJSON_AddNumberToObject(obj, "vsm_num", vsm_num);

                //获取当前宿主机可各规格可创建共享虚拟机台数
                ret = cps_get_share_vsm_num(obj, result[ncol * (i + 1)]);

                cJSON_AddItemToArray(array, obj);
            }
        }
    }
    cJSON_AddItemToObject(root, "hostInfo", array);
    data = cJSON_PrintUnformatted(root);
    printf("%s\r\n",data);
out:
    if(result){
        sqlite3_free_table(result);
    }

    if (ret)
        print_errmsg(ret);
    if (root) {
        cJSON_Delete(root);
        root = NULL;
    }
    if (data) {
        free(data);
        data = NULL;
    }
    //close_cps_db();
    return ret;
}

int vsm_netconf_set(char *vsm_id, net_config_t *cfg,char *tenant_name,char *remark)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    char cluster_id[64] = {0};
    if (vsm_id == NULL || cfg == NULL)
        return ERR_PARAM;
 
    //判断虚机是否存在集群中
    memset(cluster_id, 0, sizeof(cluster_id));
    ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id", (void *)vsm_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    if (ret == 0 && strlen(cluster_id) > 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm %s is in cluster %s, can't set network", vsm_id, cluster_id);
        ret = ERR_VSM_OPT;
        goto out;
    }

    //判断IP是否冲突，如果冲突，则返回错误
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        if (interface->ipv4 && strlen(interface->ipv4) > 0) {
            //判断ipv4对应的vsm_id是否为vsm_id
            if (cps_get_num_from_db("vsm_ip_info", "vsm_ipv4='%s' and vsm_id!='%s'", interface->ipv4, vsm_id) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "set interface %s failed, ipv4 already exists", interface->interface_name);
                ret = ERR_IP_EXIST;
                goto out;
            }
            /*
            //判断ipv4是否在ip池中
            if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=1", interface->ipv4) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "set interface %s failed, ipv4 already used", interface->interface_name);
                ret = ERR_IP_USED;
                goto out;
            }
            */
        }   
        if (interface->ipv6 && strlen(interface->ipv6) > 0) {
            //判断ipv6对应的vsm_id是否为vsm_id
            if (cps_get_num_from_db("vsm_ip_info", "vsm_ipv6='%s' and vsm_id!='%s'", interface->ipv6, vsm_id) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "set interface %s failed, ipv6 already exists", interface->interface_name);
                ret = ERR_IP_EXIST;
                goto out;
            }
            /*
            //判断ipv6是否在ip池中
            if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=1", interface->ipv6) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "set interface %s failed, ipv6 already used", interface->interface_name);
                ret = ERR_IP_USED;
                goto out;
            }
            */
        }
        //如果租户不为空，则需要判断IP是否在租户地址池中
        if(tenant_name != NULL && strlen(tenant_name) > 0){
            ret = vsm_opt_tenant_ip_check(vsm_id, tenant_name,1,interface->ipv4,interface->ipv6,interface->interface_name);
            API_CHECK_FUNC(ret, "vsm_netconf_set:vsm_opt_tenant_ip_check");
        }   
        
    }
    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_generate_random failed! error=%0x", ret);
        return ret;
    }

    //获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    //组配置虚拟机网络接口调用的json数据,即传入参数
    memset(data, 0, sizeof(data));
   //组包json
   
    ret = vsm_netconfig_data_comp(request_id, vsm_id, cfg, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_netconfig_data_comp");
    data_len = strlen(data);
    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_NETWORK);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        ret = ERR_PARAM;
        goto out;
    }

    //更新数据库
    // 继续更新数据库
    // 这里只做最小化实现，假设需要将网络配置信息写入数据库
    // 这里只记录操作日志，实际业务可根据需要扩展
    DEBUG_CPS_CLI(COMMON_DEBUG, "vsm_netconf_set: vsm_id=%s, request_id=%s, 已发送网络配置，准备更新数据库", vsm_id, request_id);

    // 示例：更新vsm_ip_info表
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue;
        }
        ret = cps_update_data_to_db("vsm_ip_info",
            "vsm_ipv4='%s',vsm_ipv6='%s',vsm_maskv4='%s',vsm_maskv6='%s',"
            "vsm_gatewayv4='%s',vsm_gatewayv6='%s',bindType=%d,is_enabled=%d,"
            "manage=%d,bridge_interface='%s',vlan_id=%d,ping=%d,remark='%s'"
            "where vsm_id='%s' and interface_name='%s'",
            interface->ipv4 ? interface->ipv4 : "",
            interface->ipv6 ? interface->ipv6 : "",
            interface->maskv4 ? interface->maskv4 : "",
            interface->maskv6 ? interface->maskv6 : "",
            interface->gatewayv4 ? interface->gatewayv4 : "",
            interface->gatewayv6 ? interface->gatewayv6 : "",
            interface->bindType,
            interface->is_enable,
            interface->manage,
            interface->bridge ? interface->bridge : "",
            interface->vlan,
            interface->ping,
            remark!=NULL && strlen(remark) > 0 ? remark : "",
            vsm_id,
            interface->interface_name
        );
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "更新数据库失败: vsm_id=%s, interface=%s, error=%0x", vsm_id, interface->interface_name, ret);
            goto out;
        }
    }
out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);

    return ret;
}
//添加网卡配置
int vsm_netconf_add(char *vsm_id, net_config_t *cfg, char *tenant_name,char *remark)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    char cluster_id[64] = {0};
    if (vsm_id == NULL)
        return ERR_PARAM;
    
    //判断虚机是否存在集群中
    memset(cluster_id, 0, sizeof(cluster_id));
    ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id", (void *)vsm_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    if (ret == 0 && strlen(cluster_id) > 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm %s is in cluster %s, can't add network", vsm_id, cluster_id);
        ret = ERR_VSM_OPT;
        goto out;
    }
    //判断IP是否冲突，如果冲突，则返回错误
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue; // 跳过无名接口
        }
        //tenant_name不为空则需要判断IP是否是在tenant_addr_info表中所属租户分配网段的里面

        if (tenant_name != NULL && strlen(tenant_name) > 0) {
            if (cps_get_num_from_db(CPS_TENANT_ADDR_INFO_TABLE, "tenant_name='%s'", tenant_name) <= 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, tenant_name :[%s] not found", interface->interface_name,tenant_name);
                ret = ERR_TENANT_NO_EXIST;
                goto out;
            }
            ret = vsm_opt_tenant_ip_check(vsm_id, tenant_name,1,interface->ipv4,interface->ipv6,interface->interface_name);
            API_CHECK_FUNC(ret, "vsm_netconf_add:vsm_opt_tenant_ip_check");
        }

        if (cps_get_num_from_db("vsm_ip_info", "vsm_id='%s' and interface_name='%s'", vsm_id, interface->interface_name) > 0) {
            // 接口已存在
            DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, interface already exists", interface->interface_name);
            ret = ERR_INTERFACE_EXIST;
            goto out;
        }
        if (interface->ipv4 && strlen(interface->ipv4) > 0) {
            if (cps_get_num_from_db("vsm_ip_info", "vsm_ipv4='%s'", interface->ipv4) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, ipv4 already exists", interface->interface_name);
                ret = ERR_IP_EXIST;
                goto out;
            }
            if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=1", interface->ipv4) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, ipv4 already used", interface->interface_name);
                ret = ERR_IP_USED;
                goto out;
            }
        }
        if (interface->ipv6 && strlen(interface->ipv6) > 0) {
            if (cps_get_num_from_db("vsm_ip_info", "vsm_ipv6='%s'", interface->ipv6) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, ipv6 already exists", interface->interface_name);
                ret = ERR_IP_EXIST;
                goto out;
            }
            if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=1", interface->ipv6) > 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, ipv6 already used", interface->interface_name);
                ret = ERR_IP_USED;
                goto out;
            }
        }
    }
    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    //组配置虚拟机网络接口调用的json数据,即传入参数
    memset(data, 0, sizeof(data));

    ret = vsm_netconfig_data_comp(request_id, vsm_id, cfg, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_netconfig_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据 
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {  
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_NETWORK);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        ret = ERR_PARAM;
        goto out;
    }

    //更新数据库
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue; // 跳过无名接口
        }
        ret = cps_insert_data_to_db("vsm_ip_info", 
        "(vsm_id,interface_name,vsm_ipv4,vsm_ipv6,vsm_maskv4,vsm_maskv6,"
        "vsm_gatewayv4,vsm_gatewayv6,bindType,is_enabled,manage,"
        "bridge_interface,vlan_id,ping,remark) "
        "values('%s','%s','%s','%s','%s','%s','%s','%s',%d,%d,%d,'%s',%d,%d,'%s')",
        vsm_id, interface->interface_name,
        interface->ipv4 ? interface->ipv4 : "",
        interface->ipv6 ? interface->ipv6 : "",
        interface->maskv4 ? interface->maskv4 : "",
        interface->maskv6 ? interface->maskv6 : "",
        interface->gatewayv4 ? interface->gatewayv4 : "",
        interface->gatewayv6 ? interface->gatewayv6 : "",
        interface->bindType,
        interface->is_enable,
        interface->manage,
        interface->bridge ? interface->bridge : "",
        interface->vlan,
        interface->ping,
        remark!=NULL && strlen(remark) > 0 ? remark : "");
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "add interface %s failed, insert interface failed", interface->interface_name);
            goto out;
        }
       
        // 插入IPv4静态路由
        for (int j = 0; j < interface->static_route_v4_count; j++) {
            if (interface->static_route_v4[j].dest && interface->static_route_v4[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',1,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v4[j].dest,
                    interface->static_route_v4[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert IPv4 route failed: %s -> %s", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next);
                    return ret;
                }
            }
        }

        // 插入IPv6静态路由
        for (int j = 0; j < interface->static_route_v6_count; j++) {
            if (interface->static_route_v6[j].dest && interface->static_route_v6[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',2,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v6[j].dest,
                    interface->static_route_v6[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "insert IPv6 route failed: %s -> %s", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next);
                    return ret;
                }
            }
        }

        // 更新IP池（仅在有租户时）
        if (tenant_name != NULL && strlen(tenant_name) > 0) {
            if (interface->ipv4 && strlen(interface->ipv4) > 0) {
                //判断ipv4是否在ip池中且是否被使用
                if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=0", interface->ipv4) > 0) {
                    ret = cps_update_data_to_db(CPS_IP_INFO_TABLE, "use_status=1 where ip='%s'", interface->ipv4);
                    if (ret) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "update ipv4 to ip pool failed: %s", interface->ipv4);
                    }
                }else{
                    ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                        "(use_status,tenant_name,ip,use) values(1,'%s','%s',0)",
                        tenant_name, interface->ipv4);
                    if (ret) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "insert ipv4 to ip pool failed: %s", interface->ipv4);
                    }
                }
                
            }
            if (interface->ipv6 && strlen(interface->ipv6) > 0) {
                if (cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip='%s' and use_status=0", interface->ipv6) > 0) {
                    ret = cps_update_data_to_db(CPS_IP_INFO_TABLE, "use_status=1 where ip='%s'", interface->ipv6);
                    if (ret) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "update ipv6 to ip pool failed: %s", interface->ipv6);
                    }
                }else{
                    ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                        "(use_status,tenant_name,ip,use) values(1,'%s','%s',0)",
                        tenant_name, interface->ipv6);
                    if (ret) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "insert ipv6 to ip pool failed: %s", interface->ipv6);
                    }
                }
            }
        }else{
            DEBUG_CPS_CLI(ERR_DEBUG, "vsm_netconf_add vsm_id:%s,tenant_name is empty",vsm_id );
            //如果租户为空,也要插入ip地址池中,但是use_status为1
            if (interface->ipv4 && strlen(interface->ipv4) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                    "(use_status,tenant_name,ip,use) values(1,'NONE_TENANT','%s',0)",
                    interface->ipv4);
            }
            
            if (interface->ipv6 && strlen(interface->ipv6) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, 
                    "(use_status,tenant_name,ip,use) values(1,'NONE_TENANT','%s',0)",
                    interface->ipv6);
            }
        }
    }

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}
//删除网卡配置
int vsm_netconf_del(char *vsm_id, net_config_t *cfg)
{
    unsigned int ret = ERR_NONE;
    char interface_name[128];
    if (vsm_id == NULL || cfg == NULL) return ERR_PARAM;
    // 1. 组包并发送给服务器，等服务器反馈正常后再删除数据库
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char request_id[16] = {0};
    unsigned char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char old_ipv4[64] = {0};
    char old_ipv6[64] = {0};
    char cluster_id[64] = {0};
    //判断虚机是否存在集群中
    memset(cluster_id, 0, sizeof(cluster_id));
    ret = cps_get_data_from_db(CPS_CLUSTER_VSM_TABLE, "vsm_id", (void *)vsm_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    if (ret == 0 && strlen(cluster_id) > 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm %s is in cluster %s, can't delete network", vsm_id, cluster_id);
        ret = ERR_VSM_OPT;
        goto out;
    }
    // 获取本机IP
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    // 组json数据
    ret = vsm_netconfig_data_comp(request_id, vsm_id, cfg, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_netconfig_data_comp");
    data_len = strlen(data);

    // 对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    // 对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    // 组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    // 组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_NETWORK);

    // 发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        ret = ERR_PARAM;
        goto out;
    }
    // 删除指定接口的路由与网卡记录
    for (int i = 0; i < cfg->interface_count; i++) {
        memset(interface_name, 0, sizeof(interface_name));
        if (cfg->interfaces[i].interface_name)
            strncpy(interface_name, cfg->interfaces[i].interface_name, sizeof(interface_name)-1);

        if (strlen(interface_name) == 0) {
            continue; // 跳过无名接口
        }
         // 获取旧的IP地址用于清理IP池
         memset(old_ipv4, 0, sizeof(old_ipv4));
         memset(old_ipv6, 0, sizeof(old_ipv6));
         //查询vsm_ip_info表中vsm_id为vsm_id的vsm_ipv4 interface_name为interface_name的vsm_ipv4
        ret = cps_get_vsm_interface_ip(vsm_id, interface_name, old_ipv4, old_ipv6);
        API_CHECK_FUNC(ret, "cps_get_vsm_interface_ip");
         if (ret == 0 && strlen(old_ipv4) > 0) {
             ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip='%s'", old_ipv4);
             if (ret) {
                 DEBUG_CPS_CLI(ERR_DEBUG, "delete old ipv4 from ip pool failed: %s", old_ipv4);
             }
         }
         if (ret == 0 && strlen(old_ipv6) > 0) {
             ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip='%s'", old_ipv6);
             if (ret) {
                 DEBUG_CPS_CLI(ERR_DEBUG, "delete old ipv6 from ip pool failed: %s", old_ipv6);
             }
         }
        // 先删路由
        ret = cps_del_data_from_db("tb_vsm_route", "vsmId='%s' and interface_name='%s'", vsm_id, interface_name);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "delete routes failed for %s", interface_name);
            goto out;
        }

        // 再删接口信息
        ret = cps_del_data_from_db("vsm_ip_info", "vsm_id='%s' and interface_name='%s'", vsm_id, interface_name);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "delete interface failed for %s", interface_name);
            goto out;
        }
    }

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}



//网卡操作opt_type操作类型，1为创建，2为删除，3为修改
int cps_vsm_net_config(int argc, char *argv[] ,int opt_type)
{
    unsigned int ret = ERR_NONE;
    vsm_netconf netconf;
    
    char *vsm_id = argv[5];
    char *interface_json = argv[7];
    char *tenant_name = NULL;
    char *remark = NULL;
    net_config_t cfg;
    memset(&cfg, 0, sizeof(cfg));
    ret = parse_interface_json(interface_json, &cfg);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "parse_interface_json failed: %0x", ret);
        goto out;
    }
    if (argc > 8) {
        if (strcmp(argv[8], "tenant_name") == 0) {
            tenant_name = argv[9];
        }
    }
    if (argc > 10) {
        if (strcmp(argv[10], "remark") == 0) {
            remark = argv[11];
        }
    }

    netconf.vsm_ipv4 = NULL;
    netconf.vsm_maskv4 = NULL;
    netconf.vsm_ipv6 = NULL;
    netconf.vsm_maskv6 = NULL;
    netconf.vsm_gatewayv4 = NULL;
    netconf.vsm_gatewayv6 = NULL;
    char tenant[128] = {0};

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...argc:%d\n",__func__,argc);
    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    if (tenant_name != NULL) {
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strlen(tenant) > 0 && (strcmp(tenant, tenant_name) != 0)) {
            return ERR_SUPP_OPT;
        }
    }
    ret = vsm_opt_state_check(vsm_id, "netconf", tenant_name);
    API_CHECK_FUNC(ret, "vsm_opt_state_check");
    cfg.rawjson = interface_json;
    if (opt_type == 1) {
        cfg.opt_type = "add_interface";
        ret = vsm_netconf_add(vsm_id, &cfg, tenant_name,remark);
        API_CHECK_FUNC(ret, "vsm_netconf_add");
    } else if (opt_type == 2) {
        cfg.opt_type = "del_interface";
        ret = vsm_netconf_del(vsm_id, &cfg);
        API_CHECK_FUNC(ret, "vsm_netconf_del");
    } else if (opt_type == 3) {
        cfg.opt_type = "set_interface";
        ret = vsm_netconf_set(vsm_id, &cfg,tenant_name,remark);
        API_CHECK_FUNC(ret, "vsm_netconf_set");
    }
    API_CHECK_FUNC(ret, "vsm_netconf_set");
out:
    free_net_config(&cfg);
    print_errmsg(ret);
    return ret;
}

int cps_vsm_opt_proc(char *opt, char *tenant_name, char *vsm_id)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    int opt_type = 0, in_state = 0;
    int cpu_model = 0;
    char data[DATA_MAX_LEN] = {0};
    char request_id[16] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char tenant[128] = {0};
    char callback_addr[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    if (opt == NULL || vsm_id ==NULL)
        return ERR_PARAM;

    //获取租户名称，不一致不允许操作
    if (tenant_name != NULL) {
        memset(tenant, 0, sizeof(tenant));
        ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");
        if (strcmp(tenant, tenant_name) != 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "tenant name:%s %s\n", tenant, tenant_name);
            return ERR_SUPP_OPT;
        }
    }

    //获取回调地址
    ret = cps_get_callback_addr(callback_addr, sizeof(callback_addr));
    API_CHECK_FUNC(ret, "cps_get_callback_addr");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    //获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    ret = vsm_opt_data_comp(request_id, opt, vsm_id, callback_addr, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_opt_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对base64数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");

    //组tcp传输body数据
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "host ip:%s\n", host_ip);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);

    //组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {
        ret = ERR_MALLOC;
        goto out;
    }

    //命令行操作转换为内部状态码和接口操作类型
    cps_state_trans(opt, &opt_type, &in_state);

    DEBUG_CPS_CLI(COMMON_DEBUG, "opt:%s opt_type:%d\n", opt, in_state);
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, in_state);

    //发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    API_CHECK_FUNC(ret, "cps_request_send");

    //更新虚拟机管理数据库
    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "request_id=\'%s\',opt_state=%d where vsm_id = \'%s\'", request_id, opt_type, vsm_id);
out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int cps_set_vsm_drift(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *tenant_name = argv[5];
    char *vsm_id = argv[7];
    int drift_flag = atoi(argv[8]);
    char tenant[128];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start\r\n", __func__);
    //获取租户名称，不一致不允许操作
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", vsm_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    if (strcmp(tenant, tenant_name) != 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "tenant name:%s %s\n", tenant, tenant_name);
        return ERR_SUPP_OPT;
    }

    ret = cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "is_drift=%d where vsm_id=\'%s\'", (drift_flag == 1)? 1 : 0, vsm_id);

out:
    return ret;
}

int cps_vsm_opt(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    int index = 5;
    char *opt = argv[3];
    char *tenant_name = NULL;
    char *vsm_id = NULL;

    if ((strcmp(argv[4], "tenant_name") == 0)) {
        tenant_name = argv[5];
        index += 2;
    }
    vsm_id = argv[index];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);

    //判断虚拟机状态是否允许操作
    ret = vsm_opt_state_check(vsm_id, opt, tenant_name);
    API_CHECK_FUNC(ret, "vsm_opt_state_check");

    ret = cps_vsm_opt_proc(opt, tenant_name, vsm_id);
    API_CHECK_FUNC(ret, "cps_vsm_opt_proc");

out:
    print_errmsg(ret);
    return ret;
}

/**
 * @brief VSM路由配置函数 - 支持添加和删除路由
 * @param argc 参数个数
 * @param argv 参数数组
 * @param opt_type 操作类型：1=添加路由，2=删除路由
 * @return 0成功，非0失败
 */
int cps_vsm_route_config(int argc, char *argv[], int opt_type)
{
    int ret = ERR_NONE;
    char tenant_name[128] = {0};
    char vsm_id[128] = {0};
    char interface_json[2048] = {0};
    net_config_t cfg;
    
    if (argc < 6) {
        DEBUG_CPS_CLI(ERR_DEBUG, "参数不足，需要至少6个参数");
        return ERR_PARAM;
    }
    
    // 解析命令行参数
    // 格式: cps vsm netconf route_add/route_del tenant_name <tenant> vsm_id <vsm_id> route <route_json>
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "tenant_name") == 0 && i + 1 < argc) {
            strncpy(tenant_name, argv[i + 1], sizeof(tenant_name) - 1);
            i++; // 跳过值
        } else if (strcmp(argv[i], "vsm_id") == 0 && i + 1 < argc) {
            strncpy(vsm_id, argv[i + 1], sizeof(vsm_id) - 1);
            i++; // 跳过值
        } else if (strcmp(argv[i], "interface") == 0 && i + 1 < argc) {
            strncpy(interface_json, argv[i + 1], sizeof(interface_json) - 1);
            i++; // 跳过值
        }
    }
    
    // 验证必要参数
    if  (strlen(vsm_id) == 0 || strlen(interface_json) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "缺少必要参数: vsm_id=%s, interface_json=%s", 
                      vsm_id, interface_json);
        return ERR_PARAM;
    }
    
    // 解析路由JSON
    memset(&cfg, 0, sizeof(cfg));
    ret = parse_interface_json(interface_json, &cfg);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "解析路由JSON失败: %0x", ret);
        return ret;
    }
    cfg.rawjson = interface_json;
    if (opt_type == 1) {
        // 添加路由
        cfg.opt_type = "add_route";
        DEBUG_CPS_CLI(COMMON_DEBUG, "开始添加路由: vsm_id=%s, tenant=%s", vsm_id, tenant_name);
        ret = cps_vsm_route_add(vsm_id, &cfg);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "添加路由失败: %0x", ret);
        } else {
            DEBUG_CPS_CLI(COMMON_DEBUG, "添加路由成功: vsm_id=%s", vsm_id);
        }
    } else if (opt_type == 2) {
        // 删除路由
        cfg.opt_type = "del_route";
        DEBUG_CPS_CLI(COMMON_DEBUG, "开始删除路由: vsm_id=%s, tenant=%s", vsm_id, tenant_name);
        ret = cps_vsm_route_del(vsm_id, &cfg);
        if (ret) {
            DEBUG_CPS_CLI(ERR_DEBUG, "删除路由失败: %0x", ret);
        } else {
            DEBUG_CPS_CLI(COMMON_DEBUG, "删除路由成功: vsm_id=%s", vsm_id);
        }
    } else {
        DEBUG_CPS_CLI(ERR_DEBUG, "不支持的操作类型: %d", opt_type);
        ret = ERR_SUPP_OPT;
    }
    
    // 清理资源
    free_net_config(&cfg);
    close_cps_db();
    
    return ret;
}

/**
 * @brief 添加VSM路由 - 通过报文发送给服务器
 * @param vsm_id VSM ID
 * @param cfg 网络配置
 * @return 0成功，非0失败
 */
int cps_vsm_route_add(char *vsm_id, net_config_t *cfg)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    if (vsm_id == NULL || cfg == NULL) {
        return ERR_PARAM;
    }

    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_generate_random failed! error=%0x", ret);
        return ret;
    }

    // 获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    // 组配置虚拟机路由调用的json数据,即传入参数
    memset(data, 0, sizeof(data));
    
    ret = vsm_netconfig_data_comp(request_id, vsm_id, cfg, data, sizeof(data));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm_netconfig_data_comp failed! error=%0x", ret);
        return ret;
    }
    data_len = strlen(data);

    // 对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    // 对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sign_with_internal_key failed! error=%0x", ret);
        goto out;
    }

    // 组tcp传输body数据 
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_msg_body_comp failed! error=%0x", ret);
        goto out;
    }

    // 组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {  
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_NETWORK);

    // 发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        goto out;
    }

    // 等待回调确认后，更新数据库
    DEBUG_CPS_CLI(COMMON_DEBUG, "路由添加请求已发送，等待服务器确认: vsm_id=%s, request_id=%s", vsm_id, request_id);
    
    // 根据表结构插入路由数据
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue;
        }
        
        // 插入IPv4静态路由
        for (int j = 0; j < interface->static_route_v4_count; j++) {
            if (interface->static_route_v4[j].dest && interface->static_route_v4[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',1,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v4[j].dest,
                    interface->static_route_v4[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "插入IPv4路由失败: %s -> %s, error=%0x", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next, ret);
                    goto out;
                } else {
                    DEBUG_CPS_CLI(COMMON_DEBUG, "插入IPv4路由成功: %s -> %s via %s", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next,
                                 interface->interface_name);
                }
            }
        }
        
        // 插入IPv6静态路由
        for (int j = 0; j < interface->static_route_v6_count; j++) {
            if (interface->static_route_v6[j].dest && interface->static_route_v6[j].next) {
                ret = cps_insert_data_to_db("tb_vsm_route", 
                    "(vsmId,interface_name,route_type,dest,next) values('%s','%s',2,'%s','%s')",
                    vsm_id, interface->interface_name,
                    interface->static_route_v6[j].dest,
                    interface->static_route_v6[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "插入IPv6路由失败: %s -> %s, error=%0x", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next, ret);
                    goto out;
                } else {
                    DEBUG_CPS_CLI(COMMON_DEBUG, "插入IPv6路由成功: %s -> %s via %s", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next,
                                 interface->interface_name);
                }
            }
        }
    }
out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

/**
 * @brief 删除VSM路由 - 通过报文发送给服务器
 * @param vsm_id VSM ID
 * @param cfg 网络配置
 * @return 0成功，非0失败
 */
int cps_vsm_route_del(char *vsm_id, net_config_t *cfg)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;

    if (vsm_id == NULL || cfg == NULL) {
        return ERR_PARAM;
    }

    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_generate_random failed! error=%0x", ret);
        return ret;
    }

    // 获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    // 组配置虚拟机路由调用的json数据,即传入参数
    memset(data, 0, sizeof(data));
    
    ret = vsm_netconfig_data_comp(request_id, vsm_id, cfg, data, sizeof(data));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm_netconfig_data_comp failed! error=%0x", ret);
        return ret;
    }
    data_len = strlen(data);

    // 对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    // 对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sign_with_internal_key failed! error=%0x", ret);
        goto out;
    }

    // 组tcp传输body数据 
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_msg_body_comp failed! error=%0x", ret);
        goto out;
    }

    // 组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {  
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, CMD_NETWORK);

    // 发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        goto out;
    }

    // 等待回调确认后，删除数据库中的路由记录
    DEBUG_CPS_CLI(COMMON_DEBUG, "路由删除请求已发送,服务器确认: vsm_id=%s, request_id=%s", vsm_id, request_id);
    
    // 根据表结构删除路由数据
    for (int i = 0; i < cfg->interface_count; i++) {
        interface_cfg_t* interface = &cfg->interfaces[i];
        
        if (!interface->interface_name || strlen(interface->interface_name) == 0) {
            continue;
        }
        
        // 删除IPv4静态路由
        for (int j = 0; j < interface->static_route_v4_count; j++) {
            if (interface->static_route_v4[j].dest && interface->static_route_v4[j].next) {
                ret = cps_del_data_from_db("tb_vsm_route", 
                    "vsmId='%s' and interface_name='%s' and route_type=1 and dest='%s' and next='%s'",
                    vsm_id, interface->interface_name, 
                    interface->static_route_v4[j].dest,
                    interface->static_route_v4[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "删除IPv4路由失败: %s -> %s, error=%0x", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next, ret);
                    goto out;
                } else {
                    DEBUG_CPS_CLI(COMMON_DEBUG, "删除IPv4路由成功: %s -> %s via %s", 
                                 interface->static_route_v4[j].dest,
                                 interface->static_route_v4[j].next,
                                 interface->interface_name);
                }
            }
        }
        
        // 删除IPv6静态路由
        for (int j = 0; j < interface->static_route_v6_count; j++) {
            if (interface->static_route_v6[j].dest && interface->static_route_v6[j].next) {
                ret = cps_del_data_from_db("tb_vsm_route", 
                    "vsmId='%s' and interface_name='%s' and route_type=2 and dest='%s' and next='%s'",
                    vsm_id, interface->interface_name, 
                    interface->static_route_v6[j].dest,
                    interface->static_route_v6[j].next);
                if (ret) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "删除IPv6路由失败: %s -> %s, error=%0x", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next, ret);
                    goto out;
                } else {
                    DEBUG_CPS_CLI(COMMON_DEBUG, "删除IPv6路由成功: %s -> %s via %s", 
                                 interface->static_route_v6[j].dest,
                                 interface->static_route_v6[j].next,
                                 interface->interface_name);
                }
            }
        }
    }

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int cps_vsm_manage_service_data_comp(char *request_id, char *vsm_id, char *srv_type, char *status, char *config, char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL || data == NULL)
        return ERR_PARAM;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
    cJSON_AddStringToObject(root, "oprType", "manage_service");
    cJSON *config_obj = cJSON_CreateObject();
    cJSON_AddStringToObject(config_obj, "serviceType", srv_type);
    cJSON_AddStringToObject(config_obj, "serviceStatus", status);
    if (strcmp(srv_type, "3") == 0) {
        cJSON_AddStringToObject(config_obj, "snmpIp", config);
    }
    cJSON_AddItemToObject(root, "config", config_obj);
    pData = cJSON_PrintUnformatted(root);
    if (pData == NULL) {
        ret = ERR_MALLOC;
        goto out;
    }
out:
    strcpy(data, pData);
    data_len = strlen(data);
    free(pData);
    cJSON_Delete(root);
    return ret;
}


int cps_vsm_manage_service_build(char *vsm_id, char *srv_type, char *status, char *config)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    if (vsm_id == NULL || srv_type == NULL || status == NULL) {
        return ERR_PARAM;
    }
    if (strcmp(srv_type, "3") == 0 && (config == NULL || strlen(config) == 0)) {
        return ERR_PARAM;
    }
    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_generate_random failed! error=%0x", ret);
        return ret;
    }
     
    // 获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    memset(data, 0, sizeof(data));
        
    ret = cps_vsm_manage_service_data_comp(request_id, vsm_id, srv_type, status, config, data, sizeof(data));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm_netconfig_data_comp failed! error=%0x", ret);
        return ret;
    }
    data_len = strlen(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "http_body: %s", data);    
    // 对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    // 对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sign_with_internal_key failed! error=%0x", ret);
        goto out;
    }

    // 组tcp传输body数据 
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_msg_body_comp failed! error=%0x", ret);
        goto out;
    }

    // 组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {  
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, 1);

    // 发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        goto out;
    }

    // 等待回调确认后
    DEBUG_CPS_CLI(COMMON_DEBUG, "服务管理请求已发送,服务器确认: vsm_id=%s, request_id=%s, srv_type=%s, status=%s", vsm_id, request_id, srv_type, status);

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}
int cps_vsm_manage_service_option(int argc, char *argv[])
{
    char vsm_id[64] = {0};
    char service_type[64] = {0};
    char opt[64] = {0};
    char config[1024] = {0};
    if (argc < 5) {
        return ERR_PARAM;
    }
    strcpy(vsm_id, argv[3]);
    strcpy(service_type, argv[5]);
    strcpy(opt, argv[7]);
    if (argc >= 9) {
        strcpy(config, argv[9]);
    }
    
    return cps_vsm_manage_service_build(vsm_id, service_type, opt, config);
}

int  printf_vsm_manage_service_status(char *response_data,char *request_id)
{
    cJSON *root = NULL;
    cJSON *status = NULL;
    cJSON *result = NULL;
    cJSON *error_msg = NULL;
    cJSON *request_id_str = NULL;
    if (response_data == NULL || request_id == NULL) {
        return ERR_PARAM;
    }
    root = cJSON_Parse(response_data);
    if (!root) {
        return ERR_PARAM;
    }
    status = cJSON_GetObjectItem(root, "status");
    result = cJSON_GetObjectItem(root, "result");
    request_id_str = cJSON_GetObjectItem(root, "requestId");
    error_msg = cJSON_GetObjectItem(root, "message");
    if (result == NULL || request_id_str == NULL) {
        return ERR_PARAM;
    }
    if (status->valueint != 200) {
        if (error_msg && error_msg->valuestring) {
           DEBUG_CPS_CLI(ERR_DEBUG, "error message: %s", error_msg->valuestring);
        }
        return status->valueint;
    }
    if (strcmp(request_id_str->valuestring, request_id) != 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "printf_vsm_manage_service_status request id:[%s] [%s]\r\n", request_id_str->valuestring, request_id);
        return ERR_REQUEST_ID;
    }
    char *result_str = cJSON_PrintUnformatted(result);
    if (result_str == NULL) {
        return ERR_MALLOC;
    }
    printf("%s\r\n", result_str);
    DEBUG_CPS_CLI(COMMON_DEBUG, "result:<< %s>>", result_str);
    free(result_str);
    return ERR_NONE;
}

int cps_get_vsm_manage_service_status_data_comp(char *request_id, char *vsm_id,  char *data, unsigned int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    if (request_id == NULL || vsm_id == NULL || data == NULL)
        return ERR_PARAM;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "vsmId", vsm_id);
    cJSON_AddStringToObject(root, "oprType", "get_manage_service");
    pData = cJSON_PrintUnformatted(root);
    if (pData == NULL) {
        ret = ERR_MALLOC;
        goto out;
    }
out:
    strcpy(data, pData);
    data_len = strlen(data);
    free(pData);
    cJSON_Delete(root);
    return ret;
}
int cps_get_vsm_manage_service_status_build(char *vsm_id)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int sign_len = 0;
    char request_id[16] = {0};
    char data[DATA_MAX_LEN] = {0};
    char sign_data[1024] = {0};
    char host_ip[64] = {0};
    char *data_base64 = NULL;
    msg_header_t *msg_header = NULL;
    if (vsm_id == NULL) {
        return ERR_PARAM;
    }
    // 生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_generate_random failed! error=%0x", ret);
        return ret;
    }
    // 获取host ip
    ret = cps_get_data_from_db(CPS_VSM_MANAGE_TABLE, "vsm_id", (void *)vsm_id, "host_ip", host_ip, sizeof(host_ip), false);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_data_from_db failed! error=%0x", ret);
        return ret;
    }

    // 组配置虚拟机路由调用的json数据,即传入参数
    memset(data, 0, sizeof(data));
    ret = cps_get_vsm_manage_service_status_data_comp(request_id, vsm_id, data, sizeof(data));
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_vsm_manage_service_status_data_comp failed! error=%0x", ret);
        return ret;
    }
    data_len = strlen(data);
    DEBUG_CPS_CLI(COMMON_DEBUG, "http_body: %s", data);    
    // 对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    // 对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sign_with_internal_key failed! error=%0x", ret);
        goto out;
    }

    // 组tcp传输body数据 
    memset(data, 0, sizeof(data));
    body_len = sizeof(data);
    ret = cps_msg_body_comp(host_ip, request_id, sign_data, data_base64, data_len, data, &body_len);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_msg_body_comp failed! error=%0x", ret);
        goto out;
    }

    // 组tcp包
    msg_header = (msg_header_t *)malloc(sizeof(msg_header_t) + body_len);
    if (!msg_header) {  
        ret = ERR_MALLOC;
        goto out;
    }
    cps_request_init(msg_header, data, body_len, VSM_MAIN_TYPE, 1);

    // 发送tcp请求,并获取返回状态
    ret = cps_request_send(msg_header, printf_vsm_manage_service_status, request_id);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cps_request_send failed! error=%0x", ret);
        goto out;
    }

    // 等待回调确认后   
    DEBUG_CPS_CLI(COMMON_DEBUG, "获取服务管理状态请求已发送,服务器已确认: vsm_id=%s, request_id=%s", vsm_id, request_id);

out:
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    return ret;
}

int cps_get_vsm_manage_service_status(int argc, char *argv[])
{
    char vsm_id[64] = {0};
    if (argc < 4) {
        return ERR_PARAM;
    }
    strcpy(vsm_id, argv[4]);
    return cps_get_vsm_manage_service_status_build(vsm_id);
}