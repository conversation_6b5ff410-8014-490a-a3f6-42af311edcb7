/*
 * Project: base
 * Moudle:
 * File: rights_and_device_key_public.c
 * Created Date: 2023-08-31 16:01:48
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "../cps_common.h"
#include "encrypt.h"
#include "key_operate.h"
#include "key_public.h"
#include "key_store.h"
#include "key_support.h"
#include "sm2_sign_and_verify.h"
#include "sm3_with_preprocess.h"
#include "sm4.h"

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/* ======================================================================================
 * implementation
 */
int sm4_encrypt_data(
    unsigned char* in_data,
    unsigned int   indata_len,
    unsigned char* out_data,
    unsigned int*  out_length) {
    if (in_data == NULL || out_data == NULL || out_length == NULL) return -1;
    unsigned char* pEncData     = NULL;
    unsigned char* padding_data = NULL;
    // padding补位
    int padding_data_len = 0;
    int ret              = -1;
    padding_data_len     = (indata_len + 15) / 16 * 16;
    padding_data         = (unsigned char*)malloc(padding_data_len);
    // 默认填充0
    memset(padding_data, 0, padding_data_len);
    if (padding_data == NULL) goto out;

    memcpy(padding_data, in_data, indata_len);
    // 初始化key向量值
    unsigned char key[16];
    for (unsigned int i = 0; i < 16; i++) key[i] = i;
    // 申请加密输出buffer，不建议大数据加密使用该方式
    pEncData = (unsigned char*)malloc(padding_data_len);
    if (pEncData == NULL) goto out;
    // 设置key值
    SM4_KEY iKey;
    sm4_SetKey(&iKey, key, 1);
    unsigned int encOutLength = padding_data_len;
    // 加密
    if (enin_sm4((const unsigned char*)padding_data, padding_data_len, pEncData, &encOutLength, &iKey, 0) != 0)
        goto out;

    memset(out_data, 0, sizeof(out_length));
    memcpy(out_data, pEncData, encOutLength);
    *out_length = encOutLength;
    ret         = 0;
out:
    if (padding_data) free(padding_data);
    if (pEncData) free(pEncData);
    return ret;
}

int sm4_decrypt_data(
    unsigned char* in_data,
    unsigned int   indata_len,
    unsigned char* out_data,
    unsigned int*  out_length) {
    int ret = -1;
    if (in_data == NULL || out_data == NULL || out_length == NULL) return -1;
    unsigned char* decrypt_data = NULL;
    unsigned char* pEnData      = NULL;

    pEnData              = malloc(indata_len);
    unsigned int iEnData = indata_len;
    memcpy(pEnData, in_data, indata_len);
    iEnData = indata_len;
    ret     = 0;
    if (iEnData % 16) goto out;
    // 初始化key向量值
    unsigned char key[16];
    for (unsigned int i = 0; i < 16; i++) key[i] = i;
    // 设置SM4key
    SM4_KEY iKey;
    sm4_SetKey(&iKey, key, 1);
    // 申请解密数据
    decrypt_data = (unsigned char*)malloc(iEnData);
    if (decrypt_data == NULL) goto out;
    unsigned int decOutLength = iEnData;
    // 数据解密
    if (dein_sm4((const unsigned char*)pEnData, iEnData, decrypt_data, &decOutLength, &iKey, 0) != 0) goto out;
    memcpy(out_data, decrypt_data, decOutLength);
    *out_length = decOutLength;
    ret         = 0;
out:
    if (pEnData) free(pEnData);
    if (decrypt_data) free(decrypt_data);
    return ret;
}

int encrypt_with_kek(unsigned char* in_data, unsigned char* out_data, unsigned int data_len) {
    int out_len = 0;
    return sm4_encrypt_data(in_data, data_len, out_data, &out_len);
}

int decrypt_with_kek(unsigned char* in_data, unsigned char* out_data, unsigned int data_len) {
    int out_len = 0;
    return sm4_decrypt_data(in_data, data_len, out_data, &out_len);
}

/**
 * @brief 验签操作
 *
 * @param pub_key ecc公钥结构
 * @param Hash 哈希值
 * @param SignData 签名数据
 * @return int 成功返回0
 */
int verify_ecc(void* pub_key, char* Hash, char* SignData) {
    int                  ret     = 0;
    unsigned char        pub[65] = {0};
    SM2_SIGNATURE_STRUCT sign_data;
    memset(&sign_data, 0, sizeof(SM2_SIGNATURE_STRUCT));

    char  de_hash[33] = {0};
    char* de_SignData = (char*)malloc(strlen(SignData));
    memset(de_SignData, '\0', strlen(SignData));

    memcpy(pub + 1, pub_key + 36, 32);
    memcpy(pub + 33, pub_key + 100, 32);
    pub[0] = 4;

    base64_decode(Hash, de_hash, strlen(Hash));
    base64_decode(SignData, de_SignData, strlen(SignData));

    memcpy(sign_data.r_coordinate, de_SignData, 32);
    memcpy(sign_data.s_coordinate, de_SignData + 32, 32);

    // 1.验签
    ret = sm2_verify_sig(de_hash, 32, pub, &sign_data);
    API_CHECK_FUNC(ret, "sm2_verify_sig");

out:
    if (de_SignData) {
        free(de_SignData);
    }

    return ret;
}

/**
 * @brief 获取签名公钥
 *
 * @param out_key 结构(头|04|x|y),base64编码
 * @return int 成功返回0
 */
int get_sign_pub_key(unsigned char* out_key) {
    int ret = 0;
    if (0) {
        InterkeyStore    key_info;
        ECCrefPrivateKey pri_key;
        ECCrefPublicKey  pub_key;
        int              pub_key_len  = sizeof(ECCrefPublicKey);
        char             base_pri[64] = {0};
        char             pub[92] = {0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01, 0x06,
                                    0x08, 0x2A, 0x81, 0x1C, 0xCF, 0x55, 0x01, 0x82, 0x2D, 0x03, 0x42, 0x00, 0x04};

        // char sm2_head[27]                  =
        // {0x30,0x59,0x30,0x13,0x06,0x07,0x2A,0x86,0x48,0xCE,0x3D,0x02,0x01,0x06,0x08,0x2A,0x81,0x1C,0xCF,0x55,0x01,0x82,0x2D,0x03,0x42,0x00,0x04};
        key_info.key_node[1].iKeyType      = 0;
        key_info.key_node[1].iKeyIndex     = 1;
        key_info.key_node[1].iKeyFlag      = ENC_KEY;
        key_info.key_node[1].iKeyBits      = 256;
        key_info.key_node[1].pKeyPriInfo   = &pri_key;
        key_info.key_node[1].iKeyPriLength = sizeof(ECCrefPrivateKey);
        key_info.key_node[1].pKeyPubInfo   = &pub_key;
        key_info.key_node[1].iKeyPubLength = pub_key_len;

        ret = GetDeviceKey(ENC_KEY, &key_info, decrypt_with_kek);
        API_CHECK_FUNC(ret, "GetDeviceKey");

        // base64编码
        memcpy(pub + 27, pub_key.x + 32, 32);
        memcpy(pub + 27 + 32, pub_key.y + 32, 32);
        base64_encode(pub, out_key, 91);
    } else {
        memcpy(
            out_key,
            "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE5jcqAr//wdrXRayVmo0tFSkw0hwg+6J/CCZsczikzTOwDW1YQCHupyHuB/"
            "zhJvcBWuAlD3z5xq2UwTePWBW0Tg==",
            125);
    }

out:

    return ret;
}

/**
 * @brief 获取签名公钥
 *
 * @param out_key 结构(头|04|x|y)
 * @return int 成功返回0
 */
int get_sign_pub_key_without_base64(unsigned char* out_key) {
    int              ret = 0;
    InterkeyStore    key_info;
    ECCrefPrivateKey pri_key;
    ECCrefPublicKey  pub_key;
    int              pub_key_len = sizeof(ECCrefPublicKey);

    key_info.key_node[1].iKeyType      = 0;
    key_info.key_node[1].iKeyIndex     = 1;
    key_info.key_node[1].iKeyFlag      = ENC_KEY;
    key_info.key_node[1].iKeyBits      = 256;
    key_info.key_node[1].pKeyPriInfo   = &pri_key;
    key_info.key_node[1].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[1].pKeyPubInfo   = &pub_key;
    key_info.key_node[1].iKeyPubLength = pub_key_len;

    ret = GetDeviceKey(ENC_KEY, &key_info, decrypt_with_kek);
    API_CHECK_FUNC(ret, "GetDeviceKey");

    memcpy(out_key + 1, pub_key.x + 32, 32);
    memcpy(out_key + 33, pub_key.y + 32, 32);

out:
    return ret;
}

/**
 * @brief 获取签名私钥
 *
 * @param out_key 结构(k),base64编码
 * @return int 成功返回0
 */
int get_sign_pri_key(unsigned char* out_key) {
    int              ret = 0;
    InterkeyStore    key_info;
    ECCrefPrivateKey pri_key;
    ECCrefPublicKey  pub_key;
    int              pub_key_len = sizeof(ECCrefPublicKey);
    char             pub[66]     = {0};

    key_info.key_node[1].iKeyType      = 0;
    key_info.key_node[1].iKeyIndex     = 1;
    key_info.key_node[1].iKeyFlag      = ENC_KEY;
    key_info.key_node[1].iKeyBits      = 256;
    key_info.key_node[1].pKeyPriInfo   = &pri_key;
    key_info.key_node[1].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[1].pKeyPubInfo   = &pub_key;
    key_info.key_node[1].iKeyPubLength = pub_key_len;

    ret = GetDeviceKey(ENC_KEY, &key_info, decrypt_with_kek);
    API_CHECK_FUNC(ret, "GetDeviceKey");

    memcpy(out_key, pri_key.K + 32, 32);
out:

    return ret;
}

/**
 * @brief 获取sm3 hash值
 *
 * @param in_data 输入数据
 * @param in_data_le 输入数据长度
 * @param hash 值,默认长度32
 * @return int 成功返回0
 */
int get_sm3_hash(unsigned char* in_data, int in_data_len, char* hash) {
    int           ret         = 0;
    unsigned char pub_key[65] = {0};
    // 获取公钥
    if(0){
        ret = get_sign_pub_key_without_base64(pub_key);
        API_CHECK_FUNC(ret, "get_sign_pub_key_without_base64");
    }else{
        char dd[256]    = {0};
        base64_decode("MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE5jcqAr//wdrXRayVmo0tFSkw0hwg+6J/CCZsczikzTOwDW1YQCHupyHuB/"
        "zhJvcBWuAlD3z5xq2UwTePWBW0Tg==", dd, 125);
        memcpy(pub_key,dd+26,65);
    }

    ret = sm3_digest_init("1234567812345678", 16, pub_key);
    API_CHECK_FUNC(ret, "sm3_digest_init");

    ret = sm3_digest_update(in_data, in_data_len);
    API_CHECK_FUNC(ret, "sm3_digest_update");

    ret = sm3_digest_final(hash);
    API_CHECK_FUNC(ret, "sm3_digest_final");

out:
    return ret;
}

/**
 * @brief 使用内部私钥对数据进行签名
 *
 * @param in_data 输入数据
 * @param in_data_len 输入数据长度
 * @param out_signature 返回的签名值,base64编码
 * @param sign_len 签名值长度
 * @return int 成功返回0
 */
int sign_with_internal_key(unsigned char* in_data, int in_data_len, char* out_signature, int* sign_len) {
    int                  ret = 0;
    SM2_SIGNATURE_STRUCT signature;
    unsigned char        pri_key[32] = {0};
    char                 hash[32]    = {0};
    // 1.获取内部私钥
    if (0) {
        ret = get_sign_pri_key(pri_key);
        API_CHECK_FUNC(ret, "get_sign_pri_key");
    } else {
        base64_decode("Sd6/c+MZXoLnJQeEePtoPK8vIm0geJ4andsL1pV2bZE=", pri_key, 45);
    }

    // 获取数据hash
    ret = get_sm3_hash(in_data, in_data_len, hash);
    API_CHECK_FUNC(ret, "get_sm3_hash");

    // 2.签名
    ret = sm2_sign_data(hash, 32, pri_key, &signature);
    API_CHECK_FUNC(ret, "sm2_sign_data");

    // base64编码
    char* tmp = base64_encode((char*)&signature, out_signature, 64);
    API_CHECK_FUNC(!tmp, "base64_encode");

    *sign_len = strlen(out_signature);
out:
    return ret;
}
