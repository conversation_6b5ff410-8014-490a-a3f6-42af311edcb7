# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-09-04 09:22+0800\n"
"Last-Translator: wanghai <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: cps_common.c:77 cps_common.c:95 cps_vsm_manage.c:1493
msgid "success"
msgstr "成功"

#: cps_common.c:96
msgid "sql operate failed"
msgstr "数据库操作失败"

#: cps_common.c:97
msgid "parameter error"
msgstr "参数错误"

#: cps_common.c:98
msgid "duplicate work orders"
msgstr "重复的工单"

#: cps_common.c:99
msgid "malloc failed"
msgstr "内存分配失败!"

#: cps_common.c:100
msgid "connect to the server failed"
msgstr "连接服务失败"

#: cps_common.c:101
msgid "request id did not match"
msgstr "request id不匹配"

#: cps_common.c:102
msgid "data parse failed"
msgstr "数据解析失败"

#: cps_common.c:103
msgid "number of vms is incorrect"
msgstr "虚拟机数量超出范围"

#: cps_common.c:104
msgid "cluster can be referenced by only one service"
msgstr "集群只能被一个服务所引用"

#: cps_common.c:105
msgid "cluster is still referenced, cannot be deleted"
msgstr "集群被服务引用,无法删除"

#: cps_common.c:106
msgid "cluster is still referenced, cannot be stoped"
msgstr "集群被服务引用,无法停止"

#: cps_common.c:107
msgid "cluster vrid error"
msgstr "集群vrid错误"

#: cps_common.c:108
msgid "unsupported operation"
msgstr "不支持的操作"

#: cps_common.c:109
msgid "no operation authority"
msgstr "无操作权限"

#: cps_common.c:110
msgid "get authpk failed"
msgstr "获取公钥失败!"

#: cps_common.c:111
msgid "cluster is disabled"
msgstr "集群未开启，无法启动服务"

#: cps_common.c:112
msgid "please add vsm first"
msgstr "请添加虚拟机"

#: cps_common.c:113
msgid "cluster has been referenced by service, delete the service first"
msgstr "集群已被服务引用，请先删除服务"

#: cps_common.c:114
msgid "vsm is referenced by cluster, unable to operate"
msgstr "虚拟机被集群引用，无法操作"

#: cps_common.c:115
msgid "administrator needs to add a callback address"
msgstr "请管理员添加回调地址"

#: cps_common.c:116
msgid "image upload address error"
msgstr "请检查影像上传地址"

#: cps_common.c:117
msgid "vsm is not ready, image cannot be exported"
msgstr "虚拟机尚未就绪，无法导出影像"

#: cps_common.c:118
msgid "vsm resources are insufficient to support this operation"
msgstr "资源不足，不支持该操作"

#: cps_common.c:119
msgid "vsm synchronization failed"
msgstr "虚拟机同步错误"

#: cps_common.c:120
msgid "vsm with the same name exists"
msgstr "已存在同名虚拟机"

#: cps_common.c:121
msgid "read data failure"
msgstr "接收数据失败"

#: cps_common.c:122
msgid "insufficient disk resources"
msgstr "磁盘资源不足"

#: cps_common.c:123
msgid ""
"ip addresses of the vsm to be added to the cluster must be of the same type"
msgstr "加入集群的虚拟机ip类型必须相同"

#: cps_common.c:124
msgid "vsm ip not within address_range,please change ip first"
msgstr "虚机ip不在地址池范围内,请先修改ip地址"

#: cps_common.c:125
msgid "recv error message follow"
msgstr "接收的错误信息如下"

#: cps_common.c:126
msgid "image type unauthorized"
msgstr "镜像类型不匹配"

#: cps_common.c:127
msgid "vsm type unauthorized"
msgstr "虚拟机类型不匹配"

#: cps_common.c:128 cps_work_order.c:333
#, c-format
msgid "vsm not exist"
msgstr "虚拟机不存在"

#: cps_common.c:129
msgid "tenant not exist"
msgstr "租户不存在"

#: cps_common.c:130
msgid "interface not exist"
msgstr "接口不存在"

#: cps_common.c:131
msgid "interface already exists"
msgstr "接口已存在"

#: cps_common.c:132
msgid "ip already exists"
msgstr "ip已存在"

#: cps_common.c:133
msgid "ip already used"
msgstr "ip已经被使用"

#: cps_common.c:134
msgid "unknown error"
msgstr "未知错误"

#: cps_common.c:254
#, c-format
msgid "error message: %s"
msgstr "错误信息: %s"

#: cps_device.c:1101 cps_device_monitor.c:116
msgid "device disconnected,ip is"
msgstr "设备断开连接,ip:"

#: cps_device.c:1498 cps_device.c:1560
#, c-format
msgid "need delete device!"
msgstr "需删除正在使用的设备！"

#: cps_device.c:1504 cps_device.c:1566
#, c-format
msgid "need delete tenant!"
msgstr "需删除正在使用的租户！"

#: cps_device.c:1514 cps_device.c:1520
#, c-format
msgid "device init device file failed!"
msgstr "初始化设备文件失败!"

#: cps_device.c:1526 cps_device.c:1532
#, c-format
msgid "device init usr file failed!"
msgstr "初始化用户文件失败!"

#: cps_device.c:1538 cps_device.c:1545
#, c-format
msgid "device init failed!"
msgstr "设备初始化失败!"

#: cps_device.c:1573
#, c-format
msgid "device clean failed!"
msgstr "设备恢复到初始化状态失败!"

#: cps_device.c:1617
msgid "self check failed!"
msgstr "自检失败!"

#: cps_device.c:1619
msgid "self check successful!"
msgstr "自检成功"

#: cps_device.c:1819
#, c-format
msgid "having the same name or ip!"
msgstr "名字或者ip冲突!"

#: cps_device.c:1832 cps_device.c:1836 cps_device.c:1851
#, c-format
msgid "add device faild!"
msgstr "添加设备失败!"

#: cps_device.c:1840
#, c-format
msgid "device dose not exist,ip:%s!"
msgstr "设备不存在,ip:%s!"

#: cps_device.c:1845
#, c-format
msgid "restore device info faild!!"
msgstr "恢复设备信息失败!"

#: cps_device.c:1862
#, c-format
msgid "get device info faild!"
msgstr "获取设备信息失败!"

#: cps_device.c:1880
#, c-format
msgid "device added successfully,invalid callback addr,please modify it!"
msgstr "添加设备成功,回调地址无效,请修改!"

#: cps_device.c:1902 cps_device.c:1915 cps_device.c:1922
#, c-format
msgid "delete device faild!"
msgstr "删除设备失败!"

#: cps_rights.c:542
#, c-format
msgid "sn error,not found device!"
msgstr "未找到对应序列号设备!"

#: cps_rights.c:615 cps_rights.c:799 cps_rights.c:2250
#, c-format
msgid "pin code error,remaining retry count:%d!"
msgstr "pin码错误:剩余重试次数:%d!"

#: cps_rights.c:618 cps_rights.c:802 cps_rights.c:2253
#, c-format
msgid "pin code locked!"
msgstr "USBkey已锁死"

#: cps_rights.c:621 cps_rights.c:805 cps_rights.c:2256
#, c-format
msgid "pin code invalid,remaining retry count:%d!"
msgstr "pin码无效:剩余重试次数:%d!"

#: cps_rights.c:624 cps_rights.c:808 cps_rights.c:2259
#, c-format
msgid "pin len error!"
msgstr "pin码长度错误"

#: cps_rights.c:627 cps_rights.c:811 cps_rights.c:2262
#, c-format
msgid "pin error,remaining retry count:%d!"
msgstr "pin错误:剩余重试次数:%d!"

#: cps_rights.c:697 cps_rights.c:749
#, c-format
msgid "failed to write to file!"
msgstr "信息写入文件失败!"

#: cps_rights.c:774 cps_rights.c:2223
#, c-format
msgid "device not found!"
msgstr "未找到对应设备!"

#: cps_rights.c:827 cps_rights.c:834 cps_rights.c:1751
#, c-format
msgid "user not registered!"
msgstr "用户未注册!"

#: cps_rights.c:866 cps_rights.c:896
#, c-format
msgid "can not get user key!"
msgstr "获取不到用户密钥"

#: cps_rights.c:976
#, c-format
msgid "device list not obtained!"
msgstr "未找到设备列表!"

#: cps_rights.c:1089 cps_rights.c:1226 cps_rights.c:1744
#, c-format
msgid "no corresponding tenant!"
msgstr "没有对应租户"

#: cps_rights.c:1102 cps_rights.c:1239 cps_rights.c:1463 cps_rights.c:1533
#, c-format
msgid "there are enough administrators!"
msgstr "管理员数目已达到最大,不允许添加"

#: cps_rights.c:1110 cps_rights.c:1131 cps_rights.c:1159 cps_rights.c:1182
#: cps_rights.c:1245 cps_rights.c:1265 cps_rights.c:1293 cps_rights.c:1316
#: cps_rights.c:1471 cps_rights.c:1491 cps_rights.c:1500 cps_rights.c:1539
#: cps_rights.c:1558 cps_rights.c:1567
#, c-format
msgid "add user failed!"
msgstr "添加用户失败！"

#: cps_rights.c:1591
#, c-format
msgid "parameter error!"
msgstr "参数错误"

#: cps_rights.c:1597 cps_rights.c:1604
#, c-format
msgid "not allowed to delete the last admin!"
msgstr "当前只有一个管理员,不允许删除!"

#: cps_rights.c:1612
#, c-format
msgid "delete user failed!"
msgstr "删除用户失败！"

#: cps_rights.c:1652 cps_rights.c:1672 cps_rights.c:1764 cps_rights.c:1773
#: cps_rights.c:1780 cps_rights.c:1800 cps_rights.c:1809
#, c-format
msgid "login failed!"
msgstr "登录失败!"

#: cps_rights.c:1700
#, c-format
msgid "get random faild"
msgstr "获取随机数失败!"

#: cps_rights.c:1827 cps_rights.c:1833 cps_rights.c:1840 cps_rights.c:1930
#, c-format
msgid "login out failed!"
msgstr "登出失败！"

#: cps_rights.c:1871 cps_rights.c:2106
#, c-format
msgid "open db failed!"
msgstr "打开数据库文件失败!"

#: cps_rights.c:2131
#, c-format
msgid "check failed!"
msgstr "权限检查失败!"

#: cps_rights.c:2146
#, c-format
msgid "super administrator permissions required!"
msgstr "当前操作需要超级管理员权限!"

#: cps_rights.c:2157
#, c-format
msgid "administrator permissions required!"
msgstr "当期操作需要管理员权限!"

#: cps_rights.c:2168
#, c-format
msgid "operator permissions required!"
msgstr "当前操作需要操作员权限!"

#: cps_rights.c:2179
#, c-format
msgid "auditor permissions required!"
msgstr "当前操作需要审计员权限!"

#: cps_rights.c:2189
#, c-format
msgid "tenant permissions required!"
msgstr "当前操作需要租户管理员权限!"

#: cps_rights.c:2242
#, c-format
msgid "open device failed!"
msgstr "打开设备失败!"

#: cps_rights.c:2293
#, c-format
msgid "failed to modify comments!"
msgstr "设置备注信息失败!"

#: cps_strategy.c:659
#, c-format
msgid "no addr info of tenant!"
msgstr "没有对应租户的地址信息"

#: cps_strategy.c:759
#, c-format
msgid "vsm in use,please delete them!"
msgstr "有虚拟机正在使用,请删除!"

#: cps_strategy.c:766
#, c-format
msgid "the previous address has already been assigned!"
msgstr "地址正在被使用!"

#: cps_strategy.c:776
#, c-format
msgid "set addr faild!"
msgstr "设置地址信息失败!"

#: cps_strategy.c:818
#, c-format
msgid "set image url faild,device ip is:%s"
msgstr "设置影像上传地址失败,失败设备ip为:%s"

#: cps_strategy.c:862
#, c-format
msgid "invalid callback addr,host ip is:%s!"
msgstr "无效的回调地址,宿主机ip:%s!"

#: cps_strategy.c:869
#, c-format
msgid "set callback addr faild!"
msgstr "设置回调地址失败!"

#: cps_strategy.c:885
#, c-format
msgid "failed to set image drift switch!"
msgstr "开关影像漂移失败!"

#: cps_tenant.c:114
#, c-format
msgid "having the same name or account!"
msgstr "名称或账户冲突!"

#: cps_tenant.c:146
#, c-format
msgid "add tenant failed!"
msgstr "添加租户失败！"

#: cps_tenant.c:186
#, c-format
msgid "set tenant failed!"
msgstr "设置租户信息失败!"

#: cps_tenant.c:216
#, c-format
msgid "reset admin faild!"
msgstr "重置租户管理员失败!"

#: cps_tenant.c:256
#, c-format
msgid "tenant has virtual machines in use!"
msgstr "租户有虚机正在使用!"

#: cps_tenant.c:264
#, c-format
msgid "delete tenant failed!"
msgstr "删除租户失败！"

#: cps_vsm_manage.c:1493
msgid "upgrade failed"
msgstr "升级失败"

#: cps_work_order.c:166
#, c-format
msgid "vsm type not matching"
msgstr "虚拟机类型不匹配"

#: cps_work_order.c:285
#, c-format
msgid "invalid command"
msgstr "无效命令"

#: cps_work_order.c:293
#, c-format
msgid "no corresponding tenant found!"
msgstr "没有对应租户!"

#: cps_work_order.c:304
#, c-format
msgid "address information not configured!"
msgstr "地址信息未配置!"

#: cps_work_order.c:318 cps_work_order.c:370
#, c-format
msgid "approval faild!"
msgstr "审批失败!"

#: cps_work_order.c:340
#, c-format
msgid "insufficient memory and CPU resources!"
msgstr "资源不足!"

#: cps_work_order.c:346
#, c-format
msgid "need to shut down the virtual machine!"
msgstr "需关闭对应虚机!"

#: database_api.c:210
#, c-format
msgid "the sn has already registered!"
msgstr "序列号已经被注册!"

#: database_api.c:236
#, c-format
msgid "the name has already registered!"
msgstr "用户名已经被注册!"

#: database_api.c:579
#, c-format
msgid "sn and name not corresponding!"
msgstr "序列号与用户名不对应"

#: database_api.c:583
#, c-format
msgid "sn not register!"
msgstr "序列号未被注册使用!"

#: database_api.c:1674 database_api.c:1682
#, c-format
msgid "IP conflict:%s-%s and %s !"
msgstr "IP冲突:%s-%s 与 %s !"

#: database_api.c:1771 database_api.c:1777
#, c-format
msgid "IP conflict:%s and %s !"
msgstr "IP冲突:%s 与 %s !"

#~ msgid "del addr faild!"
#~ msgstr "删除地址失败!"

#~ msgid "del addr faild3!"
#~ msgstr "删除地址失败!"

#~ msgid "the addr_name or tenant has already been assigned!"
#~ msgstr "地址名或者租户名已经被使用!"

#~ msgid "the device has been add by other cps!"
#~ msgstr "设备已经被其他云平台纳管!"

#~ msgid "not enough disk space!"
#~ msgstr "磁盘空间不足"

#~ msgid "unapproved CPU or memory resources!"
#~ msgstr "未申请cpu或者内存"

#~ msgid "start"
#~ msgstr "启动"

#~ msgid "shutdown"
#~ msgstr "关闭"

#~ msgid "ready"
#~ msgstr "就绪"

#~ msgid "error"
#~ msgstr "错误:"

#~ msgid "restart"
#~ msgstr "重启"

#~ msgid "close"
#~ msgstr "关闭"

#~ msgid "administrator needs to add a image upload address"
#~ msgstr "请管理员添加影像上传地址"

#~ msgid "http request send failed"
#~ msgstr "http请求发送失败"

#~ msgid "cpu exclusive resources are insufficient"
#~ msgstr "cpu独享资源不足"

#~ msgid "vsm start success"
#~ msgstr "虚拟机启动成功"

#~ msgid "no vsm in cluster, enable failed"
#~ msgstr "请先添加虚拟机"

#~ msgid "session id mismatch"
#~ msgstr "session id不匹配"

#~ msgid "device running!"
#~ msgstr "未找到对应设备!"

#~ msgid "need delete  tenant!"
#~ msgstr "删除租户失败！"

#~ msgid "allocation ip error!"
#~ msgstr "分配ip错误!"
