#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>

#include "cjson.h"
#include "cps_common.h"
#include "message.h"

struct {
    int   errcode;
    char* errmsg;
} errmsg_def[] = {
    {ERR_NONE, (char*)"success"},
    {ERR_SQL_OPT, (char*)"sql operate failed"},
    {ERR_PARAM, (char*)"parameter error"},
    {ERR_ORDER_DUP, (char*)"duplicate work orders"},
    {ERR_MALLOC, (char*)"malloc failed"},
    {ERR_SRV_CONNECT, (char*)"connect to the server failed"},
    {ERR_REQUEST_ID, (char*)"request id did not match"},
    {ERR_CJSON, (char*)"data parse failed"},
    {ERR_VSM_NUM, (char*)"number of vms is incorrect"},
    {ERR_CLUSTER_REF, (char*)"cluster can be referenced by only one service"},
    {ERR_CLUSTER_DEL, (char*)"cluster is still referenced, cannot be deleted"},
    {ERR_CLUSTER_STOP, (char*)"cluster is still referenced, cannot be stoped"},
    {ERR_CLUSTER_VRID, (char*)"cluster vrid error"},
    {ERR_SUPP_OPT, (char*)"unsupported operation"},
    {ERR_OPT_AUTH, (char*)"no operation authority"},
    {ERR_GET_AUTHPK, (char*)"get authpk failed"},
    {ERR_CLUSTER_ENABLE, (char*)"cluster is disabled"},
    {ERR_CLUSTER_VSM, (char*)"please add vsm first"},
    {ERR_CLUSTER_OPT, (char*)"cluster has been referenced by service, delete the service first"},
    {ERR_VSM_OPT, (char*)"vsm is referenced by cluster, unable to operate"},
    {ERR_CALLBACK_ADDR, (char*)"administrator needs to add a callback address"},
    {ERR_IMAGE_ADDR, (char*)"image upload address error"},
    {ERR_VSM_INIT, (char*)"vsm is not ready, image cannot be exported"},
    {ERR_VSM_RESOURCE, (char*)"vsm resources are insufficient to support this operation"},
    {ERR_VSM_SYMM, (char*)"vsm synchronization failed"},
    {ERR_VSM_NAME, (char*)"vsm with the same name exists"},
    {ERR_READ, (char*)"read data failure"},
    {ERR_DISK_RESOURCE, (char*)"insufficient disk resources"},
    {ERR_VSM_IP, (char*)"ip addresses of the vsm to be added to the cluster must be of the same type"},
    {ERR_ADDR_RANGE, (char*)"vsm ip not within address_range,please change ip first"},
    {ERR_RECV_MESSAGE, (char*)"recv error message follow"},
    {ERR_IMAGE_TYPE, (char*)"image type unauthorized"},
    {ERR_VSM_TYPE, (char*)"vsm type unauthorized"},
    {ERR_VSM_NO_EXIST, (char*)"vsm not exist"},
    {ERR_TENANT_NO_EXIST, (char*)"tenant not exist"},
    {ERR_INTERFACE_EXIST, (char*)"interface already exists"},
    {ERR_INTERFACE_NO_EXIST, (char*)"interface not exist"},
    {ERR_IP_EXIST, (char*)"ip already exists"},
    {ERR_IP_USED, (char*)"ip already used"},
    {ERR_UNKNOWN, (char*)"unknown error"}};

sqlite3* cps_db = NULL;

const char* queryerrmsg(int errcode) {
    int i = 0;

    for (i = 1; i < sizeof(errmsg_def) / sizeof(errmsg_def[0]); i++) {
        if (errcode == errmsg_def[i].errcode) {
            break;
        }
    }

    if (i == sizeof(errmsg_def) / sizeof(errmsg_def[0])) {
        i -= 1;
    }
    return gettext(errmsg_def[i].errmsg);
}

void print_errmsg(int errcode) {
    const char* errmsg_res = NULL;

    if (errcode == ERR_NONE) {
        fprintf(stderr, "%s", gettext("success"));
        fprintf(stderr, "\n");
        return;
    }

    errmsg_res = queryerrmsg(errcode);
    if (errmsg_res) {
        fprintf(stderr, "%s", errmsg_res);
        fprintf(stderr, "\n");
    } else {
        fprintf(stderr, "%s", queryerrmsg(ERR_UNKNOWN));
        fprintf(stderr, "\n");
    }

    return;
}

void gettext_test() {
    gettext("success");
    gettext("sql operate failed");
    gettext("parameter error");
    gettext("duplicate work orders");
    gettext("malloc failed");
    gettext("connect to the server failed");
    gettext("request id did not match");
    gettext("data parse failed");
    gettext("number of vms is incorrect");
    gettext("cluster can be referenced by only one service");
    gettext("cluster is still referenced, cannot be deleted");
    gettext("cluster is still referenced, cannot be stoped");
    gettext("cluster vrid error");
    gettext("unsupported operation");
    gettext("no operation authority");
    gettext("get authpk failed");
    gettext("cluster is disabled");
    gettext("please add vsm first");
    gettext("cluster has been referenced by service, delete the service first");
    gettext("vsm is referenced by cluster, unable to operate");
    gettext("administrator needs to add a callback address");
    gettext("image upload address error");
    gettext("vsm is not ready, image cannot be exported");
    gettext("vsm resources are insufficient to support this operation");
    gettext("vsm synchronization failed");
    gettext("vsm with the same name exists");
    gettext("read data failure");
    gettext("insufficient disk resources");
    gettext("ip addresses of the vsm to be added to the cluster must be of the same type");
    gettext("vsm ip not within address_range,please change ip first");
    gettext("recv error message follow");
    gettext("image type unauthorized");
    gettext("vsm type unauthorized");
    gettext("vsm not exist");
    gettext("tenant not exist");
    gettext("interface not exist");
    gettext("interface already exists");
    gettext("ip already exists");
    gettext("ip already used");
    gettext("unknown error");
}

int ip_status_check(char* ip) {
    unsigned int ret       = 0;
    char         status[8] = {0};

    ret = cps_get_data_from_db(CPS_IP_INFO_TABLE, "ip", ip, "use_status", status, sizeof(status), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");

    if (atoi(status) == 1)
        return -1;
    else if (atoi(status) == 0)
        return 0;
out:
    return ret;
}

void convert_to_string(int num, char* str) {
    int i;
    int digits[4];
    int val = num;

    for (i = 3; i >= 0; i--) {
        digits[i] = val % 10;
        val /= 10;
    }
    sprintf(str, "v%d.%d.%d.%d", digits[0], digits[1], digits[2], digits[3]);
}

int* parse_string_to_array(char* string, int* length) {
    int*  val       = NULL;
    int   arrLength = 0;
    char  str[512]  = {0};
    char* p         = NULL;

    if (string == NULL || length == NULL) return NULL;

    memset(str, 0, sizeof(str));
    strncpy(str, string, sizeof(str));
    p = str;

    val = (int*)malloc(sizeof(int) * 16);
    // 使用逗号分割字符串
    char* token = strtok(str, ",");

    while (token != NULL) {
        p += strlen(token) + 1;

        // 检查是否包含连字符
        char* dash = strchr(token, '-');
        if (dash != NULL) {
            // 提取连字符前后的数字
            int start = atoi(strtok(token, "-"));
            int end   = atoi(strtok(NULL, "-"));

            // 将范围内的数字添加到数组中
            for (int i = start; i <= end; i++) {
                val[arrLength] = i;
                arrLength++;

                if ((arrLength % 16) == 0) {
                    val = realloc(val, sizeof(int) * (arrLength + 16));
                }
            }
        } else {
            // 将单个数字添加到数组中
            val[arrLength] = atoi(token);
            arrLength++;
            if ((arrLength % 16) == 0) {
                val = realloc(val, sizeof(int) * (arrLength + 16));
            }
        }
        // 获取下一个逗号分割的部分
        token = strtok(p, ",");
    }

    // 将数组长度写入到指定参数中
    *length = arrLength;

    return val;
}

int callback_db(void* ptr, int count) {
    DEBUG_CPS_CLI(COMMON_DEBUG, "count %d\r\n", count);

    if (ptr != NULL)
        DEBUG_CPS_CLI(COMMON_DEBUG, "ptr %s\r\n", (char *)ptr);
    usleep(100000);

    if (count >= 400)
        return 0;
    return 1;
}

int callback_status_check(char* data, char* requestId) {
    unsigned int ret        = ERR_NONE;
    cJSON*       root       = NULL;
    cJSON*       param      = NULL;
    cJSON*       request_id = NULL;
    cJSON*       error_msg = NULL;

    if (data == NULL || requestId == NULL) return ERR_PARAM;

    root = cJSON_Parse(data);
    if (!root) return ERR_PARAM;
    DEBUG_CPS_CLI(COMMON_DEBUG, "resp data:%s\r\n", data);

    param      = cJSON_GetObjectItem(root, "status");
    request_id = cJSON_GetObjectItem(root, "requestId");
    error_msg = cJSON_GetObjectItem(root, "message");
    
    if (param == NULL || request_id == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "recv data err!\n");
        ret = ERR_PARAM;
        goto out;
    }
    if (param->valueint != 200) {
        ret = param->valueint;
        if (error_msg && error_msg->valuestring) {
            printf(gettext("error message: %s"), error_msg->valuestring);
        }
        goto out;
    }

    if (strcmp(requestId, request_id->valuestring) != 0) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "request id:[%s] [%s]\r\n", requestId, request_id->valuestring);
        ret = ERR_REQUEST_ID;
        goto out;
    }

out:
    if (root) cJSON_Delete(root);
    return ret;
}

void cps_url_comp(char* ip, int port, char* sub, char* url, int url_len) {
    struct in_addr addr4;

    if (ip == NULL || url == NULL) return;

    if (inet_pton(AF_INET, ip, &addr4) == 1) {
        if (sub == NULL) {
            snprintf(url, url_len, "http://%s:%d", ip, port);
        } else {
            snprintf(url, url_len, "http://%s:%d/%s", ip, port, sub);
        }
    } else {
        if (sub == NULL) {
            snprintf(url, url_len, "http://[%s]:%d", ip, port);
        } else {
            snprintf(url, url_len, "http://[%s]:%d/%s", ip, port, sub);
        }
    }

    return;
}

int cps_generate_random(char* data, unsigned int data_len) {
    int  i         = 0;
    int  cp_len    = 0;
    int  rand_len  = 8;
    int  rand_type = 0;
    char random[rand_len + 1];

    if (data == NULL) return ERR_PARAM;
    struct timeval val;
    gettimeofday(&val, NULL);

    srand(val.tv_usec);

    for (i = 0; i < rand_len; i++) {
        rand_type = rand() % 2;
        if (rand_type == 0) {
            random[i] = '0' + (rand() % 10);
        } else {
            random[i] = 'a' + (rand() % 26);
        }
    }
    random[rand_len] = '\0';

    cp_len = (data_len > rand_len) ? rand_len : data_len;
    memcpy(data, random, cp_len);

    return ERR_NONE;
}

int cps_get_time_now(char* time_data, unsigned int data_len) {
    char YMD[64] = {0};
    // char HMS[16] = {0};
    time_t     cur_time;
    struct tm* now_time;

    if (time_data == NULL) return ERR_PARAM;

    time(&cur_time);
    now_time = localtime(&cur_time);

    strftime(YMD, sizeof(YMD), "%Y-%m-%d %H:%M:%S", now_time);
    // strftime(YMD, sizeof(YMD),"%T", now_time);
    if (data_len > strlen(YMD)) {
        memcpy(time_data, YMD, strlen(YMD));
    }

    return ERR_NONE;
}

int cps_get_image_addr(char* addr_data, unsigned int data_len) {
    unsigned int ret  = ERR_NONE;
    int          nrow = 0, ncol = 0;
    char**       result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};
    char         tmp_buf[128]     = {0};

    if (!addr_data) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select upload_url from %s", CPS_IMG_ADDR_TABLE);
    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    if ((nrow <= 0) || (strlen(result[1]) <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "get iamge upload url failed\n");
        ret = ERR_IMAGE_ADDR;
        goto out;
    }

    if (data_len > strlen(result[1])) {
        memcpy(addr_data, result[1], strlen(result[1]));
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_get_callback_addr(char* addr_data, unsigned int data_len) {
    unsigned int ret  = ERR_NONE;
    int          nrow = 0, ncol = 0;
    char**       result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};
    char         tmp_buf[128]     = {0};

    if (!addr_data) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select addr from %s", "callback_addr");
    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    if ((nrow <= 0) || (strlen(result[1]) <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "get callback url failed\n");
        ret = ERR_CALLBACK_ADDR;
        goto out;
    }

    if (data_len > strlen(result[1])) {
        memcpy(addr_data, result[1], strlen(result[1]));
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int open_cps_db()
{
    unsigned int ret = ERR_NONE;
    char *err_msg = NULL;

    if (cps_db)
        return ret;

    if (sqlite3_open_v2(CPS_DB_PATH, &cps_db, SQLITE_OPEN_FULLMUTEX | SQLITE_OPEN_READWRITE | SQLITE_OPEN_CREATE, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "db:%s open failed!", CPS_DB_PATH);
        return ERR_SQL_OPT;
    }

    ret = sqlite3_exec(cps_db, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg);
    if (ret) {
        usleep(500000);
        if (ret = sqlite3_exec(cps_db, "PRAGMA synchronous=OFF;PRAGMA Journal_Mode=WAL;PRAGMA cache_size=2000;", 0, 0, &err_msg)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "synchronous set failed!(%s)", err_msg);
            sqlite3_free(err_msg);
        }
    }

    return ret;
}

void close_cps_db()
{
    if (cps_db) {
        sqlite3_close(cps_db);
        cps_db = NULL;
    }
}

int cps_get_data_from_db(
    char*        table_name,
    char*        match_index,
    void*        match_data,
    char*        ind_data,
    char*        targ_data,
    unsigned int data_len,
    int          dig_flag) {
    unsigned int ret  = ERR_NONE;
    int          nrow = 0, ncol = 0;
    char**       result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};

    if (table_name == NULL || ind_data == NULL || targ_data == NULL) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));

    if (dig_flag == 1)
        sprintf(sql, "select %s from %s where %s = %d", ind_data, table_name, match_index, *(int*)match_data);
    else
        sprintf(sql, "select %s from %s where %s = \'%s\'", ind_data, table_name, match_index, (char*)match_data);

    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    if (nrow > 0) {
        if (data_len > strlen(result[1])) {
            memcpy(targ_data, result[1], strlen(result[1]));
        }
    }
out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int cps_del_data_from_db(char* table_name, char* format, ...) {
    unsigned int ret              = ERR_NONE;
    char*        result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};

    if (table_name == NULL || format == NULL) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX*10);
    sprintf(sql, "delete  from %s where ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec_printf(cps_db, sql, 0, 0, &result);
    if ((ret != SQLITE_OK)) {
        if (result) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed. result(%s)\n", sql, result);
            sqlite3_free(result);
        }
        ret = ERR_SQL_OPT;
        goto out;
    }

out:
    //close_cps_db();
    return ret;
}

int cps_insert_data_to_db(char* table_name, char* format, ...) {
    unsigned int ret              = ERR_NONE;
    char*        result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};

    if (table_name == NULL || format == NULL) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX*10);
    sprintf(sql, "insert into %s ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec_printf(cps_db, sql, 0, 0, &result);
    if ((ret != SQLITE_OK)) {
        if (result) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed. result(%s)\n", sql, result);
            sqlite3_free(result);
        }
        ret = ERR_SQL_OPT;
        goto out;
    }

out:
    //close_cps_db();
    return ret;
}

int cps_update_data_to_db(char* table_name, char* format, ...) {
    unsigned int ret = ERR_NONE;
    unsigned int i = 0;
    char *result = NULL;
    char sql[SQL_LEN_MAX*10] = {0};

    if (table_name == NULL || format == NULL) return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //ret = sqlite3_exec_printf(cps_db, "BEGIN", 0, 0, &result);
    //DEBUG_CPS_CLI(COMMON_DEBUG, "ret:%d", ret);

    memset(sql, 0, sizeof(sql));
    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX*10);
    sprintf(sql, "update %s set ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_exec_printf(cps_db, sql, 0, 0, &result);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed. result(%s)\n", sql, result);
        sqlite3_free(result);
        for (i = 0; i < 5; i++) {
            usleep(500000);
            ret = sqlite3_exec_printf(cps_db, sql, 0, 0, NULL);
            if (ret == SQLITE_OK) {
                break ;
            }
        }
    }

    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed.\n", sql);
    }
out:
    //ret = sqlite3_exec_printf(cps_db, "COMMIT", 0, 0, &result);
    //DEBUG_CPS_CLI(COMMON_DEBUG, "ret:%d", ret);

    //close_cps_db();
    return ret;
}

/**
 * @brief 查询符合条件的数据数量
 *
 * @param table_name 表名
 * @param format 条件格式,同sql格式.例: "name='%s' and type='%d'"
 * @param ...
 * @return int 返回数量
 */
int cps_get_num_from_db(char* table_name, char* format, ...) {
    unsigned int ret  = 0;
    int          nrow = 0, ncol = 0;
    char**       result           = NULL;
    char         sql[SQL_LEN_MAX*10] = {0};

    if (table_name == NULL) return ret;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    va_list args;
    va_start(args, format);
    memset(sql, '\0', SQL_LEN_MAX*10);
    sprintf(sql, "select count(*) from %s where ", table_name);
    vsprintf(sql + strlen(sql), format, args);
    va_end(args);

    sqlite3_busy_handler(cps_db, callback_db, sql);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = 0;
        goto out;
    }

    ret = atoi(result[ncol]);

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

void printHex(unsigned char* pdata, unsigned int length) {
    for (int i = 0; i < length; i++) {
        printf("%02X ", *(pdata + i));
    }
    printf("\n");
}

/**
 * @brief 获取随机数(伪随机:当前时间 + 进程ID + 栈地址随机)
 *
 * @param random 随机数
 * @param random_len 随机数长度
 * @return int 0成功
 */
int cps_get_random_soft(char* random, int random_len){
    if(random == NULL){
        return -1;
    }

    unsigned int seed = 0;
    seed ^= (unsigned int)time(NULL);
    seed ^= (unsigned int)getpid() << 16;
    // seed ^= (unsigned int)(&seed)  >> 4;
    srand(seed);
    for (int i = 0; i < random_len; i ++) {
        random[i] = rand() % 256;
    }

out:
   return 0;
}