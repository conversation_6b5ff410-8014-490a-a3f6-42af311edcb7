#include "cps_cluster_manage.h"
#include "cps_common.h"
#include "cps_device.h"
#include "cps_device_monitor.h"
#include "cps_image_manage.h"
#include "cps_rights.h"
#include "cps_srv_manage.h"
#include "cps_strategy.h"
#include "cps_tenant.h"
#include "cps_vsm_manage.h"
#include "cps_work_order.h"

extern int _Close_DbFile(void);
CF_CLI(init, "cps init", "cps is module name", "init is commmand") {
    return cps_init(argc, argv);
}

CF_CLI(
    version_show,
    "cps version show",
    "cps is module name",
    "version is commmand",
    "show is commmand") {

    printf("version:%s\r\n", PLAT_VERSION);
    return 0;
}

CF_CLI(
    get_device_status,
    "cps get device_status",
    "cps is module name",
    "get is commmand",
    "device_status is commmand") {
    return cps_get_status(argc, argv);
}

CF_CLI(clean, "cps clean", "cps is module name", "clean is commmand", "device is commmand ") {
    return cps_clean(argc, argv);
}

CF_CLI(self_check, "cps self_check", "cps is module name", "self_check is commmand") {
    return cps_self_check(argc, argv);
}

CF_CLI(
    rights_list_ukey,
    "cps rights list ukey",
    "cps is module name",
    "rights is commmand",
    "list is commmand",
    "ukey") {
    return cps_rights_list_ukey(argc, argv);
}

CF_CLI(
    rights_check_user,
    "cps rights check_user type {0|1|2|3} sn <strings> name <strings> ",
    "cps is module name",
    "rights is commmand",
    "check_user is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter") {
    return cps_rights_check_user(argc, argv);
}

CF_CLI(
    rights_add,
    "cps rights add type {0|1|2|3} sn <strings> name <strings> password <strings> login_type {0|1} pin <strings> cover{0|1} ip <ip> sessionid <strings> "
    "[remark <strings>] [pri_temp <strings>]",
    "cps is module name",
    "rights is commmand",
    "add is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter",
    "password",
    "<strings> is parameter",
    "login_type",
    "0 is parameter",
    "1 is parameter",
    "pin",
    "strings is parameter",
    "cover",
    "0 is parameter",
    "1 is parameter",
    "ip is IPV4",
    "<ip> is parameter",
    "sessionid",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter",
    "pri_temp",
    "<strings> is parameter") {
    return cps_rights_add_user(argc, argv);
}

CF_CLI(
    rights_bind,
    "cps rights bind type {3} sn <strings> name <strings> pin <strings> cover{0|1} ip <ip> sessionid <strings> "
    "[remark <strings>] tenant_name <strings>",
    "cps is module name",
    "rights is commmand",
    "add is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter",
    "pin",
    "strings is parameter",
    "cover",
    "0 is parameter",
    "1 is parameter",
    "ip is IPV4",
    "<ip> is parameter",
    "sessionid",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter") {
    return cps_rights_add_user_bind_ukey(argc, argv);
}

CF_CLI(
    rights_remote_add,
    "cps rights remote_add type {0|1|2|3} sn <strings> name <strings> password <strings> login_type {0|1} key <strings> ip <ip> sessionid <strings> "
    "[remark <strings>] [pri_temp <strings>]",
    "cps is module name",
    "rights is commmand",
    "remote_add is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter",
    "password",
    "<strings> is parameter",
    "login_type",
    "0 is parameter",
    "1 is parameter",
    "key",
    "strings is parameter",
    "ip is IPV4",
    "<ip> is parameter",
    "sessionid",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter",
    "pri_temp",
    "<strings> is parameter") {
    return cps_rights_remote_add_user(argc, argv);
}

CF_CLI(
    rights_remote_bind,
    "cps rights remote_bind type {3} sn <strings> name <strings> key <strings> ip <ip> sessionid <strings> "
    "[remark "
    "<strings>] tenant_name <strings>",
    "cps is module name",
    "rights is commmand",
    "remote_add is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter",
    "key",
    "strings is parameter",
    "ip is IPV4",
    "<ip> is parameter",
    "sessionid",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter") {
    return cps_rights_remote_add_user_bind_ukey(argc, argv);
}

CF_CLI(
    rights_del,
    "cps rights del type {0|1|2|3} name <strings> [tenant_id <strings>]",
    "cps is module name",
    "rights is commmand",
    "del is commmand",
    "type",
    "0 is parameter",
    "1 is parameter",
    "2 is parameter",
    "3 is parameter",
    "name",
    "<strings> is parameter",
    "tenant_id",
    "<strings> is parameter") {
    return cps_rights_del_user(argc, argv);
}

CF_CLI(
    rights_login,
    "cps rights login sn <strings> name <strings> pin <strings> ip <ip> sessionid <strings>",
    "cps is module name",
    "rights is commmand",
    "login is commmand",
    "sn",
    "strings is parameter",
    "name",
    "<strings> is parameter",
    "pin",
    "strings is parameter",
    "ip is IPV4",
    "sessionid",
    "<strings> is parameter",
    "<strings> is parameter") {
    return cps_rights_login(argc, argv);
}

CF_CLI(
    rights_remote_login,
    "cps rights remote_login name <strings> sign_data <strings> hash <strings> ip <ip> sessionid <strings>",
    "cps is module name",
    "rights is commmand",
    "remote_login is commmand",
    "name",
    "strings is parameter",
    "sign_data",
    "strings is parameter",
    "hash",
    "strings is parameter",
    "ip is IPV4",
    "ip is parameter",
    "sessionid",
    "<strings> is parameter") {
    return cps_rights_remote_login(argc, argv);
}

CF_CLI(
    rights_remote_get_login_random,
    "cps rights get login random",
    "cps is module name",
    "rights is commmand",
    "get is commmand",
    "login is commmand",
    "random is commmand") {
    return cps_rights_get_login_random(argc, argv);
}

CF_CLI(
    rights_logout,
    "cps rights logout name <strings> [tenant_id <strings>]",
    "cps is module name",
    "rights is commmand",
    "logout is commmand",
    "name",
    "strings is parameter",
    "tenant_id",
    "<strings> is parameter") {
    return cps_rights_logout(argc, argv);
}

CF_CLI(
    rights_logout_ip,
    "cps rights logout ip <strings> [ sessionid <strings> ] [tenant_id <strings>]",
    "cps is module name",
    "rights is commmand",
    "logout is commmand",
    "ip is IPV4 ",
    "strings is parameter",
    "sessionid",
    "<strings> is parameter",
    "tenant_id",
    "<strings> is parameter") {
    return cps_rights_logout_by_ip(argc, argv);
}

CF_CLI(
    rights_get_status,
    "cps rights get_status [tenant_id <strings>]",
    "cps is module name",
    "rights is commmand",
    "get_status is commmand",
    "tenant_id",
    "<strings> is parameter") {
    return cps_rights_get_status(argc, argv);
}

CF_CLI(
    rights_set_pin,
    "cps rights set_pin sn <strings> old_pin <strings> new_pin <strings>",
    "cps is module name",
    "rights is commmand",
    "set_pin is commmand",
    "sn",
    "strings is parameter",
    "old_pin",
    "strings is parameter",
    "new_pin",
    "strings is parameter") {
    return cps_rights_set_pin(argc, argv);
}

CF_CLI(
    rights_check_pr,
    "cps rights check_privileage ip <strings> sessionid <strings> route_str <strings> [is_tenant {0|1}]",
    "cps is module name",
    "rights is commmand",
    "check_privileage is commmand",
    "ip",
    "strings is parameter",
    "sessionid",
    "<strings> is parameter",
    "route_str",
    "strings is parameter",
    "is_tenant",
    "0 is parameter",
    "1 is parameter") {
    return cps_rights_check_privileage(argc, argv);
}

CF_CLI(
    rights_set,
    "cps rights set name <strings> login_type {0|1} [pri_temp <strings>] [tenant_id <strings>] [remark <strings>]",
    "cps is module name",
    "rights is commmand",
    "check_privileage is commmand",
    "name",
    "strings is parameter",
     "login_type",
    "0 is parameter",
    "1 is parameter",
    "pri_temp",
    "strings is parameter",
    "tenant_id",
    "<strings> is parameter",
    "remark",
    "strings is parameter") {
    return cps_rights_set_user_info(argc, argv);
}

CF_CLI(
    device_add,
    "cps device add device_name <strings> device_type {0} ip <ip> [remark <strings>]",
    "cps is module name",
    "device is commmand",
    "add is commmand",
    "device_name",
    "strings is parameter",
    "device_type",
    "0 is parameter",
    "ip",
    "ip is parameter",
    "remark",
    "<strings> is parameter") {
    return cps_device_add(argc, argv);
}

CF_CLI(
    device_del,
    "cps device del device_name <strings>",
    "cps is module name",
    "device is commmand",
    "del is commmand",
    "device_name",
    "<strings> is parameter") {
    return cps_device_del(argc, argv);
}

CF_CLI(
    tenant_add,
    "cps tenant add name <strings> account <strings> password <strings> valid_time <strings> corporate_name <strings> "
    "[telephone <strings>] "
    "phone_number <strings> mail <strings> [remark <strings>]",
    "cps is module name",
    "tenant is commmand",
    "add is commmand",
    "name",
    "strings is parameter",
    "account",
    "strings is parameter",
    "password",
    "strings is parameter",
    "valid_time",
    "strings is parameter",
    "corporate_name",
    "strings is parameter",
    "telephone",
    "strings is parameter",
    "phone_number",
    "strings is parameter",
    "mail",
    "strings is parameter",
    "remark",
    "strings is parameter") {
    return cps_tenant_add(argc, argv);
}

CF_CLI(
    tenant_set,
    "cps tenant set name <strings> [corporate_name <strings>] [telephone <strings>] [phone_number <strings>] [mail "
    "<strings>] [remark <strings>][valid_time <strings>]",
    "cps is module name",
    "tenant is commmand",
    "set is commmand",
    "name",
    "<strings> is parameter",
    "corporate_name",
    "<strings> is parameter",
    "telephone",
    "<strings> is parameter",
    "phone_number",
    "<strings> is parameter",
    "mail",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter",
    "valid_time",
    "strings is parameter") {
    return cps_tenant_set(argc, argv);
}

CF_CLI(
    tenant_set_paswword,
    "cps tenant reset_password account <strings>",
    "cps is module name",
    "tenant is commmand",
    "set_paswword is commmand",
    "account",
    "<strings> is parameter") {
    return cps_tenant_set_paswword(argc, argv);
}

CF_CLI(
    tenant_del,
    "cps tenant del tenant_id <strings> [force {1}]",
    "cps is module name",
    "tenant is commmand",
    "del is commmand",
    "tenant_id",
    "<strings> is parameter",
    "force",
    "1 is parameter") {
    return cps_tenant_del(argc, argv);
}

CF_CLI(
    tenant_set_bindkey,
    "cps tenant reset_bindkey tenant_name <strings>",
    "cps is module name",
    "tenant is commmand",
    "reset_bindkey is commmand",
    "tenant_name",
    "<strings> is parameter") {
    return cps_tenant_reset_bind_key(argc, argv);
}

CF_CLI(
    tenant_update_status,
    "cps tenant update status",
    "cps is module name",
    "tenant is commmand",
    "update is commmand",
    "status is commmand") {
    return cps_tenant_on_off(argc, argv);
}

CF_CLI(
    work_order_check_vsm,
    "cps work_order check_vsm host_name <strings> vsm_cpu <number> vsm_mem <number>",
    "cps is module name",
    "work_order is commmand",
    "check_vsm is commmand",
    "host_name",
    "strings is parameter",
    "vsm_cpu",
    "number is parameter",
    "vsm_mem",
    "number is parameter") {
    return cps_work_order_check_vsm(argc, argv);
}

CF_CLI(
    work_order_approval,
    "cps work_order approval wo_index <strings> [vsm_id <strings>] [vsm_num <number>] opinion {0|1} sessionid <strings> "
    "[annotations <strings>]",
    "cps is module name",
    "work_order is commmand",
    "approval is commmand",
    "wo_index",
    "strings is parameter",
    "vsm_name",
    "strings is parameter",
    "vsm_num is command",
    "number is parameter",
    "opinion",
    "0 is parameter",
    "1 is parameter",
    "sessionid",
    "strings is parameter",
    "annotations",
    "strings is parameter") {
    return cps_work_order_approval(argc, argv);
}

CF_CLI(
    work_order_create,
    "cps work_order create name <strings> type {0 | 1} [vsm_num <number> vsm_type <number> apply_spec <number> cpu_model {0 | 1}] [vsm_id <strings> dila_spec <number>] session_id <strings> [remark <strings>]",
    "cps is module name",
    "work_order is commmand",
    "create is commmand",
    "name id parameter",
    "strings is parameter",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_work_order_create(argc, argv);
}

CF_CLI(
    addr_add,
    "cps addr add addr_name <strings> tenant_name <strings> addr_info <strings> "
    "[remark <strings>]",
    "cps is module name",
    "addr is commmand",
    "add is commmand",
    "addr_name",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter",
    "addr_info",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter") {
    return cps_addr_add(argc, argv);
}
CF_CLI(
    addr_addex,
    "cps addr addex addr_name <strings> ip_range <strings> subnet <strings> gateway <strings> tenant_name <strings> "
    "[remark <strings>]",
    "cps is module name",
    "addr is commmand",
    "add is commmand",
    "addr_name",
    "<strings> is parameter",
    "ip_range",
    "<strings> is parameter",
    "subnet",
    "<strings> is parameter",
    "gateway",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter") {
    return cps_addr_add(argc, argv);
}

CF_CLI(
    addr_check,
    "cps addr check ip_range <strings> subnet <strings> tenant_name <strings>",
    "cps is module name",
    "addr is commmand",
    "check is commmand",
    "ip_range",
    "<strings> is parameter",
    "subnet",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter") {
    return cps_addr_check(argc, argv);
}

CF_CLI(
    addr_set,
    "cps addr set addr_name <strings> tenant_name <strings> addr_info <strings> [remark <strings>]",
    "cps is module name",
    "addr is commmand",
    "set is commmand",
    "addr_name",
    "<strings> is parameter",
    "tenant_name",
    "<strings> is parameter",
    "addr_info",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter") {
    return cps_addr_set(argc, argv);
}

CF_CLI(
    addr_setex,
    "cps addr setex nant_name <strings> ip_range <strings> subnet <strings> gateway <strings> [remark <strings>]",
    "cps is module name",
    "addr is commmand",
    "set is commmand",
    "tenant_name",
    "<strings> is parameter",
    "ip_range",
    "<strings> is parameter",
    "subnet",
    "<strings> is parameter",
    "gateway",
    "<strings> is parameter",
    "remark",
    "<strings> is parameter") {
    return cps_addr_set(argc, argv);
}

CF_CLI(
    addr_del,
    "cps addr del tenant_name <strings>",
    "cps is module name",
    "addr is commmand",
    "del is commmand",
    "tenant_name",
    "<strings> is parameter") {
    return cps_addr_del(argc, argv);
}

CF_CLI(
    cb_addr_set,
    "cps callback_addr set addr <ip> [check_flag {1}]",
    "cps is module name",
    "callback_addr is commmand",
    "set is commmand",
    "addr",
    "<ip> is parameter",
    "check_flag",
    "1 is parameter") {
    return cps_callback_addr_set(argc, argv);
}

CF_CLI(
    img_set,
    "cps img_addr set url <ip>",
    "cps is module name",
    "img_addr is commmand",
    "set is commmand",
    "url",
    "<ip> is parameter") {
    return cps_img_addr_set(argc, argv);
}

CF_CLI(tenant_acct, "cps tenant <strings> ", "cps is module name", "host is command", "show is command", "", "") {
    return cps_host_show(argc, argv);
}

CF_CLI(
    host_show,
    "cps host show",
    "cps is module name",
    "host is command",
    "show is command") {
    return cps_host_show(argc, argv);
}

CF_CLI(
    image_show,
    "cps image show host_ip <strings>",
    "cps is module name",
    "image is command",
    "show is command",
    "",
    "") {
    return cps_image_show(argc, argv);
}

CF_CLI(
    vsm_image_upgrade,
    "cps vsm upgrade vsm_id <strings> image_sign <strings> image_ver <strings> [remark <strings>]",
    "cps is module name",
    "vsm is command",
    "upgrade is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_vsm_image_upgrade(argc, argv);
}

CF_CLI(
    vsm_pkg_upgrade,
    "cps vsm upgrade vsm_id <strings> pkg_name <strings> [remark <strings>]",
    "cps is module name",
    "vsm is command",
    "upgrade is command",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_vsm_pkg_upgrade(argc, argv);
}
CF_CLI(
    vsm_create,
    "cps vsm create image_dig <strings> image_name <strings> image_ver <strings> host_name "
    "<strings> host_ip <strings> vsm_name <strings> vsm_type <number> create_num <number> cpu_model <number> spec <number> [interface <string>] [remark <strings>]",
    "cps is module name",
    "vsm is command",
    "create is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_vsm_create(argc, argv);
}

//添加网卡配置
CF_CLI(
    vsm_net_config_add,
    "cps vsm netconf add vsm_id <strings> interface <string> [tenant_name <strings>] [remark <strings>]",
    "cps is module name",
    "vsm is command",
    "netconf is command",
    "add is command",
    "vsm_id is vsm id",
    "strings is values",
    "interface is interface json string",
    "string is values",
    "tenant_name is tenant name",
    "strings is values",
    "remark is remark",
    "strings is values"
    ) {
    return cps_vsm_net_config(argc, argv, 1);
}

//删除网卡配置
CF_CLI(
    vsm_net_config_del,
    "cps vsm netconf del vsm_id <strings> interface <string> [tenant_name <strings>]",
    "cps is module name",
    "vsm is command",
    "netconf is command",
    "del is command",
    "vsm_id is vsm id",
    "strings is values",    
    "interface is interface json string",
    "string is values",
    "tenant_name is tenant name",
    "strings is values"
    ) {
    return cps_vsm_net_config(argc, argv, 2);
}

//修改网卡配置
CF_CLI(
    vsm_net_config_set,
    "cps vsm netconf set vsm_id <strings> interface <string> [tenant_name <strings>] [remark <strings>]",
    "cps is module name",
    "vsm is command",
    "netconf is command",
    "set is command",
    "vsm_id is vsm id",
    "strings is values",
    "interface is interface json string",
    "string is values",
    "tenant_name is tenant name",
    "strings is values",
    "remark is remark",
    "strings is values"
    ) {
    return cps_vsm_net_config(argc, argv, 3);
}

//添加路由 
//cps vsm netconf  route_add vsm_id <strings> interface <strings> 
CF_CLI(
    vsm_route_add,
    "cps vsm netconf route_add vsm_id <strings> interface <string>",
    "cps is module name",
    "vsm is command",
    "netconf is command",
    "route_add is command",
    "vsm_id is vsm id",
    "strings is values",
    "interface is interface json string",
    "string is values"
    ) {
    return cps_vsm_route_config(argc, argv, 1);
}
//删除路由 
//cps vsm netconf  route_del vsm_id <strings> interface <strings> 
CF_CLI(
    vsm_route_del,
    "cps vsm netconf route_del vsm_id <strings> interface <string>",
    "cps is module name",
    "vsm is command",
    "netconf is command",
    "route_del is command",
    "vsm_id is vsm id",
    "strings is values",
    "interface is interface json string",
    "string is values"
    ) {
    return cps_vsm_route_config(argc, argv, 2);
}




CF_CLI(
    vsm_remark_set,
    "cps vsm remark set [tenant_name <strings>] vsm_id <strings> remark <strings>",
    "cps is module name",
    "remark is module name",
    "vsm is command",
    "set is command",
    "tenant_name is tenant name",
    "strings is values",
    "vsm_id is vsm id",
    "strings is values",
    "",
    "") {
    return cps_vsm_remark_set(argc, argv);
}

CF_CLI(
    vsm_info_get,
    "cps vsm info get tenant_name <strings> vsm_id <strings>",
    "cps is module name",
    "vsm is command",
    "info is command",
    "get is command",
    "tenant_name is tenant name",
    "strings is values",
    "vsm_id is vsm id",
    "strings is values") {
    return cps_vsm_info_get(argc, argv);
}

CF_CLI(
    vsm_spec_set,
    "cps vsm spec set [tenant_name <strings>] vsm_id <strings> spec <number>",
    "cps is module name",
    "vsm is command",
    "spec is command",
    "set is command",
    "tenant_name is tenant name",
    "strings is values",
    "vsm_id is vsm id",
    "strings is values",
    "spec is flavor",
    "number is flavor value") {
    return cps_vsm_spec_set(argc, argv);
}

CF_CLI(
    vsm_opt,
    "cps vsm opt {restart | start | stop | reset | destroy | init} [tenant_name <strings>] vsm_id <strings>",
    "cps is module name",
    "vsm is command",
    "opt is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_vsm_opt(argc, argv);
}

CF_CLI (
    vsm_drift_set,
    "cps vsm drift set tenant_name <strings> vsm_id <strings> <number>",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_set_vsm_drift(argc, argv);
}

CF_CLI(
    asymm_proc,
    "cps_callback main_type <number> sub_type <number> state {0 | 1} request_id <strings> msg <strings>",
    "cps_callback is module name",
    "main_type is command name",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_asymm_proc(argc, argv);
}

CF_CLI(
    vsm_image_export,
    "cps vsm export image tenant_name <strings> vsm_id <strings>",
    "cps is module name",
    "vsm is command",
    "export is command",
    "",
    "",
    "",
    "",
    "") {
    return cps_image_export(argc, argv);
}

CF_CLI(
    vsm_image_import,
    "cps image import tenant_name <strings> image_name <strings> id <strings>",
    "cps is module name",
    "image is command",
    "import is command",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_image_import(argc, argv);
}

CF_CLI(
    vsm_image_del,
    "cps image del tenant_name <strings> image_name <strings>",
    "cps is module name",
    "image is command",
    "del is command",
    "",
    "",
    "",
    "") {
    return cps_image_del(argc, argv);
}

CF_CLI(
    cluster_create,
    "cps cluster create tenant_name <strings> name <strings> cluster_type {1 | 2} vsm_max <number> [remark <strings>]",
    "cps is module name",
    "cluster is command",
    "create is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_cluster_create(argc, argv);
}

CF_CLI(
    cluster_vsm_opt,
    "cps_cluster vsm set tenant_name <strings> cluster_id <strings> id <strings> [master_id <strings>] [main_id <strings>]",
    "cps_cluster is module name",
    "vsm is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    ""
    ) {
    return cps_cluster_vsm_opt(argc, argv);
}

CF_CLI(
    cluster_edit,
    "cps cluster set tenant_name <strings> cluster_id <strings> name <strings> vsm_max <number> [remark <strings>]",
    "cps is module name",
    "cluster is command",
    "set is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_cluster_edit(argc, argv);
}

CF_CLI(
    cluster_opt,
    "cps cluster opt {start | stop | delete} tenant_name <strings> cluster_id <strings> [enforce]",
    "cps is module name",
    "cluster is command",
    "opt is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_cluster_opt(argc, argv);
}


CF_CLI(
    cluster_syn_conf,
    "cps cluster syn tenant_name <strings> syn_id <strings> temp_id <strings>",
    "cps is module name",
    "cluster is command",
    "syn is command",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_cluster_syn(argc, argv);
}


CF_CLI(
    service_add,
    "cps service add tenant_name <strings> srv_name <strings> cluster_id <strings> interface <strings> srv_port "
    "<number> srv_http_port <number> load_mode {0 | 1 | 2} weight_policy {0 | 1} [remark <strings>]",
    "cps is module name",
    "service is command",
    "add is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_srv_add(argc, argv);
}

CF_CLI(
    service_edit,
    "cps service edit tenant_name <strings> srv_id <strings> interface <strings> srv_port <number> srv_http_port <number> load_mode {0 | 1 | "
    "2} weight_policy {0 | 1} [remark <strings>]",
    "cps is module name",
    "service is command",
    "edit is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_srv_edit(argc, argv);
}

CF_CLI(
    service_opt,
    "cps service opt {start_up | stop | del} tenant_name <strings> srv_id <strings>",
    "cps is module name",
    "service is command",
    "opt is command",
    "",
    "",
    "",
    "",
    "",
    "",
    "") {
    return cps_srv_conf(argc, argv);
}

CF_CLI(get_sign_pub, "cps get sign_pub", "cps is module name", "get is command", "sign_pub is command") {
    return cps_get_sign_pub_key(argc, argv);
}

CF_CLI(get_sign_pri, "cps get sign_pri", "cps is module name", "get is command", "sign_pri is command") {
    return cps_get_sign_pri_key(argc, argv);
}

CF_CLI(img_drift_option, "cps image drift {on|off}", "cps is module name", "image is command", "","","") {
    return cps_image_drift_option(argc, argv);
}

CF_CLI(manage_service_option, "cps manage vsm_id <strings> srv_type <number> status {on|off} [config <strings>]", 
    "cps is module name", 
    "manage is command", 
    "vsm is command", 
    "strings is parameter", 
    "srv_type is command", 
    "number is type(1:SSH,2:WEBCA,3:SNMP)", 
    "status is command", 
    "on is parameter", 
    "off is parameter",
    "config is parameter",
    "strings is json data"
    ) {
    return cps_vsm_manage_service_option(argc, argv);
}

CF_CLI(get_vsm_manage_service_status, "cps manage get vsm_id <strings>", 
    "cps is module name", 
    "manage is command", 
    "get is command", 
    "vsm_id is vsm id", 
    "strings is parameter"
    ) {
    return cps_get_vsm_manage_service_status(argc, argv);
}

CF_CLI_ARRAY(
    cps_clis,
    &init,
    &version_show,
    &get_device_status,
    &clean,
    &self_check,
    &rights_list_ukey,
    &rights_check_user,
    &rights_add,
    &rights_bind,
    &rights_remote_add,
    &rights_remote_bind,
    &rights_del,
    &rights_login,
    &rights_remote_login,
    &rights_logout,
    &rights_check_pr,
    &rights_get_status,
    &rights_set_pin,
    &rights_set,
    &rights_logout_ip,
    &device_add,
    &device_del,
    &tenant_add,
    &tenant_set,
    &tenant_update_status,
    &tenant_set_paswword,
    &tenant_set_bindkey,
    &tenant_del,
    &work_order_check_vsm,
    &work_order_approval,
    &work_order_create,
    &addr_set,
    &addr_add,
    &addr_del,
    &addr_check,
    &img_set,
    &cb_addr_set,
    &host_show,
    &image_show,
    &vsm_image_upgrade,
    &vsm_pkg_upgrade,
    &vsm_create,
    &vsm_net_config_add,
    &vsm_net_config_del,
    &vsm_net_config_set,
    &vsm_route_add,
    &vsm_route_del,
    &vsm_remark_set,
    &vsm_info_get,
    &vsm_spec_set,
    &vsm_opt,
    &vsm_drift_set,
    &asymm_proc,
    &vsm_image_export,
    &vsm_image_import,
    &vsm_image_del,
    &cluster_create,
    &cluster_vsm_opt,
    &cluster_opt,
    &cluster_syn_conf,
    &cluster_edit,
    &service_add,
    &service_edit,
    &service_opt,
    &get_sign_pub,
    &get_sign_pri,
    &img_drift_option,
    &manage_service_option,
    &get_vsm_manage_service_status);

int main(int argc, char* argv[]) {
    int ret = OP_SUCCESS;
  
    gettext_test();
  
    if (cf_init() < 0) {
        ret = OP_ERROR;
        DEBUG_CPS_CLI(ERR_DEBUG, "cf_init failed!\n");
        goto out;
    }
  
    if (cf_locale("cps_values", LOCALEDIR) < 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cf_locale failed!\n");
    }
  
    if (strstr(argv[0], "cps_monitor") != NULL) {
        return cps_monitor();
    }
      
    if (cf_load_array(cps_clis) < 0) {
        ret = OP_ERROR;
        DEBUG_CPS_CLI(ERR_DEBUG, "cf_load failed!\n");
        goto out;
    }
        
    if ((ret = cf_process(argc, argv)) < 0) {
        ret = OP_ERROR;
        DEBUG_CPS_CLI(ERR_DEBUG, "cf_process failed!\n");
        goto out;
    }
out:
    close_cps_db();
    _Close_DbFile();
    if (strcmp(CF_ERR_STR(), "NULL") != 0) {
        printf("%s\n", CF_ERR_STR());
    }

    cf_exit();
    return ret;
}
