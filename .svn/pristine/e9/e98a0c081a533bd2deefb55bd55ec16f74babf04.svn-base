#ifndef _KEY_SUPPORT_H_
#define _KEY_SUPPORT_H_

/* ======================================================================================
 * includes
 */
# include "key_public.h"
/* ======================================================================================
 * extern
 */

/* ======================================================================================
 * macros
 */
 //数据库文件路径
#define DB_FILE_PATH "/usr/local/conf/cps.db"

/* ======================================================================================
 * types
 */

int Db_GetKeySupport( KeyCapBility *pCapbility, unsigned int count);
int Db_GetManagerSupport(KeyCapBility *pCapbility, unsigned int count);
int Db_GetDeviceSupport(KeyCapBility *pCapbility, unsigned int count);

#endif // _DATABASE_API_H