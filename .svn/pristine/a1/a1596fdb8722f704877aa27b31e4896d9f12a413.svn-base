/*
 * Project: base
 * Moudle: cps
 * File: cps_strategy.c
 * Created Date: 2023-08-29 14:52:11
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include "cjson.h"
#include "cps_common.h"
#include "cps_msg.h"
#include "cps_strategy.h"
#include "database_api.h"
#include "public/key_operate.h"

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* 地址信息结构体 */
typedef struct {
    char* ip_range;
    char* subnet;
    char* gateway;
    int bindType;
    char* interface_name;
    char* bridge_interface;
    char* vlan_id;
} addr_info_t;

/* 函数声明 */
static int parse_addr_info_json(const char* addr_info, addr_info_t** parsed_info_array, int* array_size);
static void free_addr_info_array(addr_info_t* info_array, int array_size);
static void free_addr_info(addr_info_t* info);

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/**
 * @brief 释放 addr_info_t 结构体数组中分配的内存
 * @param info_array 要释放的地址信息结构体数组指针
 * @param array_size 数组大小
 */
static void free_addr_info_array(addr_info_t* info_array, int array_size) {
    if (info_array) {
        for (int i = 0; i < array_size; i++) {
            free_addr_info(&info_array[i]);
        }
        free(info_array);
    }
}

/**
 * @brief 释放 addr_info_t 结构体中分配的内存
 * @param info 要释放的地址信息结构体指针
 */
static void free_addr_info(addr_info_t* info) {
    if (info) {
        if (info->ip_range) {
            free(info->ip_range);
            info->ip_range = NULL;
        }
        if (info->subnet) {
            free(info->subnet);
            info->subnet = NULL;
        }
        if (info->gateway) {
            free(info->gateway);
            info->gateway = NULL;
        }
        if (info->interface_name) {
            free(info->interface_name);
            info->interface_name = NULL;
        }
        if (info->bridge_interface) {
            free(info->bridge_interface);
            info->bridge_interface = NULL;
        }
        if (info->vlan_id) {
            free(info->vlan_id);
            info->vlan_id = NULL;
        }
    }
}

/**
 * @brief 解析 JSON 格式的地址信息
 * @param addr_info JSON 字符串
 * @param parsed_info_array 解析结果存储结构体数组
 * @param array_size 数组大小
 * @return 0 成功，-1 失败
 */
static int parse_addr_info_json(const char* addr_info, addr_info_t** parsed_info_array, int* array_size) {
    if (!addr_info || !parsed_info_array || !array_size) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid parameters");
        return -1;
    }

    // 解析 JSON 格式的 addr_info
    cJSON* root = cJSON_Parse(addr_info);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to parse JSON addr_info: %s", addr_info);
        printf("Invalid JSON format in addr_info!\n");
        return -1;
    }

    // 获取 addr_info 数组
    cJSON* addr_info_array = cJSON_GetObjectItem(root, "addr_info");
    if (!cJSON_IsArray(addr_info_array) || cJSON_GetArraySize(addr_info_array) == 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "addr_info array not found or empty");
        printf("addr_info array not found or empty!\n");
        cJSON_Delete(root);
        return -1;
    }

    // 获取数组大小
    *array_size = cJSON_GetArraySize(addr_info_array);
    
    // 分配内存
    *parsed_info_array = (addr_info_t*)malloc(*array_size * sizeof(addr_info_t));
    if (!*parsed_info_array) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for addr_info array");
        cJSON_Delete(root);
        return -1;
    }
    
    // 初始化数组
    memset(*parsed_info_array, 0, *array_size * sizeof(addr_info_t));

    // 遍历所有地址信息对象
    for (int i = 0; i < *array_size; i++) {
        cJSON* addr_obj = cJSON_GetArrayItem(addr_info_array, i);
        if (!cJSON_IsObject(addr_obj)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Invalid addr_info object at index %d", i);
            printf("Invalid addr_info object at index %d!\n", i);
            free_addr_info_array(*parsed_info_array, *array_size);
            cJSON_Delete(root);
            return -1;
        }

        // 解析 bindType
        cJSON* bindType_json = cJSON_GetObjectItem(addr_obj, "bindType");
        if (cJSON_IsNumber(bindType_json)) {
            (*parsed_info_array)[i].bindType = bindType_json->valueint;
        } else {
            (*parsed_info_array)[i].bindType = 0; // 默认值
        }

        // 解析 interface_name
        cJSON* interface_name_json = cJSON_GetObjectItem(addr_obj, "interface_name");
        if (cJSON_IsString(interface_name_json)) {
            char* interface_name_str = cJSON_GetStringValue(interface_name_json);
            if (interface_name_str) {
                (*parsed_info_array)[i].interface_name = malloc(strlen(interface_name_str) + 1);
                if ((*parsed_info_array)[i].interface_name) {
                    strcpy((*parsed_info_array)[i].interface_name, interface_name_str);
                }
            }
        }

        // 解析 bridge_interface
        cJSON* bridge_interface_json = cJSON_GetObjectItem(addr_obj, "bridge_interface");
        if (cJSON_IsString(bridge_interface_json)) {
            char* bridge_interface_str = cJSON_GetStringValue(bridge_interface_json);
            if (bridge_interface_str) {
                (*parsed_info_array)[i].bridge_interface = malloc(strlen(bridge_interface_str) + 1);
                if ((*parsed_info_array)[i].bridge_interface) {
                    strcpy((*parsed_info_array)[i].bridge_interface, bridge_interface_str);
                }
            }
        }

        // 解析 vlan_id_range
        cJSON* vlan_id_json = cJSON_GetObjectItem(addr_obj, "vlan_id_range");
        if (cJSON_IsNull(vlan_id_json))
        {
            (*parsed_info_array)[i].vlan_id = NULL;
        }else{
            if (cJSON_IsString(vlan_id_json)) {
                char* vlan_id_str = cJSON_GetStringValue(vlan_id_json);
                if (vlan_id_str) {
                    (*parsed_info_array)[i].vlan_id = malloc(strlen(vlan_id_str) + 1);
                    if ((*parsed_info_array)[i].vlan_id) {
                        strcpy((*parsed_info_array)[i].vlan_id, vlan_id_str);
                    }
                }
            }
        }
        // 解析 subnet 字段
        cJSON* subnet_json = cJSON_GetObjectItem(addr_obj, "subnet");
        if (cJSON_IsString(subnet_json)) {
            char* subnet_str = cJSON_GetStringValue(subnet_json);
            char subnet_buffer[512] = {0};
            char temp_subnet[512] = {0};
            strcpy(temp_subnet, subnet_str);  // 复制字符串以避免修改原始数据

            char* token = strtok(temp_subnet, ",");
            int first = 1;

            while (token != NULL) {
                // 去除前后空格
                while (*token == ' ') token++;
                char* end = token + strlen(token) - 1;
                while (end > token && *end == ' ') *end-- = '\0';

                // 提取网络地址部分（去掉子网掩码）
                char* slash_pos = strchr(token, '/');
                if (slash_pos != NULL) {
                    *slash_pos = '\0';  // 截断到斜杠位置
                }

                if (!first) {
                    strcat(subnet_buffer, ",");
                }
                strcat(subnet_buffer, token);
                first = 0;

                token = strtok(NULL, ",");
            }

            (*parsed_info_array)[i].subnet = malloc(strlen(subnet_buffer) + 1);
            if ((*parsed_info_array)[i].subnet) {
                strcpy((*parsed_info_array)[i].subnet, subnet_buffer);
            }
        }

        // 解析 ip_range 数组
        cJSON* ip_range_array = cJSON_GetObjectItem(addr_obj, "ip_range");
        if (cJSON_IsArray(ip_range_array)) {
            char ip_range_buffer[1024] = {0};
            char gateway_buffer[512] = {0};
            int first_ip = 1, first_gw = 1;

            int ip_array_size = cJSON_GetArraySize(ip_range_array);
            for (int j = 0; j < ip_array_size; j++) {
                cJSON* ip_range_item = cJSON_GetArrayItem(ip_range_array, j);
                if (cJSON_IsString(ip_range_item)) {
                    char* ip_range_str = cJSON_GetStringValue(ip_range_item);

                    // 查找 "/" 分隔符
                    char* slash_pos = strchr(ip_range_str, '/');
                    if (slash_pos != NULL) {
                        // 提取 IP 范围部分（"/" 前面）
                        int range_len = slash_pos - ip_range_str;
                        char range_part[256] = {0};
                        strncpy(range_part, ip_range_str, range_len);

                        if (!first_ip) {
                            strcat(ip_range_buffer, ",");
                        }
                        strcat(ip_range_buffer, range_part);
                        first_ip = 0;

                        // 提取网关部分（"/" 后面）
                        char* gateway_part = slash_pos + 1;
                        if (!first_gw) {
                            strcat(gateway_buffer, ",");
                        }
                        strcat(gateway_buffer, gateway_part);
                        first_gw = 0;
                    }
                }
            }

            if (strlen(ip_range_buffer) > 0) {
                (*parsed_info_array)[i].ip_range = malloc(strlen(ip_range_buffer) + 1);
                if ((*parsed_info_array)[i].ip_range) {
                    strcpy((*parsed_info_array)[i].ip_range, ip_range_buffer);
                }
            }

            if (strlen(gateway_buffer) > 0) {
                (*parsed_info_array)[i].gateway = malloc(strlen(gateway_buffer) + 1);
                if ((*parsed_info_array)[i].gateway) {
                    strcpy((*parsed_info_array)[i].gateway, gateway_buffer);
                }
            }
        }
    }

    // 清理 JSON 对象
    cJSON_Delete(root);

    // 检查是否成功解析了必要的字段
    for (int i = 0; i < *array_size; i++) {
        if (!(*parsed_info_array)[i].ip_range || !(*parsed_info_array)[i].subnet || !(*parsed_info_array)[i].gateway) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to parse required fields from JSON at index %d", i);
            printf("Failed to parse required fields from addr_info JSON at index %d!\n", i);
            free_addr_info_array(*parsed_info_array, *array_size);
            return -1;
        }

        DEBUG_CPS_CLI(COMMON_DEBUG, "Parsed[%d] - bindType: %d, interface_name: %s, bridge_interface: %s, vlan_id: %s",
                      i, (*parsed_info_array)[i].bindType,
                      (*parsed_info_array)[i].interface_name ? (*parsed_info_array)[i].interface_name : "NULL",
                      (*parsed_info_array)[i].bridge_interface ? (*parsed_info_array)[i].bridge_interface : "NULL",
                      (*parsed_info_array)[i].vlan_id ? (*parsed_info_array)[i].vlan_id : "NULL");
        DEBUG_CPS_CLI(COMMON_DEBUG, "Parsed[%d] - ip_range: %s, subnet: %s, gateway: %s",
                      i, (*parsed_info_array)[i].ip_range, (*parsed_info_array)[i].subnet, (*parsed_info_array)[i].gateway);
    }

    return 0;
}
int _set_img_url_cb(char* str, char* request_id) {
    int ret = 0;

    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }
    DEBUG_CPS_CLI(COMMON_DEBUG, "data:%s\r\n", str);

    cJSON* tmp = NULL;
    tmp        = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret = tmp->valueint;
            goto out;
        }
    } else {
        ret = -1;
        goto out;
    }

out:
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

int _img_set_request_str(char* ip, char* out_str, char* request_id) {
    int   ret       = -1;
    char* json_str  = NULL;
    char  url[1024] = {0};

    if (strstr(ip, ".") != NULL) {
        sprintf(url, "http://%s:%d/image/upload", ip, HTTP_SERVER_PORT);
    } else {
        sprintf(url, "http://[%s]:%d/image/upload", ip, HTTP_SERVER_PORT);
    }

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "url", url);

    json_str = cJSON_PrintUnformatted(root);
    memcpy(out_str, json_str, strlen(json_str));

    ret = 0;

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

int send_request_cb(char* ip, char* signature, char* url) {
    msg_header_t* msg_header;
    int           ret             = 0;
    char          request_id[9]   = {0};
    int           request_id_len  = 8;
    char          body_data[2048] = {0};
    int           body_len        = 2048;
    char          signature1[128] = {0};
    int           signature1_len  = 0;

    msg_header = (msg_header_t*)malloc(body_len);
    memset(msg_header, '\0', body_len);
    char request_data[1024]      = {0};
    char request_data_base[1024] = {0};
    ret                          = cps_generate_random(request_id, request_id_len);

    _img_set_request_str(url, request_data, request_id);
    ret = sign_with_internal_key(request_data, strlen(request_data), signature1, &signature1_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key!");
    base64_encode(request_data, request_data_base, strlen(request_data));

    ret = cps_msg_body_comp(
        ip, request_id, signature1, request_data_base, strlen(request_data_base), body_data, &body_len);
    cps_request_init(msg_header, body_data, body_len, CHSM_MAIN_TYPE, CMD_IMAGE);
    ret = cps_request_send(msg_header, _set_img_url_cb, request_id);

    if (msg_header) {
        free(msg_header);
    }
out:
    return ret;
}

int _cb_addr_check_request_str(char* ip, char* out_str, char* request_id) {
    int   ret      = -1;
    char* json_str = NULL;

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "oprType", "checkcb");
    cJSON_AddStringToObject(root, "addr", ip);

    json_str = cJSON_PrintUnformatted(root);
    memcpy(out_str, json_str, strlen(json_str));

    ret = 0;

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

int send_cb_addr_request_cb(char* ip, char* cb_ip) {
    msg_header_t* msg_header;
    int           ret             = 0;
    char          request_id[9]   = {0};
    int           request_id_len  = 8;
    char          body_data[2048] = {0};
    int           body_len        = 2048;
    char          signature1[128] = {0};
    int           signature1_len  = 0;

    msg_header = (msg_header_t*)malloc(body_len);
    memset(msg_header, '\0', body_len);
    char request_data[1024]      = {0};
    char request_data_base[1024] = {0};
    ret                          = cps_generate_random(request_id, request_id_len);

    _cb_addr_check_request_str(cb_ip, request_data, request_id);
    ret = sign_with_internal_key(request_data, strlen(request_data), signature1, &signature1_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key!");
    base64_encode(request_data, request_data_base, strlen(request_data));

    ret = cps_msg_body_comp(
        ip, request_id, signature1, request_data_base, strlen(request_data_base), body_data, &body_len);
    cps_request_init(msg_header, body_data, body_len, CHSM_MAIN_TYPE, 0);
    ret = cps_request_send(msg_header, _set_img_url_cb, request_id);

    if (msg_header) {
        free(msg_header);
    }
out:
    return ret;
}

int cut_ip(char cut_str, char* ip, char* segment, int* num) {
    char* p           = NULL;
    char  hex[3]      = {0};
    int   segment_len = 0;
    int   value       = 0;

    p = strrchr(ip, cut_str);
    if (p == NULL) {
        return -1;
    }

    segment_len = p - ip;
    memcpy(segment, ip, segment_len);
    sprintf(segment + segment_len, ".%d", 0);

    if (cut_str == '.') {
        *num = atoi(p + 1);
    } else {
        sprintf(hex, "%s", p + 1);
        for (int j = 0; j < 2; j++) {
            if (hex[j] > '0' && hex[j] < '9') {
                value = value * 16 + hex[j] - '0';
            } else if ('a' <= hex[j] && hex[j] <= 'f') {
                value = value * 16 + 10 + hex[j] - 'a';
            } else if ('A' <= hex[j] && hex[j] <= 'F') {
                value = value * 16 + 10 + hex[j] - 'A';
            }
        }
        *num = value;
    }

    return 0;
}

/**
 * @brief 获取指定ip的网段以及最后ip段的值
 *
 * @param family (IPv4|IPv6)
 * @param ip ip地址
 * @param segment 网段
 * @param num
 */
int _get_segment_and_last_num(char* family, char* ip, char* segment, int* num) {
    int ret = 0;
    if (!strcmp("IPv4", family)) {
        return cut_ip('.', ip, segment, num);
    } else {
        return cut_ip(':', ip, segment, num);
    }
}
/* ======================================================================================
 * implementation
 */
int cps_addr_add(int argc, char* argv[]) {
    char* addr_name   = argv[4];
    char* tenant_name = argv[6];
    char* addr_info   = argv[8];
    char* remark      = NULL;

    if (argc == 11) {
        remark = argv[10];
    } else {
        remark = "";
    }

    int ret = 0;
    addr_info_t* parsed_info_array = NULL;
    int array_size = 0;

    // 解析 JSON 格式的 addr_info
    ret = parse_addr_info_json(addr_info, &parsed_info_array, &array_size);
    if (ret != 0) {
        return -1;
    }

    // 判断当前租户是否已经添加地址
    ret = cps_get_num_from_db(CPS_ADDR_INFO_TABLE, "tenant_name='%s' or addr_name='%s'", tenant_name, addr_name);
    if (ret) {
        printf("the addr_name or tenant has already been assigned!\n");
        free_addr_info_array(parsed_info_array, array_size);
        return -1;
    }
   
    // 检查所有地址信息的IP段冲突
    for (int i = 0; i < array_size; i++) {
        ret = db_check_addr_info(parsed_info_array[i].ip_range, parsed_info_array[i].subnet, tenant_name);
        if (ret) {
            printf("db_check_addr_info failed!\n");
            free_addr_info_array(parsed_info_array, array_size);
            return ret;
        }
    }

    // 为每个地址信息对象插入数据库记录
    for (int i = 0; i < array_size; i++) {
        ret = cps_insert_data_to_db(
            CPS_TENANT_ADDR_INFO_TABLE,
            "(addr_name,tenant_name,subnet,gateway,ip_range,bindType,interface_name,bridge_interface,vlan_id_range) values ('%s','%s','%s','%s','%s',%d,'%s','%s','%s')",
            addr_name,
            tenant_name,
            parsed_info_array[i].subnet,
            parsed_info_array[i].gateway,
            parsed_info_array[i].ip_range,
            parsed_info_array[i].bindType,
            parsed_info_array[i].interface_name ? parsed_info_array[i].interface_name : "",
            parsed_info_array[i].bridge_interface ? parsed_info_array[i].bridge_interface : "",
            parsed_info_array[i].vlan_id ? parsed_info_array[i].vlan_id : "");
        
        if (ret) {
            printf("set addr failed for index %d!\n", i);
            free_addr_info_array(parsed_info_array, array_size);
            return -1;
        }
    }
    ret = cps_insert_data_to_db(CPS_ADDR_INFO_TABLE, 
        "(addr_name,tenant_name,remark) values ('%s','%s','%s')",
        addr_name,
        tenant_name,
        remark);
    if (ret) {  
        printf("set add_info to db failed!\n");
        free_addr_info_array(parsed_info_array, array_size);
        return -1;
    }
    // 清理分配的内存
    free_addr_info_array(parsed_info_array, array_size);
    return ret;
}

int cps_addr_set(int argc, char* argv[]) {
    char* addr_name   = argv[4];
    char* tenant_name = argv[6];
    char* addr_info   = argv[8];
    char* remark      = NULL;

    if (argc == 11) {
        remark = argv[10];
    } else {
        remark = "";
    }
    int ret = 0;
    addr_info_t* parsed_info_array = NULL;
    int array_size = 0;

    // 解析 JSON 格式的 addr_info
    ret = parse_addr_info_json(addr_info, &parsed_info_array, &array_size);
    if (ret != 0) {
        return -1;
    }
    
    ret = cps_get_num_from_db(CPS_ADDR_INFO_TABLE, "tenant_name='%s'", tenant_name);
    if (!ret) {
        printf(gettext("no addr info of tenant!"));
        return -1;
    }
   // 查询是否有所属ip正在被使用
    for (int i = 0; i < array_size; i++) {
        //printf("ip_range: %s, subnet: %s, tenant_name: %s\n", parsed_info_array[i].ip_range, parsed_info_array[i].subnet, tenant_name);
        ret = db_check_addr_info(parsed_info_array[i].ip_range, parsed_info_array[i].subnet, tenant_name);
        if (ret) {
            printf("db_check_addr_info failed!\n");
            free_addr_info_array(parsed_info_array, array_size);
            return ret;
        }
        
    }
    // 更新地址信息
    if (remark != NULL) {
        ret = cps_update_data_to_db(
            CPS_ADDR_INFO_TABLE,
            "remark='%s' where tenant_name='%s'",
            remark,
            tenant_name);
        if (ret) {
            printf("cps_update_data_to_db failed!\n");
            free_addr_info_array(parsed_info_array, array_size);
            return -1;
        }
    }
    for (int i = 0; i < array_size; i++) {
        ret = cps_get_num_from_db(CPS_TENANT_ADDR_INFO_TABLE, "tenant_name='%s' and addr_name='%s' and interface_name='%s'", tenant_name, addr_name, parsed_info_array[i].interface_name);
        //不存在则添加
        if (!ret) {
            printf("interface_name %s not found,need to add!\n", parsed_info_array[i].interface_name);
            ret = cps_insert_data_to_db(CPS_TENANT_ADDR_INFO_TABLE, 
                "(addr_name,tenant_name,subnet,gateway,ip_range,bindType,interface_name,bridge_interface,vlan_id_range) values ('%s','%s','%s','%s','%s',%d,'%s','%s','%s')",
                addr_name,
                tenant_name,
                parsed_info_array[i].subnet,
                parsed_info_array[i].gateway,
                parsed_info_array[i].ip_range,
                parsed_info_array[i].bindType,
                parsed_info_array[i].interface_name,
                parsed_info_array[i].bridge_interface,
                parsed_info_array[i].vlan_id);
            if (ret) {
                printf("cps_insert_data_to_db failed!\n");
                free_addr_info_array(parsed_info_array, array_size);
                return -1;
            }
        } else {
            //存在则更新
            ret = cps_update_data_to_db(CPS_TENANT_ADDR_INFO_TABLE, 
                "bridge_interface='%s',ip_range='%s',subnet='%s',gateway='%s',bindType='%d', vlan_id_range='%s' where tenant_name='%s' and addr_name='%s' and interface_name='%s'", 
            parsed_info_array[i].bridge_interface,
            parsed_info_array[i].ip_range,
            parsed_info_array[i].subnet,
            parsed_info_array[i].gateway,
            parsed_info_array[i].bindType,
            parsed_info_array[i].vlan_id ? parsed_info_array[i].vlan_id : "",
            tenant_name,
            addr_name,
            parsed_info_array[i].interface_name);
            if (ret) {
                printf("cps_update_data_to_db failed!\n");
                free_addr_info_array(parsed_info_array, array_size);
                return -1;
            }
        }
    }
    free_addr_info_array(parsed_info_array, array_size);
    return ret;
}

int cps_addr_del(int argc, char* argv[]) {
    int   ret         = 0;
    char* tenant_name = argv[4];

    // 判断是否创建了虚机
    ret = cps_get_num_from_db(CPS_VSM_MANAGE_TABLE, "tenant_name='%s'", tenant_name);
    if (ret) {
        printf(gettext("vsm in use,please delete them!"));
        return -1;
    }

    // 查询是否有所属ip正在被使用
    ret = cps_get_num_from_db(CPS_IP_INFO_TABLE, "use_status='1' and tenant_name='%s'", tenant_name);
    if (ret) {
        printf(gettext("the previous address has already been assigned!"));
        return -1;
    }

    // 对所属租户的工单作废
    ret = db_work_order_invalid(tenant_name);
    if (ret) {
        printf(gettext("set addr faild!"));
        return -1;
    }

    // 删除地址表中数据
    ret = cps_del_data_from_db(CPS_ADDR_INFO_TABLE, "tenant_name='%s'", tenant_name);
    if (ret) {
        printf(gettext("del addr faild!"));
        return -1;
    }
    ret = cps_del_data_from_db(CPS_TENANT_ADDR_INFO_TABLE, "tenant_name='%s'", tenant_name);
    if (ret) {
        printf("del addr faild2!\n");
        return -2;
    }
    return 0;
}

int cps_addr_check(int argc, char* argv[]) {
    return db_check_addr_info(argv[4], argv[6], argv[8]);
}

int cps_img_addr_set(int argc, char* argv[]) {
    int   ret           = 0;
    char* ip            = argv[4];
    char  err_msg[2048] = {0};
    // 查看是否有设备存在,不存在不允许设置
    // ret = cps_get_num_from_db(CPS_DEV_STATUS_TABLE, "device_name!=''");
    // if (!ret) {
    //     printf(gettext("there are no devices to set!"));
    //     return -1;
    // }

    ret = db_set_addr_image(ip, err_msg, send_request_cb);
    if (ret) {
        printf(gettext("set image url faild,device ip is:%s"), err_msg);
    }

    return ret;
}

int cps_img_addr_set_at_add_device(char* ip) {
    int ret = 0;

    ret = db_set_addr_image(ip, NULL, send_request_cb);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "set image url faild!");
    }

    return ret;
}

int cps_cb_addr_check_at_add_device(void) {
    int  ret         = 0;
    char cb_addr[64] = {0};

    // 获取回调地址,没有返回
    ret = cps_get_callback_addr(cb_addr, 64);
    if (ret) {
        return 0;
    }

    ret = db_check_cb_addr(cb_addr, NULL, send_cb_addr_request_cb);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "invalid callback addr!");
        return -1;
    }

    return ret;
}

int cps_callback_addr_set(int argc, char* argv[]) {
    int   ret          = 0;
    char* addr         = argv[4];
    char  errmsg[1024] = {0};

    if (argc == 7) {
        ret = db_check_cb_addr(addr, errmsg, send_cb_addr_request_cb);
        if (ret) {
            printf(gettext("invalid callback addr,host ip is:%s!"), errmsg);
            return -1;
        }
    }

    ret = cps_update_data_to_db(CPS_CALLBACKADDR_INFO_TABLE, "addr='%s'", addr);
    if (ret) {
        printf(gettext("set callback addr faild!"));
        return -1;
    }

    return ret;
}

int cps_image_drift_option(int argc, char* argv[]) {
    int ret         = 0;
    int option_flag = 0;
    if (!strcmp("on", argv[3])) {
        option_flag = 1;
    }

    ret = cps_update_data_to_db(CPS_DRIFT_OPTION_TABLE, "status='%d'", option_flag);
    if (ret) {
        printf(gettext("failed to set image drift switch!"));
        return -1;
    }

    return ret;
}

