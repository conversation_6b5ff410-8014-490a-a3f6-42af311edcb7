TARGET  = cps
VERSION_NAME = CPS
CFLAGS =-O2 -DPLAT_VERSION="\"$(VERSION_NAME)\"" -Werror -Wno-unused-result

LIBBMQDIR=$(SRC_ROOT)/base/libapi/bmqapi
LIBCJSONDIR=$(SRC_ROOT)/base/libapi/cjson
LIBNTLDIR=$(SRC_ROOT)/base/libapi/ntl
LIBSM4DIR=$(SRC_ROOT)/base/libapi/sm4
INCPUBLIC=$(SRC_ROOT)/base/cli/public
INCLIBAPI=$(SRC_ROOT)/base/libapi
LIBCUTIL = ${SRC_ROOT}/base/libapi/cutil
LIBZMQDIR  = ${SRC_ROOT}/base/osource/libzmq/dist
LIBCZMQDIR = ${SRC_ROOT}/base/osource/czmq/dist

KEYSTOREDIR=${SRC_ROOT}/base/key_store

ifeq ($(FW_ARCH), arm)
	LIBCRYTOCARDDIR = ${SRC_ROOT}/base/libapi/csm_adaptor
	LIBUKEYDIR = ${SRC_ROOT}/base/ukey/ft/arm64
	KEYSTOREDIR_LIB=${KEYSTOREDIR}/arm64
else
	LIBCRYTOCARDDIR = ${SRC_ROOT}/base/libapi/csm_adaptor
	LIBUKEYDIR = ${SRC_ROOT}/base/ukey/ft/x86_64
	KEYSTOREDIR_LIB=${KEYSTOREDIR}/x86_64
endif

#CC = gcc

#C_SOURCES = cps_cli.c cps_device.c cps_rights.c cps_strategy.c cps_tenant.c cps_work_order.c database_api.c ./public/rights_and_device_key_public.c ./public/#sm2_create_key_pair.c ./public/sm2_sign_and_verify.c ./public/sm3_with_preprocess.c

OBJS :=$(wildcard *.c ./public/*.c)
OBJS :=$(patsubst %.c,%.o,$(OBJS))

C_HEAD = $(wildcard *.h)


MO_FILE=cps_values.mo
PO_FILE=cps_values.po

INCLUDE :=$(wildcard ./ ./public/)
INCLUDE :=$(addprefix -I,$(INCLUDE))
INCLUDE += -I${INCPUBLIC} -I${INCLIBAPI} -I${LIBZMQDIR}/include -I${LIBCZMQDIR}/include -I${LIBCRYTOCARDDIR}/include -I${LIBUKEYDIR}/include -I${LIBBMQDIR} -I${LIBCUTIL} -I${LIBCJSONDIR} -I${KEYSTOREDIR}/include -I${LIBSM4DIR}

LIBS = -lcliframe \
       -lsqlite3 \
       -lfwlog \
       -lcli \
       -llicense \
       -L${INCPUBLIC} -lclipublic -lsqlite3 \
       -lcluster_public \
       -lm \
     -L$(LIBSM4DIR) -L$(LIBCJSONDIR) -L$(LIBNTLDIR) -L${LIBUKEYDIR}/lib -L${KEYSTOREDIR_LIB}\
        -lpthread -lcjson  -les_3000gm  -lcrypt_sm4 -lkey_store  -lcrypto

INIT_EXPORT_LIST=${SRC_ROOT}/compile/build/init_export_list
ZH_FILE_PATH=./locales/zh_CN/
EN_FILE_PATH=./locales/en_US/
PO_FILES := $(ZH_FILE_PATH)$(PO_FILE) $(EN_FILE_PATH)$(PO_FILE)


all:$(TARGET) $(MO_FILE)

$(TARGET): $(OBJS)
	$(CC) -o $@ -Wl,-Bsymbolic -export-dynamic $^  $(LIBS)
	strip ${TARGET}

%.o: %.c
	$(CC) $(CFLAGS) $(INCLUDE) -g -c --verbose $< -o $@

clean:
	rm -f $(TARGET) *.o core  new.pot
	rm -f ./public/*.o
	cd $(ZH_FILE_PATH) && rm -f $(MO_FILE) old.po
	cd $(EN_FILE_PATH) && rm -f $(MO_FILE) old.po

install:
	mkdir -p $(INSTALL_ROOT)/../cpsroot/usr/local/bin/
	cp -f $(TARGET) $(INSTALL_ROOT)/../cpsroot/usr/local/bin/
	cp -f $(TARGET) $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_monitor
	cp -f $(TARGET) $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_cluster
	cp -f $(TARGET) $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_callback
	mkdir -p $(INSTALL_ROOT)/../cpsroot//usr/local/i18n/locales/zh_CN/LC_MESSAGES/
	mkdir -p $(INSTALL_ROOT)/../cpsroot//usr/local/i18n/locales/en_US/LC_MESSAGES/
	cp -f $(ZH_FILE_PATH)$(MO_FILE) $(INSTALL_ROOT)/../cpsroot/usr/local/i18n/locales/zh_CN/LC_MESSAGES/
	cp -f $(EN_FILE_PATH)$(MO_FILE) $(INSTALL_ROOT)/../cpsroot/usr/local/i18n/locales/en_US/LC_MESSAGES/

uninstall:
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/bin/$(TARGET)
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_monitor
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_cluster
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/bin/cps_callback
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/i18n/locales/zh_CN/LC_MESSAGES/$(MO_FILE)
	rm -rf $(INSTALL_ROOT)/../cpsroot/usr/local/i18n/locales/en_US/LC_MESSAGES/$(MO_FILE)

$(MO_FILE): $(ZH_FILE_PATH)$(PO_FILE) $(EN_FILE_PATH)$(PO_FILE) $(OBJS)
	@xgettext --from-code=utf-8  -kgettext -o  new.pot *.c *.h
	@cd $(ZH_FILE_PATH) && cp $(PO_FILE) old.po && msgmerge old.po ../../new.pot -o $(PO_FILE)
	@cd $(EN_FILE_PATH) && cp $(PO_FILE) old.po && msgmerge old.po ../../new.pot -o $(PO_FILE)
	@$(foreach TEMP_FILE, $(PO_FILES), cat $(TEMP_FILE) | awk 'BEGIN{tag = 0;i = 0;} \
	{\
		++i;\
		if(index($$0, "fuzzy")!=0)\
		{\
			printf("$(TEMP_FILE):%d: Warning: exist fuzzy\n",i);\
			tag=0;\
		}\
		else\
		{ \
			if($$0 ~ /msgstr ""/)\
			{\
				tag=1;\
			}\
			else\
			{\
				if(tag == 1)\
				{\
					if(length($$0) == 0)\
					{\
						printf("$(TEMP_FILE):%d: Warning: The statement doesn'"'"'t have been translated!\n",i-1);\
					}\
				}\
				tag=0;\
			}\
		}\
	}\
	END{\
		if(tag == 1)\
			printf("$(TEMP_FILE):%d: Warning: The statement doesn'"'"'t have been translated!\n",i-1);\
	}\
	';)
	@cd $(ZH_FILE_PATH) && msgfmt -o $(MO_FILE) $(PO_FILE)
	@cd $(EN_FILE_PATH) && msgfmt -o $(MO_FILE) $(PO_FILE)

gettext:
	xgettext --from-code=utf-8  -kgettext -o $(PO_FILE)  *.c *.h

