/*
 * Project: base
 * Moudle:
 * File: key_public.h
 * Created Date: 2023-08-31 16:00:41
 * Author: caohongfa
 * Description: caohongfa
 * -----
 * todo: modified
 * -----
 * Copyright (c), Inc
 */
#ifndef _KEY_OPERATE_H
#define _KEY_OPERATE_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
int encrypt_with_kek(unsigned char* in_data, unsigned char* out_data, unsigned int data_len);

int decrypt_with_kek(unsigned char* in_data, unsigned char* out_data, unsigned int data_len);
int sm4_decrypt_data(
    unsigned char* in_data,
    unsigned int   indata_len,
    unsigned char* out_data,
    unsigned int*  out_length);

int sm4_encrypt_data(
    unsigned char* in_data,
    unsigned int   indata_len,
    unsigned char* out_data,
    unsigned int*  out_length);

/**
 * @brief 获取签名公钥
 *
 * @param out_key 结构(04|x|y),base64编码
 * @return int 成功返回0
 */
int get_sign_pub_key(unsigned char* out_key);

int get_sign_pub_key_without_base64(unsigned char* out_key);
/**
 * @brief 使用内部私钥对数据进行签名
 *
 * @param in_data 输入数据
 * @param in_data_len 输入数据长度
 * @param out_signature 返回的签名值,base64编码
 * @param sign_len 签名值长度
 * @return int 成功返回0
 */
int sign_with_internal_key(unsigned char* in_data, int in_data_len, char* out_signature, int* sign_len);

/**
 * @brief 外部公钥验签
 *
 * @param pub_key ECCPUBLICKEYBLOB公钥结构序列化
 * @param Hash 数据hash
 * @param SignData 签名值
 * @return int 成功返回0
 */
int verify_ecc(void* pub_key, char* Hash, char* SignData);
int get_sign_pri_key(unsigned char* out_key);
#endif  // _KEY_OPERATE_H
