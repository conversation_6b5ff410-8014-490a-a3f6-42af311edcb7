# cps_addr_add 函数修改说明

## 修改概述

原来的 `cps_addr_add` 函数只能处理单个地址信息对象，现在修改为支持多个地址信息对象的数组。

## 主要修改点

### 1. 函数签名修改
```c
// 修改前
static int parse_addr_info_json(const char* addr_info, addr_info_t* parsed_info);

// 修改后  
static int parse_addr_info_json(const char* addr_info, addr_info_t** parsed_info_array, int* array_size);
```

### 2. 新增内存管理函数
```c
static void free_addr_info_array(addr_info_t* info_array, int array_size);
```

### 3. cps_addr_add 函数逻辑修改
- 解析多个地址信息对象
- 为每个对象检查IP段冲突
- 为每个对象插入数据库记录

## 流程图

```mermaid
flowchart TD
    A[开始: cps_addr_add] --> B[解析命令行参数]
    B --> C[调用 parse_addr_info_json]
    C --> D{JSON解析成功?}
    D -->|否| E[返回错误]
    D -->|是| F[获取地址信息数组]
    F --> G[检查租户和地址名是否已存在]
    G --> H{已存在?}
    H -->|是| I[释放内存并返回错误]
    H -->|否| J[遍历所有地址信息对象]
    J --> K[检查IP段冲突]
    K --> L{有冲突?}
    L -->|是| M[释放内存并返回错误]
    L -->|否| N{还有对象?}
    N -->|是| J
    N -->|否| O[插入主地址表记录]
    O --> P{插入成功?}
    P -->|否| Q[释放内存并返回错误]
    P -->|是| R[遍历所有地址信息对象]
    R --> S[插入租户地址表记录]
    S --> T{插入成功?}
    T -->|否| U[释放内存并返回错误]
    T -->|是| V{还有对象?}
    V -->|是| R
    V -->|否| W[释放内存]
    W --> X[返回成功]
```

## 数据结构变化

### 修改前
```c
addr_info_t parsed_info = {0};  // 单个对象
```

### 修改后
```c
addr_info_t* parsed_info_array = NULL;  // 对象数组
int array_size = 0;                     // 数组大小
```

## 错误处理改进

1. **内存泄漏防护**: 所有错误路径都会调用 `free_addr_info_array` 释放内存
2. **详细错误信息**: 为每个数组索引提供具体的错误信息
3. **事务性操作**: 如果任何一个对象插入失败，会清理已分配的内存

## 测试用例

### 输入JSON
```json
{
  "addr_info": [
    {
      "bindType": 0,
      "interface_name": "eth1",
      "bridge_interface": "brg1", 
      "subnet": "************/*************, fd86:731:ac:111::/64",
      "vlan_id": "100",
      "ip_range": [
        "*************-*************/************",
        "fd86:731:ac:111::64-fd86:731:ac:111::67/fd86:731:ac:111::1"
      ]
    },
    {
      "bindType": 0,
      "interface_name": "eth2",
      "bridge_interface": "brg2",
      "subnet": "************/*************, fd86:731:ac:112::/64", 
      "vlan_id": "1001",
      "ip_range": [
        "*************-*************/************",
        "fd86:731:ac:112::64-fd86:731:ac:112::67/fd86:731:ac:112::1"
      ]
    }
  ]
}
```

### 预期结果
- 解析出2个地址信息对象
- 每个对象包含完整的网络配置信息
- 为每个对象插入数据库记录
- 正确处理IPv4和IPv6地址

## 兼容性说明

修改后的代码保持了向后兼容性：
- 单个对象的JSON仍然可以正常工作
- 数据库表结构没有变化
- 函数接口保持一致 