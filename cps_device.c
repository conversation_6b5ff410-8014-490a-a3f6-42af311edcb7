/*
 * Project: base
 * Moudle: cps
 * File: cps_device.c
 * Created Date: 2023-08-29 14:44:15
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include <math.h>

#include "./public/alg_id.h"
#include "./public/key_support.h"
#include "./public/sm2_create_key_pair.h"
#include "cjson.h"
#include "cps_common.h"
#include "cps_device.h"
#include "cps_msg.h"
#include "cps_vsm_manage.h"
#include "database_api.h"
#include "fwlog.h"
#include "key_public.h"
#include "key_store.h"
#include "public/key_operate.h"

/* ======================================================================================
 * macros
 */
#define BODY_LEN 2048

/* ======================================================================================
 * types
 */
enum { RET_OK = 0, RET_RESULT_NULL };

// OVS信息内存缓存结构
typedef struct ovs_cache_item {
    char ovs_instance[128];
    char ovs_ipv4[64];
    char ovs_ipv6[128];
    char port_name[64];
    int  port_mode;
    char port_vlans[1024];
    int  ping_enable;
    int  found_in_server;         // 标记字段：1=在服务器数据中找到，0=未找到（用于删除检测）
    struct ovs_cache_item* next;
} OVS_CACHE_ITEM_T;

// 设备OVS缓存结构
typedef struct device_ovs_cache {
    char device_name[64];
    OVS_CACHE_ITEM_T* ovs_list;
    int is_first_time;  // 标记是否为首次获取
} DEVICE_OVS_CACHE_T;

typedef struct device_ovs_cache_list {
    DEVICE_OVS_CACHE_T* device_ovs_cache;
    struct device_ovs_cache_list* next;
} DEVICE_OVS_CACHE_LIST_T;

// 哈希表节点结构
typedef struct hash_node {
    char* key;                    // 字符串键（device_name）
    void* value;                  // 通用指针值（DEVICE_OVS_CACHE_T*）
    struct hash_node* next;       // 链表指针，处理哈希冲突
} HASH_NODE_T;

// 哈希表结构
typedef struct hash_table {
    HASH_NODE_T** buckets;        // 哈希桶数组
    size_t bucket_count;          // 哈希桶数量
    size_t size;                  // 当前存储的元素数量
} HASH_TABLE_T;

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */
static char* g_ip           = NULL;
static char* g_host_name    = NULL;
static int   min_flavor_cpu = 0;
static int   min_flavor_mem = 0;
static int _incremental_update_ovs_info(DEVICE_OVS_CACHE_T* device_cache, cJSON* ovs_info, const char* device_name);
// OVS信息内存缓存全局变量 - 使用哈希表
static HASH_TABLE_T* g_device_ovs_cache_table = NULL;
static void _free_ovs_cache_list(OVS_CACHE_ITEM_T* list);
// 全局变量定义
DEVICE_INFO_MONITOR_T g_device_list[MAX_DEVICE_NUM];
int g_device_count = 0;


// 哈希表大小（质数）
#define HASH_TABLE_SIZE 101
/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/**
 * @brief djb2 哈希函数 - 处理字符串键
 * @param str 输入字符串
 * @return 哈希值
 */
static unsigned long _hash_function(const char* str) {
    unsigned long hash = 5381;
    int c;

    while ((c = *str++)) {
        hash = ((hash << 5) + hash) + c; // hash * 33 + c
    }

    return hash;
}

/**
 * @brief 初始化哈希表
 * @param bucket_count 哈希桶数量
 * @return 成功返回哈希表指针，失败返回 NULL
 */
static HASH_TABLE_T* hash_table_init(size_t bucket_count) {
    HASH_TABLE_T* table = (HASH_TABLE_T*)malloc(sizeof(HASH_TABLE_T));
    if (!table) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for hash table");
        return NULL;
    }

    table->buckets = (HASH_NODE_T**)calloc(bucket_count, sizeof(HASH_NODE_T*));
    if (!table->buckets) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for hash table buckets");
        free(table);
        return NULL;
    }

    table->bucket_count = bucket_count;
    table->size = 0;

    DEBUG_CPS_CLI(COMMON_DEBUG, "Hash table initialized with %zu buckets", bucket_count);
    return table;
}

/**
 * @brief 插入或更新 key-value 对
 * @param table 哈希表指针
 * @param key 字符串键
 * @param value 值指针
 * @return 成功返回 0，失败返回 -1
 */
static int hash_table_insert(HASH_TABLE_T* table, const char* key, void* value) {
    if (!table || !key) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid parameters for hash_table_insert");
        return -1;
    }

    unsigned long hash = _hash_function(key);
    size_t index = hash % table->bucket_count;

    // 查找是否已存在该键
    HASH_NODE_T* current = table->buckets[index];
    while (current) {
        if (strcmp(current->key, key) == 0) {
            // 键已存在，更新值
            current->value = value;
            DEBUG_CPS_CLI(COMMON_DEBUG, "Updated existing key: %s", key);
            return 0;
        }
        current = current->next;
    }

    // 创建新节点
    HASH_NODE_T* new_node = (HASH_NODE_T*)malloc(sizeof(HASH_NODE_T));
    if (!new_node) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for hash node");
        return -1;
    }

    // 复制键
    new_node->key = (char*)malloc(strlen(key) + 1);
    if (!new_node->key) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for hash node key");
        free(new_node);
        return -1;
    }
    strcpy(new_node->key, key);

    new_node->value = value;
    new_node->next = table->buckets[index];
    table->buckets[index] = new_node;
    table->size++;

    DEBUG_CPS_CLI(COMMON_DEBUG, "Inserted new key: %s (table size: %zu)", key, table->size);
    return 0;
}

/**
 * @brief 根据键获取值
 * @param table 哈希表指针
 * @param key 字符串键
 * @return 找到返回值指针，未找到返回 NULL
 */
static void* hash_table_get(HASH_TABLE_T* table, const char* key) {
    if (!table || !key) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid parameters for hash_table_get");
        return NULL;
    }

    unsigned long hash = _hash_function(key);
    size_t index = hash % table->bucket_count;

    HASH_NODE_T* current = table->buckets[index];
    while (current) {
        if (strcmp(current->key, key) == 0) {
            DEBUG_CPS_CLI(COMMON_DEBUG, "Found key: %s", key);
            return current->value;
        }
        current = current->next;
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "Key not found: %s", key);
    return NULL;
}

/**
 * @brief 删除指定键的条目
 * @param table 哈希表指针
 * @param key 字符串键
 * @return 成功返回 0，失败返回 -1
 */
static int hash_table_delete(HASH_TABLE_T* table, const char* key) {
    if (!table || !key) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid parameters for hash_table_delete");
        return -1;
    }

    unsigned long hash = _hash_function(key);
    size_t index = hash % table->bucket_count;

    HASH_NODE_T* current = table->buckets[index];
    HASH_NODE_T* prev = NULL;

    while (current) {
        if (strcmp(current->key, key) == 0) {
            // 找到要删除的节点
            if (prev) {
                prev->next = current->next;
            } else {
                table->buckets[index] = current->next;
            }

            free(current->key);
            free(current);
            table->size--;

            DEBUG_CPS_CLI(COMMON_DEBUG, "Deleted key: %s (table size: %zu)", key, table->size);
            return 0;
        }
        prev = current;
        current = current->next;
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "Key not found for deletion: %s", key);
    return -1;
}

/**
 * @brief 销毁哈希表并释放所有内存
 * @param table 哈希表指针
 */
static void hash_table_destroy(HASH_TABLE_T* table) {
    if (!table) {
        return;
    }

    for (size_t i = 0; i < table->bucket_count; i++) {
        HASH_NODE_T* current = table->buckets[i];
        while (current) {
            HASH_NODE_T* temp = current;
            current = current->next;
            free(temp->key);
            free(temp);
        }
    }

    free(table->buckets);
    free(table);

    DEBUG_CPS_CLI(COMMON_DEBUG, "Hash table destroyed");
}
/**
 * @brief 设备初始化
 *
 * @return int 0成功
 */
int _device_init(void) {
    int              ret               = -1;
    void*            hDeviceHandle     = NULL;
    void*            hSessionHandle    = NULL;
    char             firm_version[128] = {0};
    unsigned char    key[64]           = {0};
    ECCrefPublicKey  puk;
    ECCrefPrivateKey prk;
    ECCrefPublicKey  puk1;
    ECCrefPrivateKey prk1;
    InterkeyStore    key_info = {0};
    SM2_KEY_PAIR     key_pair = {0};

    puk.bits  = 256;
    prk.bits  = 256;
    puk1.bits = 256;
    prk1.bits = 256;

    // 生成两对对设备保护密钥,一对签名,一对加密
    ret = sm2_create_key_pair(&key_pair);
    API_CHECK_FUNC(ret, "sm2_create_key_pair");
    memcpy(puk.x + 32, key_pair.pub_key + 1, +ECCref_MAX_LEN / 2);
    memcpy(puk.y + 32, key_pair.pub_key + 1 + ECCref_MAX_LEN / 2, +ECCref_MAX_LEN / 2);
    memcpy(prk.K + 32, key_pair.pri_key, +ECCref_MAX_LEN / 2);

    key_info.key_node[0].iKeyType      = SM2_KEY;
    key_info.key_node[0].iKeyIndex     = 1;
    key_info.key_node[0].iAlgId        = HSM_SGD_SM2_1;
    key_info.key_node[0].iKeyFlag      = SIGN_KEY;
    key_info.key_node[0].iKeyBits      = 256;
    key_info.key_node[0].pKeyPriInfo   = &prk;
    key_info.key_node[0].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[0].pKeyPubInfo   = &puk;
    key_info.key_node[0].iKeyPubLength = sizeof(ECCrefPublicKey);

    ret = sm2_create_key_pair(&key_pair);
    API_CHECK_FUNC(ret, "sm2_create_key_pair");
    memcpy(puk1.x + 32, key_pair.pub_key + 1, ECCref_MAX_LEN / 2);
    memcpy(puk1.y + 32, key_pair.pub_key + 1 + ECCref_MAX_LEN / 2, ECCref_MAX_LEN / 2);
    memcpy(prk1.K + 32, key_pair.pri_key, ECCref_MAX_LEN / 2);

    key_info.key_node[1].iKeyType      = SM2_KEY;
    key_info.key_node[1].iKeyIndex     = 1;
    key_info.key_node[1].iAlgId        = HSM_SGD_SM2_1;
    key_info.key_node[1].iKeyFlag      = ENC_KEY;
    key_info.key_node[1].iKeyBits      = 256;
    key_info.key_node[1].pKeyPriInfo   = &prk1;
    key_info.key_node[1].iKeyPriLength = sizeof(ECCrefPrivateKey);
    key_info.key_node[1].pKeyPubInfo   = &puk1;
    key_info.key_node[1].iKeyPubLength = sizeof(ECCrefPublicKey);

    ret = SetDeviceKey(SIGN_ENC_KEY, &key_info, encrypt_with_kek, decrypt_with_kek);
    API_CHECK_FUNC(ret, "SetDeviceKey");

out:
    return ret;
}

void _get_flavor_info_from_json(cJSON* tmp) {
    cJSON*        flavor = cJSON_GetObjectItem(tmp, "flavorInfo");
    cJSON*        tmp1   = NULL;
    cJSON*        tmp2   = NULL;
    FLAVOR_INFO_T flavor_info;
    if (flavor) {
        int arry_size = cJSON_GetArraySize(flavor);

        for (int j = 0; j < arry_size; j++) {
            tmp1               = cJSON_GetArrayItem(flavor, j);
            tmp2               = cJSON_GetObjectItem(tmp1, "flavor");
            flavor_info.flavor = atoi(tmp2->valuestring);
            tmp2               = cJSON_GetObjectItem(tmp1, "cpu");
            flavor_info.cpu    = atoi(tmp2->valuestring);
            tmp2               = cJSON_GetObjectItem(tmp1, "men");
            flavor_info.mem    = atoi(tmp2->valuestring);
            tmp2               = cJSON_GetObjectItem(tmp1, "disk");
            flavor_info.disk   = atoi(tmp2->valuestring);

            if (flavor_info.flavor == 1) {
                min_flavor_cpu = flavor_info.cpu;
                min_flavor_mem = flavor_info.mem;
            }
            // 设置到数据库
            db_set_flavor_info(flavor_info);
        }
    }
}

int _get_vsm_info_from_json(cJSON* tmp) {
    cJSON* vsm       = cJSON_GetObjectItem(tmp, "vsmStatus");
    cJSON* tmp1      = NULL;
    cJSON* tmp2      = NULL;
    cJSON* tmp3      = NULL;
    cJSON* tmp4      = NULL;
    cJSON* tmp5      = NULL;
    cJSON* tmp6      = NULL;
    cJSON* tmp7      = NULL;
    int    state     = 0;
    int    opt_state = 0;
    int    arry_size = 0;

    if (vsm) {
        char vsm_id_list[10240] = {0};
        arry_size               = cJSON_GetArraySize(vsm);
        for (int j = 0; j < arry_size; j++) {
            tmp1 = cJSON_GetArrayItem(vsm, j);
            tmp2 = cJSON_GetObjectItem(tmp1, "vsmId");
            tmp3 = cJSON_GetObjectItem(tmp1, "status");
            tmp4 = cJSON_GetObjectItem(tmp1, "clusterStatus");
            tmp5 = cJSON_GetObjectItem(tmp1, "optState");
            tmp6 = cJSON_GetObjectItem(tmp1, "Flavor");
            tmp7 = cJSON_GetObjectItem(tmp1, "ImageVer");

            if(tmp5)
            opt_state = tmp5->valueint;

            if (!strcmp(tmp3->valuestring, "normal")) {
                state     = 0;
            } else if (!strcmp(tmp3->valuestring, "initial")) {
                state     = 1;
            } else if (!strcmp(tmp3->valuestring, "error")) {
                state = 2;
            } else if (!strcmp(tmp3->valuestring, "shutdown")) {
                state     = 3;
            } else if (!strcmp(tmp3->valuestring, "restart")) {
                state = 4;
            }
            // 更新虚机信息,如果不存在则获取详细信息添加到数据库
            vsm_info_pull(tmp2->valuestring, g_ip, g_host_name);
            // 维护虚拟机操作状态与运行状态
            vsm_state_maintan(tmp2->valuestring, state, opt_state, (tmp4 == NULL) ? "" : tmp4->valuestring);
            cps_update_data_to_db(CPS_VSM_MANAGE_TABLE,"create_spec='%d',image_ver='%s' where vsm_id='%s'",tmp6->valueint,tmp7->valuestring,tmp2->valuestring);
            sprintf(vsm_id_list + strlen(vsm_id_list), "'%s',", (char*)tmp2->valuestring);
        }
        vsm_id_list[strlen(vsm_id_list) - 1] = '\0';
        cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip IN (SELECT vsm_ipv4 FROM v_vsm_with_ip_info WHERE host_ip='%s' and vsm_id NOT IN (%s) UNION SELECT vsm_ipv6 FROM v_vsm_with_ip_info WHERE host_ip='%s' and vsm_id NOT IN (%s))",g_ip,vsm_id_list,g_ip,vsm_id_list);
        cps_del_data_from_db(CPS_VSM_MANAGE_TABLE, "host_ip='%s' and vsm_id NOT IN (%s)",g_ip,vsm_id_list);
    }
    return arry_size;
}

int _interface_info_maintan(char* device_name, cJSON* brg_info) {
    int           ret       = 0;
    int           match_num = 0;
    int           interface_num = 0;
    int           i = 0, j = 0;
    int           array_size = 0;
    char*         sql        = NULL;
    sqlite3_stmt* stmt       = NULL;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    sql = sqlite3_mprintf("select interface_name,subnet,mask,gateway from interface_info where device_name=?;");
    ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sqlite prepare failed");
        goto out;
    }
    sqlite3_bind_text(stmt, 1, device_name, -1, SQLITE_STATIC);

    array_size = cJSON_GetArraySize(brg_info);
    while ((ret = sqlite3_step(stmt)) == SQLITE_ROW) {
        for (int i = 0; i < array_size; i++) {
            cJSON* tmp1 = cJSON_GetArrayItem(brg_info, i);
            cJSON* tmp2 = cJSON_GetObjectItem(tmp1, "name");
            cJSON* tmp3 = cJSON_GetObjectItem(tmp1, "ip");
            cJSON* tmp4 = cJSON_GetObjectItem(tmp1, "mask");
            cJSON* tmp5 = cJSON_GetObjectItem(tmp1, "subnet");
            cJSON* tmp6 = cJSON_GetObjectItem(tmp1, "gateway");

            char*                p       = strstr(tmp5->valuestring, "-");
            const unsigned char* if_name = sqlite3_column_text(stmt, 0);
            const unsigned char* subnet  = sqlite3_column_text(stmt, 1);
            const unsigned char* mask    = sqlite3_column_text(stmt, 2);
            const unsigned char* gateway = sqlite3_column_text(stmt, 3);
            if ((strcmp(if_name, tmp2->valuestring) == 0) && (strcmp(subnet, p + 1) == 0) &&
                (strcmp(mask, tmp4->valuestring) == 0) && (strcmp(gateway, tmp6->valuestring) == 0)) {
                match_num++;
                break;
            }
        }
        interface_num++;
    }
    ret = (ret == SQLITE_DONE) ? ERR_NONE : ret;
    API_CHECK_FUNC(ret, "sqlite3_step");

    if (match_num != array_size || interface_num!=array_size) {
        ret = cps_del_data_from_db(CPS_INTERFACE_INFO_TABLE, "device_name='%s'", device_name);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");

        for (j = 0; j < array_size; j++) {
            cJSON* tmp1 = cJSON_GetArrayItem(brg_info, j);
            cJSON* tmp2 = cJSON_GetObjectItem(tmp1, "name");
            cJSON* tmp3 = cJSON_GetObjectItem(tmp1, "ip");
            cJSON* tmp4 = cJSON_GetObjectItem(tmp1, "mask");
            cJSON* tmp5 = cJSON_GetObjectItem(tmp1, "subnet");
            cJSON* tmp6 = cJSON_GetObjectItem(tmp1, "gateway");

            char* p = strstr(tmp5->valuestring, "-");
            // 设置到数据库
            cps_insert_data_to_db(
                CPS_INTERFACE_INFO_TABLE,
                "(device_name,interface_name,subnet,mask,gateway) values ('%s','%s','%s','%s','%s')",
                device_name,
                tmp2->valuestring,
                p + 1,
                tmp4->valuestring,
                tmp6->valuestring);
        }
    }
out:
    if (sql) sqlite3_free(sql);
    if (stmt) sqlite3_reset(stmt);
    if (stmt) sqlite3_finalize(stmt);
    // close_cps_db();
    return ret;
}

int _shared_vsm_create_info_maintan(char* device_name, cJSON* share_info) {
    int ret       = 0;
    int match_num = 0;
    int i = 0, j = 0;
    int array_size = 0;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    int num = cps_get_num_from_db(CPS_SHARE_VSM_MAX_TABLE, "1");
    if (num == 0) {
        array_size = cJSON_GetArraySize(share_info);
        for (j = 0; j < array_size; j++) {
            cJSON* tmp1 = cJSON_GetArrayItem(share_info, j);
            cJSON* tmp2 = cJSON_GetObjectItem(tmp1, "cpu");
            cJSON* tmp3 = cJSON_GetObjectItem(tmp1, "created");
            cJSON* tmp4 = cJSON_GetObjectItem(tmp1, "create_max");

            // 设置到数据库
            cps_insert_data_to_db(
                CPS_SHARE_VSM_MAX_TABLE,
                "(host_name,cpu,created,create_max) values ('%s','%d','%d','%d')",
                device_name,
                tmp2->valueint,
                tmp3->valueint,
                tmp4->valueint);
        }
    }

out:
    // close_cps_db();
    return ret;
}

// 初始化全局哈希表（如果尚未初始化）
static void _ensure_hash_table_initialized() {
    if (!g_device_ovs_cache_table) {
        g_device_ovs_cache_table = hash_table_init(HASH_TABLE_SIZE);
        if (!g_device_ovs_cache_table) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to initialize global hash table");
        }
    }
}

// 查找设备的OVS缓存
DEVICE_OVS_CACHE_T* _find_device_ovs_cache(const char* device_name) {
    if (!device_name) {
        return NULL;
    }

    _ensure_hash_table_initialized();
    if (!g_device_ovs_cache_table) {
        return NULL;
    }

    return (DEVICE_OVS_CACHE_T*)hash_table_get(g_device_ovs_cache_table, device_name);
}

// 从数据库加载设备的OVS信息到缓存
int _load_ovs_cache_from_db(DEVICE_OVS_CACHE_T* device_cache) {
    char sql[512];
    sqlite3_stmt* stmt = NULL;
    int ret = 0;

    snprintf(sql, sizeof(sql), "SELECT ovs_instance, bind_device, ip_addess, ip_addessv6, work_mode, vlan_id_range, remark FROM %s WHERE device_name='%s'", CPS_OVS_INFO_TABLE, device_cache->device_name);

    ret = sqlite3_prepare_v2(cps_db, sql, -1, &stmt, NULL);
    if (ret != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to prepare SQL: %s", sqlite3_errmsg(cps_db));
        return -1;
    }

    while (sqlite3_step(stmt) == SQLITE_ROW) {
        OVS_CACHE_ITEM_T* cache_item = (OVS_CACHE_ITEM_T*)malloc(sizeof(OVS_CACHE_ITEM_T));
        if (!cache_item) continue;

        // 从数据库结果填充缓存项
        const char* ovs_instance = (const char*)sqlite3_column_text(stmt, 0);
        const char* bind_device = (const char*)sqlite3_column_text(stmt, 1);
        const char* ip_addess = (const char*)sqlite3_column_text(stmt, 2);
        const char* ip_addessv6 = (const char*)sqlite3_column_text(stmt, 3);
        int work_mode = sqlite3_column_int(stmt, 4);
        const char* vlan_id_range = (const char*)sqlite3_column_text(stmt, 5);
        const char* remark = (const char*)sqlite3_column_text(stmt, 6);

        strncpy(cache_item->ovs_instance, ovs_instance ? ovs_instance : "", sizeof(cache_item->ovs_instance) - 1);
        cache_item->ovs_instance[sizeof(cache_item->ovs_instance) - 1] = '\0';

        strncpy(cache_item->port_name, bind_device ? bind_device : "", sizeof(cache_item->port_name) - 1);
        cache_item->port_name[sizeof(cache_item->port_name) - 1] = '\0';

        strncpy(cache_item->ovs_ipv4, ip_addess ? ip_addess : "", sizeof(cache_item->ovs_ipv4) - 1);
        cache_item->ovs_ipv4[sizeof(cache_item->ovs_ipv4) - 1] = '\0';

        strncpy(cache_item->ovs_ipv6, ip_addessv6 ? ip_addessv6 : "", sizeof(cache_item->ovs_ipv6) - 1);
        cache_item->ovs_ipv6[sizeof(cache_item->ovs_ipv6) - 1] = '\0';

        cache_item->port_mode = work_mode;

        strncpy(cache_item->port_vlans, vlan_id_range ? vlan_id_range : "", sizeof(cache_item->port_vlans) - 1);
        cache_item->port_vlans[sizeof(cache_item->port_vlans) - 1] = '\0';

        // 从remark中解析ping_enable
        cache_item->ping_enable = 0;
        // 初始化标记字段
        cache_item->found_in_server = 1;  // 从数据库加载的项默认标记为找到

        // 添加到链表头部
        cache_item->next = device_cache->ovs_list;
        device_cache->ovs_list = cache_item;
    }

    sqlite3_finalize(stmt);
    return 0;
}

// 创建新的设备OVS缓存
DEVICE_OVS_CACHE_T* _create_device_ovs_cache(const char* device_name) {
    if (!device_name) {
        return NULL;
    }

    _ensure_hash_table_initialized();
    if (!g_device_ovs_cache_table) {
        return NULL;
    }

    DEVICE_OVS_CACHE_T* new_cache = (DEVICE_OVS_CACHE_T*)malloc(sizeof(DEVICE_OVS_CACHE_T));
    if (!new_cache) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to allocate memory for device cache");
        return NULL;
    }

    strncpy(new_cache->device_name, device_name, sizeof(new_cache->device_name) - 1);
    new_cache->device_name[sizeof(new_cache->device_name) - 1] = '\0';
    new_cache->ovs_list = NULL;
    new_cache->is_first_time = 1;

    // 从数据库加载现有数据到缓存
    _load_ovs_cache_from_db(new_cache);

    // 如果从数据库加载到了数据，则不是首次获取
    if (new_cache->ovs_list != NULL) {
        new_cache->is_first_time = 0;
    }

    // 将新缓存插入到哈希表中
    if (hash_table_insert(g_device_ovs_cache_table, device_name, new_cache) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert device cache into hash table");
        _free_ovs_cache_list(new_cache->ovs_list);
        free(new_cache);
        return NULL;
    }

    return new_cache;
}

// 删除设备OVS缓存
int _delete_device_ovs_cache(const char* device_name) {
    if (!device_name) {
        return -1;
    }

    _ensure_hash_table_initialized();
    if (!g_device_ovs_cache_table) {
        return -1;
    }

    // 获取缓存对象
    DEVICE_OVS_CACHE_T* cache = (DEVICE_OVS_CACHE_T*)hash_table_get(g_device_ovs_cache_table, device_name);
    if (cache) {
        // 释放OVS缓存项列表
        _free_ovs_cache_list(cache->ovs_list);
        free(cache);
    }

    // 从哈希表中删除
    return hash_table_delete(g_device_ovs_cache_table, device_name);
}

// 清理全局哈希表
void _cleanup_global_hash_table() {
    if (g_device_ovs_cache_table) {
        // 遍历哈希表，释放所有设备缓存
        for (size_t i = 0; i < g_device_ovs_cache_table->bucket_count; i++) {
            HASH_NODE_T* current = g_device_ovs_cache_table->buckets[i];
            while (current) {
                DEVICE_OVS_CACHE_T* cache = (DEVICE_OVS_CACHE_T*)current->value;
                if (cache) {
                    _free_ovs_cache_list(cache->ovs_list);
                    free(cache);
                }
                current = current->next;
            }
        }

        hash_table_destroy(g_device_ovs_cache_table);
        g_device_ovs_cache_table = NULL;
        DEBUG_CPS_CLI(COMMON_DEBUG, "Global hash table cleaned up");
    }
}

/**
 * @brief 清理不在设备列表中的OVS缓存
 * @return 成功返回0，失败返回-1
 */
int _cleanup_orphaned_ovs_cache() {
    if (!g_device_ovs_cache_table) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "Hash table not initialized, nothing to cleanup");
        return 0;
    }

    int removed_count = 0;

    // 遍历哈希表的所有桶
    for (size_t bucket_idx = 0; bucket_idx < g_device_ovs_cache_table->bucket_count; bucket_idx++) {
        HASH_NODE_T* current = g_device_ovs_cache_table->buckets[bucket_idx];
        HASH_NODE_T* prev = NULL;

        while (current) {
            int found = 0;
            const char* cache_device_name = current->key;

            // 在设备列表中查找当前缓存的设备名
            for (int i = 0; i < g_device_count; i++) {
                if (strcmp(cache_device_name, g_device_list[i].device_name) == 0) {
                    found = 1;
                    break;
                }
            }

            HASH_NODE_T* next = current->next;

            if (!found) {
                // 设备不在列表中，删除该缓存
                DEBUG_CPS_CLI(COMMON_DEBUG, "Removing orphaned OVS cache for device: %s", cache_device_name);

                // 释放缓存数据
                DEVICE_OVS_CACHE_T* cache = (DEVICE_OVS_CACHE_T*)current->value;
                if (cache) {
                    _free_ovs_cache_list(cache->ovs_list);
                    free(cache);
                }

                // 从链表中移除节点
                if (prev) {
                    prev->next = next;
                } else {
                    g_device_ovs_cache_table->buckets[bucket_idx] = next;
                }

                // 释放节点本身
                free(current->key);
                free(current);
                g_device_ovs_cache_table->size--;
                removed_count++;

                // 继续处理下一个节点
                current = next;
            } else {
                // 设备存在，继续下一个
                prev = current;
                current = next;
            }
        }
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "Cleaned up %d orphaned OVS cache entries", removed_count);
    return 0;
}

// 释放OVS缓存项列表
void _free_ovs_cache_list(OVS_CACHE_ITEM_T* list) {
    while (list) {
        OVS_CACHE_ITEM_T* temp = list;
        list = list->next;
        free(temp);
    }
}

// 查找OVS缓存项
OVS_CACHE_ITEM_T* _find_ovs_cache_item(OVS_CACHE_ITEM_T* list, const char* ovs_instance) {
    while (list) {
        if (strcmp(list->ovs_instance, ovs_instance) == 0) {
            return list;
        }
        list = list->next;
    }
    return NULL;
}

// 比较两个OVS缓存项是否相同（除了ovs_instance）
int _compare_ovs_cache_items(OVS_CACHE_ITEM_T* item1, cJSON* json_item) {
    cJSON* tmp_ovs_ipv4 = cJSON_GetObjectItem(json_item, "ovsIpv4");
    cJSON* tmp_ovs_ipv6 = cJSON_GetObjectItem(json_item, "ovsIpv6");
    cJSON* tmp_port_name = cJSON_GetObjectItem(json_item, "portName");
    cJSON* tmp_port_mode = cJSON_GetObjectItem(json_item, "portMode");
    cJSON* tmp_port_vlans = cJSON_GetObjectItem(json_item, "portVlans");
    cJSON* tmp_ping_enable = cJSON_GetObjectItem(json_item, "pingEnable");

    const char* ipv4 = (tmp_ovs_ipv4 && tmp_ovs_ipv4->valuestring) ? tmp_ovs_ipv4->valuestring : "";
    const char* ipv6 = (tmp_ovs_ipv6 && tmp_ovs_ipv6->valuestring) ? tmp_ovs_ipv6->valuestring : "";
    const char* port_name = (tmp_port_name && tmp_port_name->valuestring) ? tmp_port_name->valuestring : "";
    const char* port_vlans = (tmp_port_vlans && tmp_port_vlans->valuestring) ? tmp_port_vlans->valuestring : "";
    int port_mode = (tmp_port_mode) ? tmp_port_mode->valueint : 1;
    int ping_enable = (tmp_ping_enable) ? tmp_ping_enable->valueint : 0;

    return (strcmp(item1->ovs_ipv4, ipv4) == 0 &&
            strcmp(item1->ovs_ipv6, ipv6) == 0 &&
            strcmp(item1->port_name, port_name) == 0 &&
            strcmp(item1->port_vlans, port_vlans) == 0 &&
            item1->port_mode == port_mode &&
            item1->ping_enable == ping_enable);
}

// 从JSON填充缓存项
void _populate_cache_item_from_json(OVS_CACHE_ITEM_T* cache_item, cJSON* json_item) {
    cJSON* tmp_ovs_name = cJSON_GetObjectItem(json_item, "ovsInstance");
    cJSON* tmp_ovs_ipv4 = cJSON_GetObjectItem(json_item, "ovsIpv4");
    cJSON* tmp_ovs_ipv6 = cJSON_GetObjectItem(json_item, "ovsIpv6");
    cJSON* tmp_port_name = cJSON_GetObjectItem(json_item, "portName");
    cJSON* tmp_port_mode = cJSON_GetObjectItem(json_item, "portMode");
    cJSON* tmp_port_vlans = cJSON_GetObjectItem(json_item, "portVlans");
    cJSON* tmp_ping_enable = cJSON_GetObjectItem(json_item, "pingEnable");

    strncpy(cache_item->ovs_instance,
            (tmp_ovs_name && tmp_ovs_name->valuestring) ? tmp_ovs_name->valuestring : "",
            sizeof(cache_item->ovs_instance) - 1);
    cache_item->ovs_instance[sizeof(cache_item->ovs_instance) - 1] = '\0';

    strncpy(cache_item->ovs_ipv4,
            (tmp_ovs_ipv4 && tmp_ovs_ipv4->valuestring) ? tmp_ovs_ipv4->valuestring : "",
            sizeof(cache_item->ovs_ipv4) - 1);
    cache_item->ovs_ipv4[sizeof(cache_item->ovs_ipv4) - 1] = '\0';

    strncpy(cache_item->ovs_ipv6,
            (tmp_ovs_ipv6 && tmp_ovs_ipv6->valuestring) ? tmp_ovs_ipv6->valuestring : "",
            sizeof(cache_item->ovs_ipv6) - 1);
    cache_item->ovs_ipv6[sizeof(cache_item->ovs_ipv6) - 1] = '\0';

    strncpy(cache_item->port_name,
            (tmp_port_name && tmp_port_name->valuestring) ? tmp_port_name->valuestring : "",
            sizeof(cache_item->port_name) - 1);
    cache_item->port_name[sizeof(cache_item->port_name) - 1] = '\0';

    strncpy(cache_item->port_vlans,
            (tmp_port_vlans && tmp_port_vlans->valuestring) ? tmp_port_vlans->valuestring : "",
            sizeof(cache_item->port_vlans) - 1);
    cache_item->port_vlans[sizeof(cache_item->port_vlans) - 1] = '\0';

    cache_item->port_mode = (tmp_port_mode) ? tmp_port_mode->valueint : 1;
    cache_item->ping_enable = (tmp_ping_enable) ? tmp_ping_enable->valueint : 0;
    cache_item->found_in_server = 1;  // 标记为在服务器数据中找到
}

// 插入OVS信息到数据库
int _insert_ovs_info_to_db(const char* device_name, cJSON* json_item) {
    cJSON* tmp_ovs_name = cJSON_GetObjectItem(json_item, "ovsInstance");
    cJSON* tmp_ovs_ipv4 = cJSON_GetObjectItem(json_item, "ovsIpv4");
    cJSON* tmp_ovs_ipv6 = cJSON_GetObjectItem(json_item, "ovsIpv6");
    cJSON* tmp_port_name = cJSON_GetObjectItem(json_item, "portName");
    cJSON* tmp_port_mode = cJSON_GetObjectItem(json_item, "portMode");
    cJSON* tmp_port_vlans = cJSON_GetObjectItem(json_item, "portVlans");
    cJSON* tmp_ping_enable = cJSON_GetObjectItem(json_item, "pingEnable");

    return cps_insert_data_to_db(CPS_OVS_INFO_TABLE,
        "(device_name, ovs_instance, bind_device, ip_addess, ip_addessv6, work_mode, vlan_id_range) "
        "values ('%s', '%s', '%s', '%s', '%s', %d, '%s')",
        device_name,
        (tmp_ovs_name && tmp_ovs_name->valuestring) ? tmp_ovs_name->valuestring : "",
        (tmp_port_name && tmp_port_name->valuestring) ? tmp_port_name->valuestring : "",
        (tmp_ovs_ipv4 && tmp_ovs_ipv4->valuestring) ? tmp_ovs_ipv4->valuestring : "",
        (tmp_ovs_ipv6 && tmp_ovs_ipv6->valuestring) ? tmp_ovs_ipv6->valuestring : "",
        (tmp_port_mode) ? tmp_port_mode->valueint : 1,
        (tmp_port_vlans && tmp_port_vlans->valuestring) ? tmp_port_vlans->valuestring : ""
    );
}

int _ovs_info_maintan(char* device_name, cJSON* ovs_info) {
    int ret = 0;
    int array_size = 0;
    int j = 0;

    if (device_name == NULL || ovs_info == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid parameters for _ovs_info_maintan");
        return -1;
    }

    // 清理不在设备列表中的孤立OVS缓存
    _cleanup_orphaned_ovs_cache();
    // 查找或创建设备的OVS缓存
    DEVICE_OVS_CACHE_T* device_cache = _find_device_ovs_cache(device_name);
    if (!device_cache) {
        device_cache = _create_device_ovs_cache(device_name);
        if (!device_cache) {
            DEBUG_CPS_CLI(ERR_DEBUG, "Failed to create device ovs cache for: %s", device_name);
            ret = -1;
            goto out;
        }
    }

    array_size = cJSON_GetArraySize(ovs_info);

    // 首次获取逻辑
    if (device_cache->is_first_time) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "First time getting ovs_info for device: %s", device_name);

        // 删除数据库中对应device_name的所有ovs_info记录
        ret = cps_del_data_from_db(CPS_OVS_INFO_TABLE, "device_name='%s'", device_name);
        API_CHECK_FUNC(ret, "cps_del_data_from_db");

        // 插入新数据并建立内存缓存
        for (j = 0; j < array_size; j++) {
            cJSON* tmp_ovs = cJSON_GetArrayItem(ovs_info, j);
            if (tmp_ovs == NULL) continue;

            cJSON* tmp_ovs_name = cJSON_GetObjectItem(tmp_ovs, "ovsInstance");
            if (!tmp_ovs_name || !tmp_ovs_name->valuestring) continue;

            // 插入数据库
            ret = _insert_ovs_info_to_db(device_name, tmp_ovs);
            if (ret != 0) {
                DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert ovs_info for device: %s", device_name);
                continue;
            }

            // 添加到内存缓存
            OVS_CACHE_ITEM_T* cache_item = (OVS_CACHE_ITEM_T*)malloc(sizeof(OVS_CACHE_ITEM_T));
            if (cache_item) {
                _populate_cache_item_from_json(cache_item, tmp_ovs);
                cache_item->next = device_cache->ovs_list;
                device_cache->ovs_list = cache_item;
            }
        }

        device_cache->is_first_time = 0;
    } else {
        // 后续获取逻辑 - 增量更新
        DEBUG_CPS_CLI(COMMON_DEBUG, "Incremental update ovs_info for device: %s", device_name);
        ret = _incremental_update_ovs_info(device_cache, ovs_info, device_name);
    }

out:
    // close_cps_db();
    return ret;
}

// 增量更新OVS信息
int _incremental_update_ovs_info(DEVICE_OVS_CACHE_T* device_cache, cJSON* ovs_info, const char* device_name) {
    int ret = 0;
    int array_size = cJSON_GetArraySize(ovs_info);
    int j = 0;

    // 标记缓存中的项，用于检测删除
    OVS_CACHE_ITEM_T* cache_item = device_cache->ovs_list;
    while (cache_item) {
        cache_item->found_in_server = 0; // 标记为未在服务器数据中找到
        cache_item = cache_item->next;
    }

    // 处理服务器数据：新增和更新
    for (j = 0; j < array_size; j++) {
        cJSON* tmp_ovs = cJSON_GetArrayItem(ovs_info, j);
        if (tmp_ovs == NULL) continue;

        cJSON* tmp_ovs_name = cJSON_GetObjectItem(tmp_ovs, "ovsInstance");
        if (!tmp_ovs_name || !tmp_ovs_name->valuestring) continue;

        const char* ovs_instance = tmp_ovs_name->valuestring;
        OVS_CACHE_ITEM_T* existing_item = _find_ovs_cache_item(device_cache->ovs_list, ovs_instance);

        if (existing_item) {
            // 标记为在服务器数据中找到
            existing_item->found_in_server = 1;

            // 检查是否需要更新
            if (!_compare_ovs_cache_items(existing_item, tmp_ovs)) {
                DEBUG_CPS_CLI(COMMON_DEBUG, "Updating ovs_instance: %s", ovs_instance);

                // 更新数据库
                ret = cps_update_data_to_db(CPS_OVS_INFO_TABLE,
                    "bind_device='%s', ip_addess='%s', ip_addessv6='%s', work_mode=%d, vlan_id_range='%s' "
                    "where device_name='%s' and ovs_instance='%s'",
                    (cJSON_GetObjectItem(tmp_ovs, "portName") && cJSON_GetObjectItem(tmp_ovs, "portName")->valuestring) ?
                        cJSON_GetObjectItem(tmp_ovs, "portName")->valuestring : "",
                    (cJSON_GetObjectItem(tmp_ovs, "ovsIpv4") && cJSON_GetObjectItem(tmp_ovs, "ovsIpv4")->valuestring) ?
                        cJSON_GetObjectItem(tmp_ovs, "ovsIpv4")->valuestring : "",
                    (cJSON_GetObjectItem(tmp_ovs, "ovsIpv6") && cJSON_GetObjectItem(tmp_ovs, "ovsIpv6")->valuestring) ?
                        cJSON_GetObjectItem(tmp_ovs, "ovsIpv6")->valuestring : "",
                    (cJSON_GetObjectItem(tmp_ovs, "portMode")) ? cJSON_GetObjectItem(tmp_ovs, "portMode")->valueint : 1,
                    (cJSON_GetObjectItem(tmp_ovs, "portVlans") && cJSON_GetObjectItem(tmp_ovs, "portVlans")->valuestring) ?
                        cJSON_GetObjectItem(tmp_ovs, "portVlans")->valuestring : "",
                    device_name,
                    ovs_instance
                );

                if (ret == 0) {
                    // 更新缓存
                    _populate_cache_item_from_json(existing_item, tmp_ovs);
                } else {
                    DEBUG_CPS_CLI(ERR_DEBUG, "Failed to update ovs_info for %s", ovs_instance);
                }
            }
        } else {
            // 新增项
            DEBUG_CPS_CLI(COMMON_DEBUG, "Adding new ovs_instance: %s", ovs_instance);

            // 插入数据库
            ret = _insert_ovs_info_to_db(device_name, tmp_ovs);
            if (ret == 0) {
                // 添加到缓存
                OVS_CACHE_ITEM_T* new_item = (OVS_CACHE_ITEM_T*)malloc(sizeof(OVS_CACHE_ITEM_T));
                if (new_item) {
                    _populate_cache_item_from_json(new_item, tmp_ovs);
                    new_item->next = device_cache->ovs_list;
                    device_cache->ovs_list = new_item;
                }
            } else {
                DEBUG_CPS_CLI(ERR_DEBUG, "Failed to insert new ovs_info for %s", ovs_instance);
            }
        }
    }

    // 处理删除：移除服务器数据中不存在的项
    OVS_CACHE_ITEM_T* prev = NULL;
    cache_item = device_cache->ovs_list;
    while (cache_item) {
        if (cache_item->found_in_server == 0) {
            // 这个项在服务器数据中不存在，需要删除
            DEBUG_CPS_CLI(COMMON_DEBUG, "Deleting ovs_instance: %s", cache_item->ovs_instance);

            // 从数据库删除
            ret = cps_del_data_from_db(CPS_OVS_INFO_TABLE, "device_name='%s' and ovs_instance='%s'",
                                       device_name, cache_item->ovs_instance);

            // 从缓存中移除
            if (prev) {
                prev->next = cache_item->next;
            } else {
                device_cache->ovs_list = cache_item->next;
            }

            OVS_CACHE_ITEM_T* temp = cache_item;
            cache_item = cache_item->next;
            free(temp);
        } else {
            prev = cache_item;
            cache_item = cache_item->next;
        }
    }

    return ret;
}

int _get_device_cb(char* str, char* request_id) {
    int           ret = 0;
    DEVICE_INFO_T device_info;
    memset(&device_info, '\0', sizeof(DEVICE_INFO_T));
    device_info.ip = g_ip;
    printf("---------- %s\n",str);
    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }
    cJSON* tmp     = NULL;
    cJSON* tmp_res = NULL;
    cJSON* tmp_ext = NULL;
    tmp            = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret                       = tmp->valueint;
            device_info.device_status = 2;
            db_set_device(device_info);
            cps_update_data_to_db(CPS_VSM_MANAGE_TABLE, "run_state=5 where host_ip=\'%s\'", g_ip);
            fw_log_write(2, LOG_WARNING, "dsp_msg=\"%s %s\"", gettext("device disconnected,ip is"), g_ip);
            goto out;
        }
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

    tmp = cJSON_GetObjectItem(root, "result");
    if (tmp) {
        tmp_res = cJSON_GetObjectItem(tmp, "chsmStatus");
        if (!strcmp(tmp_res->valuestring, "ok")) {
            device_info.device_status = 1;
        } else {
            device_info.device_status = 0;
        }

        if (device_info.device_status) {
            tmp_ext = cJSON_GetObjectItem(tmp, "extensions");
            if (tmp_ext) {
                tmp_res = cJSON_GetObjectItem(tmp_ext, "cpuUsage");
                if (tmp_res) {
                    device_info.cpu_usage = atof(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "menUsage");
                if (tmp_res) {
                    device_info.memory_usage = atof(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "diskUsage");
                if (tmp_res) {
                    device_info.disk_usage = atof(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "remainCpu");
                if (tmp_res) {
                    device_info.remain_cpu = atoi(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "remainMen");
                if (tmp_res) {
                    device_info.remain_mem = atoi(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "remainDisk");
                if (tmp_res) {
                    device_info.remain_disk = atoi(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "totalCpu");
                if (tmp_res) {
                    device_info.cpu_cores = atoi(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "totalMen");
                if (tmp_res) {
                    device_info.memory_size = atoi(tmp_res->valuestring);
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "totalDisk");
                if (tmp_res) {
                    device_info.disk_size = atoi(tmp_res->valuestring);
                }
                char device_name[64] = {0};
                // 维护桥口信息
                tmp_res = cJSON_GetObjectItem(tmp_ext, "brigeInfo");
                if (tmp_res) {
                    ret                  = cps_get_data_from_db(
                        CPS_DEV_STATUS_TABLE, "ip", (void*)g_ip, "device_name", device_name, 64, false);
                    API_CHECK_FUNC(ret, "cps_get_data_from_db");

                    ret = _interface_info_maintan(device_name, tmp_res);
                    API_CHECK_FUNC(ret, "_interface_info_maintan");
                }

                tmp_res = cJSON_GetObjectItem(tmp_ext, "shareCreateInfo");
                if (tmp_res) {
                    ret = _shared_vsm_create_info_maintan(g_host_name, tmp_res);
                    API_CHECK_FUNC(ret, "_shared_vsm_create_info_maintan");
                }
                // 处理ovsInfo节点
                tmp_res = cJSON_GetObjectItem(tmp_ext, "ovsInfo");
                if (tmp_res) {
                    ret = _ovs_info_maintan(device_name, tmp_res);
                    API_CHECK_FUNC(ret, "_ovs_info_maintan");
                }

                // 获取虚机资源分配级别
                if (min_flavor_cpu == 0) {
                    _get_flavor_info_from_json(tmp_ext);
                }

                // 设置虚机详细信息
                device_info.create_vsm_num = _get_vsm_info_from_json(tmp_ext);

                // 更新设备详细信息
                device_info.max_vsm_num =
                    fmin(device_info.memory_size / min_flavor_mem, (device_info.cpu_cores * 4) / min_flavor_cpu);

                tmp_res = cJSON_GetObjectItem(tmp_ext, "vsmCapability");
                if(tmp_res)
                cps_update_data_to_db(CPS_IMAGE_MAP_TABLE,"used_flag=1 where image_id in (%s)", tmp_res->valuestring);
            }
        }
        db_set_device(device_info);
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

out:
    _Close_DbFile();
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

int _get_authpuk_cb(char* str, char* request_id) {
    int ret = 0;

    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }

    cJSON* tmp = NULL;
    tmp        = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret = tmp->valueint;
            goto out;
        }
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

    tmp = cJSON_GetObjectItem(root, "result");
    if (tmp) {
        int    arry_size = cJSON_GetArraySize(tmp);
        cJSON* arry      = cJSON_GetObjectItem(tmp, "fingerprints");
        if (arry && arry_size != 0) {
            // 暂时默认只有一个公钥
            tmp = cJSON_GetArrayItem(arry, 0);
            // 公钥指纹保存入数据库
            db_set_pub_key_fingp(g_ip, tmp->valuestring);
        }
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

out:
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

int _reset_authpuk_cb(char* str, char* request_id) {
    int ret = 0;

    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }

    cJSON* tmp = NULL;
    tmp        = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret = tmp->valueint;
            goto out;
        }
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

out:
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

int _restore_cps_info_cb(char* str, char* request_id) {
    int ret = 0;

    // 对数据解码
    cJSON* root = cJSON_Parse(str);
    if (root == NULL) {
        DEBUG_CPS_CLI(ERR_DEBUG, "cJSON_Parse failed!");
        ret = -1;
        goto out;
    }

    cJSON* tmp = NULL;
    tmp        = cJSON_GetObjectItem(root, "status");
    if (tmp != NULL) {
        if (tmp->valueint != 200) {
            ret = tmp->valueint;
            goto out;
        }
    } else {
        ret = RET_RESULT_NULL;
        goto out;
    }

out:
    if (root) {
        cJSON_Delete(root);
    }

    return ret;
}

int _get_authpuk(char* ip) {
    msg_header_t* msg_header;
    int           ret                 = 0;
    char          request_id[9]       = {0};
    int           request_id_len      = 8;
    char          body_data[BODY_LEN] = {0};
    int           body_len            = BODY_LEN;
    char          request_data[128]   = {0};
    g_ip                              = ip;

    msg_header = (msg_header_t*)malloc(BODY_LEN);
    memset(msg_header, '\0', BODY_LEN);
    ret = cps_generate_random(request_id, request_id_len);
    API_CHECK_FUNC(ret, "cps_generate_random faild!");
    char* json_str = NULL;

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);

    json_str = cJSON_PrintUnformatted(root);
    base64_encode(json_str, request_data, strlen(json_str));

    ret = cps_msg_body_comp_spcial(ip, request_id, request_data, strlen(request_data), body_data, &body_len);
    API_CHECK_FUNC(ret, "cps_msg_body_comp faild!");

    cps_request_init(msg_header, body_data, body_len, PUBKEY_MAIN_TYPE, CMD_PUBKEY_GET);
    ret = cps_request_send(msg_header, _get_authpuk_cb, request_id);
    API_CHECK_FUNC(ret, "cps_request_send faild!");

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (msg_header) {
        free(msg_header);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

int _set_authpuk(char* ip) {
    msg_header_t* msg_header;
    int           ret                 = 0;
    char          request_id[9]       = {0};
    int           request_id_len      = 8;
    char          body_data[BODY_LEN] = {0};
    int           body_len            = BODY_LEN;
    char          request_data[128]   = {0};
    g_ip                              = ip;
    msg_header                        = (msg_header_t*)malloc(BODY_LEN);
    memset(msg_header, '\0', BODY_LEN);

    char  out_key[128] = {0};
    char* json_str     = NULL;

    ret = cps_generate_random(request_id, request_id_len);
    API_CHECK_FUNC(ret, "cps_generate_random faild!");

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    // 获取公钥信息
    ret = get_sign_pub_key(out_key);
    API_CHECK_FUNC(ret, "get_sign_pub_key faild!");

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "algorithm", "sm2");
    cJSON* arry = cJSON_AddArrayToObject(root, "pks");
    cJSON* pub  = cJSON_CreateString(out_key);
    cJSON_AddItemToArray(arry, pub);

    json_str = cJSON_PrintUnformatted(root);
    base64_encode(json_str, request_data, strlen(json_str));

    ret = cps_msg_body_comp_spcial(ip, request_id, request_data, strlen(request_data), body_data, &body_len);
    API_CHECK_FUNC(ret, "cps_msg_body_comp faild!");

    cps_request_init(msg_header, body_data, body_len, PUBKEY_MAIN_TYPE, CMD_PUBKEY_SET);
    ret = cps_request_send(msg_header, callback_status_check, request_id);
    API_CHECK_FUNC(ret, "cps_request_send faild!");

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (msg_header) {
        free(msg_header);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

int _reset_authpuk(char* ip) {
    msg_header_t* msg_header;
    int           ret                 = 0;
    char          request_id[9]       = {0};
    int           request_id_len      = 8;
    char          body_data[BODY_LEN] = {0};
    int           body_len            = BODY_LEN;
    char          request_data[128]   = {0};
    g_ip                              = ip;
    char signature1[128]              = {0};
    int  signature1_len               = 0;

    msg_header = (msg_header_t*)malloc(BODY_LEN);
    memset(msg_header, '\0', BODY_LEN);
    ret = cps_generate_random(request_id, request_id_len);
    API_CHECK_FUNC(ret, "cps_generate_random faild!");
    char* json_str = NULL;

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);
    cJSON_AddStringToObject(root, "oprType", "resetpuk");

    json_str = cJSON_PrintUnformatted(root);
    ret      = sign_with_internal_key(json_str, strlen(json_str), signature1, &signature1_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key!");

    base64_encode(json_str, request_data, strlen(json_str));

    ret = cps_msg_body_comp(ip, request_id, signature1, request_data, strlen(request_data), body_data, &body_len);
    API_CHECK_FUNC(ret, "cps_msg_body_comp faild!");

    cps_request_init(msg_header, body_data, body_len, CHSM_MAIN_TYPE, CMD_RESET);
    ret = cps_request_send(msg_header, _reset_authpuk_cb, request_id);
    API_CHECK_FUNC(ret, "cps_request_send faild!");

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (msg_header) {
        free(msg_header);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}
/* ======================================================================================
 * implementation
 */
int cps_init(int argc, char* argv[]) {
    int ret = 0;
    // 判断是否有设备存在
    if (cps_get_num_from_db(CPS_DEV_STATUS_TABLE, "ip!=''")) {
        printf(gettext("need delete device!"));
        return -1;
    }

    // 判断是否有租户存在
    if (cps_get_num_from_db(CPS_TENANT_INFO_TABLE, "account!=''")) {
        printf(gettext("need delete tenant!"));
        return -1;
    }

    //  删除密钥信息信息
    Db_DeleteAllKey();

    // 清空文件密钥信息
    ret = Db_GetDeviceSupport(DeviceStoreCapbalities, MAX_KEY_TYPE_SIZE);
    if (ret != 0) {
        printf(gettext("device init device file failed!"));
        return ret;
    }

    ret = InitDeviceFile(DeviceStoreCapbalities, MAX_KEY_TYPE_SIZE, 1, 1);
    if (ret != 0) {
        printf(gettext("device init device file failed!"));
        return ret;
    }

    ret = Db_GetManagerSupport(ManagerStoreCapbalities, MAX_KEY_TYPE_SIZE);
    if (ret != 0) {
        printf(gettext("device init usr file failed!"));
        return ret;
    }

    ret = InitUsrFile(ManagerStoreCapbalities, MAX_KEY_TYPE_SIZE, 1, 1);
    if (ret != 0) {
        printf(gettext("device init usr file failed!"));
        return ret;
    }

    ret = _device_init();
    if (ret) {
        printf(gettext("device init failed!"));
        return ret;
    }

    // 设置设备状态
    ret = db_set_device_init_status(1);
    if (ret) {
        printf(gettext("device init failed!"));
        return ret;
    }

    system("killall containerController");
    return ret;
}

int cps_get_status(int argc, char* argv[]) {
    return db_get_device_init_status();
}

int cps_clean(int argc, char* argv[]) {
    // 判断是否有设备存在
    if (cps_get_num_from_db(CPS_DEV_STATUS_TABLE, "ip!=''")) {
        printf(gettext("need delete device!"));
        return -1;
    }

    // 判断是否有租户存在
    if (cps_get_num_from_db(CPS_TENANT_INFO_TABLE, "account!=''")) {
        printf(gettext("need delete tenant!"));
        return -1;
    }

    // 设置设备状态
    int ret = db_set_device_init_status(0);
    if (ret) {
        printf(gettext("device clean failed!"));
        return ret;
    }

    // 删除数据库信息
    Db_DeleteAllKey();

    // 删除文件
    system("rm /usr/local/conf/local_info/.usr_store > /dev/null 2>&1 &");
    system("rm /usr/local/conf/local_info/.device_store > /dev/null 2>&1 &");
    system("rm /usr/local/conf/local_info/.usr_file_dir/* -rf > /dev/null 2>&1 &");

    system("backpkg config >/dev/null 2>&1 &");
    return 0;
}

int cps_self_check(int argc, char* argv[]) {
    int    ret    = -1;
    int    rc     = 0;
    char*  errmsg = NULL;
    char** result = NULL;
    int    row, col;
    int    check_flag        = 1;
    char   check_result[256] = {0};

    // 1.获取宿主机列表,如果无宿主机,自检通过
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    rc =
        sqlite3_get_table_printf(cps_db, "select device_name,ip from device_status_info", &result, &row, &col, &errmsg);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "select failed!");
        goto out;
    }

    if (row > 0) {
        // 2.调用获取接口检测是否通过
        for (int j = 1; j <= row; j++) {
            ret = _get_authpuk(result[col * j + 1]);
            if (ret) {
                check_flag = 0;
                sprintf(check_result + strlen(check_result), "%s:0", result[col * j]);
                // 记录未通过的设备
                fw_log_write(2, LOG_ERR, "dsp_msg=\"%s\"", gettext("self check failed!"));
            } else {
                fw_log_write(2, LOG_INFO, "dsp_msg=\"%s\"", gettext("self check successful!"));
            }
        }
    }

    rc = sqlite3_exec_printf(
        cps_db,
        "UPDATE device_self_check SET api_check_status='%d',api_check_result='%s'",
        0,
        0,
        &errmsg,
        check_flag,
        check_result);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "update failed!");
        goto out;
    }

    ret = 0;
out:
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }

    // close_cps_db();
    return ret;
}

/* 定义常量 */
#define REQUEST_ID_LEN      8
#define REQUEST_DATA_SIZE   512
#define SIGNATURE_SIZE      128

/**
 * @brief 获取指定设备所有详细信息
 *
 * @param ip 设备IP地址
 * @param host_name 主机名
 * @return int 成功返回0，失败返回错误码
 */
int get_device_all_status(char* ip, char* host_name) {
    /* 栈上分配消息头和body缓冲区 */
    char          msg_buffer[sizeof(msg_header_t) + BODY_LEN];
    msg_header_t* msg_header        = (msg_header_t*)msg_buffer;
    cJSON*        root              = NULL;
    char*         json_str          = NULL;
    int           ret               = 0;
    char          request_id[REQUEST_ID_LEN + 1] = {0};
    char          body_data[BODY_LEN] = {0};
    int           body_len          = BODY_LEN;
    char          request_data[REQUEST_DATA_SIZE] = {0};
    char          signature[SIGNATURE_SIZE] = {0};
    int           signature_len     = 0;

    /* 输入参数验证 */
    if (!ip || !host_name) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Invalid input parameters: ip or host_name is NULL");
        return -1;
    }

    /* 设置全局变量 */
    g_ip = ip;
    g_host_name = host_name;

    /* 初始化消息头缓冲区 */
    memset(msg_buffer, 0, sizeof(msg_buffer));

    /* 生成请求ID */
    ret = cps_generate_random(request_id, REQUEST_ID_LEN);
    API_CHECK_FUNC(ret, "cps_generate_random failed!");

    /* 创建JSON对象 */
    root = cJSON_CreateObject();
    if (!root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to create JSON object");
        ret = -1;
        goto out;
    }

    /* 添加请求ID到JSON */
    if (!cJSON_AddStringToObject(root, "requestId", request_id)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to add requestId to JSON");
        ret = -1;
        goto out;
    }

    /* 生成JSON字符串 */
    json_str = cJSON_PrintUnformatted(root);
    if (!json_str) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to generate JSON string");
        ret = -1;
        goto out;
    }

    /* 签名 */
    ret = sign_with_internal_key(json_str, strlen(json_str), signature, &signature_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key failed!");

    /* Base64编码 */
    base64_encode(json_str, request_data, strlen(json_str));

    /* 组装消息体 */
    ret = cps_msg_body_comp(ip, request_id, signature, request_data, 
                           strlen(request_data), body_data, &body_len);
    API_CHECK_FUNC(ret, "cps_msg_body_comp failed!");
    /* 初始化请求 */
    cps_request_init(msg_header, body_data, body_len, CHSM_MAIN_TYPE, CMD_ALLSTATUS);
    /* 发送请求 */
    ret = cps_request_send(msg_header, _get_device_cb, request_id);
    API_CHECK_FUNC(ret, "cps_request_send failed!");

out:
    /* 资源清理 */
    if (root) {
        cJSON_Delete(root);
    }
    if (json_str) {
        free(json_str);
    }
    
    return ret;
}

/**
 * @brief 获取指定设备所有详细信息
 *
 * @return int
 */
int restore_cps_info(char* ip) {
    msg_header_t* msg_header;
    int           ret                 = 0;
    char          request_id[9]       = {0};
    int           request_id_len      = 8;
    char          body_data[BODY_LEN] = {0};
    int           body_len            = BODY_LEN;
    char          request_data[128]   = {0};
    g_ip                              = ip;

    msg_header = (msg_header_t*)malloc(BODY_LEN);
    memset(msg_header, '\0', BODY_LEN);
    ret = cps_generate_random(request_id, request_id_len);
    API_CHECK_FUNC(ret, "cps_generate_random faild!");
    char* json_str = NULL;

    cJSON* root = cJSON_CreateObject();
    if (NULL == root) {
        DEBUG_CPS_CLI(ERR_DEBUG, "create json object failed!");
        goto out;
    }

    cJSON_AddStringToObject(root, "requestId", request_id);

    json_str = cJSON_PrintUnformatted(root);
    base64_encode(json_str, request_data, strlen(json_str));

    ret = cps_msg_body_comp_spcial(ip, request_id, request_data, strlen(request_data), body_data, &body_len);
    API_CHECK_FUNC(ret, "cps_msg_body_comp faild!");

    cps_request_init(msg_header, body_data, body_len, CHSM_MAIN_TYPE, CMD_DATA_RESTORE);
    ret = cps_request_send(msg_header, _restore_cps_info_cb, request_id);
    API_CHECK_FUNC(ret, "cps_request_send faild!");

out:
    if (root) {
        cJSON_Delete(root);
    }

    if (msg_header) {
        free(msg_header);
    }

    if (json_str) {
        free(json_str);
    }
    return ret;
}

/**
 * @brief 纳管宿主机
 *
 * @param argc
 * @param argv
 * @return int
 */
int cps_device_add(int argc, char* argv[]) {
    DEVICE_INFO_T device_info;
    memset(&device_info, '\0', sizeof(DEVICE_INFO_T));
    device_info.device_name = argv[4];
    device_info.device_type = atoi(argv[6]);
    device_info.ip          = argv[8];

    if (argc == 11) {
        device_info.remark = argv[10];
    }
    int ret = 0;

    // 查看是否有同名或者同ip设备
    ret = db_check_device_ip_and_name(device_info.device_name, device_info.ip);
    if (ret) {
        printf(gettext("having the same name or ip!"));
        return ret;
    }

    // 先把数据写入数据库
    db_add_device(device_info);

    // 调用接口获取指纹
    ret = _get_authpuk(device_info.ip);
    if (ret == RET_RESULT_NULL) {
        // 设备未被纳管,配置云平台公钥
        ret = _set_authpuk(device_info.ip);
        if (ret) {
            printf(gettext("add device faild!"));
        } else {
            ret = _get_authpuk(device_info.ip);
            if (ret) {
                printf(gettext("add device faild!"));
            }
        }
    } else if (ret == ERR_SRV_CONNECT || ret == 2) {
        printf(gettext("device dose not exist,ip:%s!"), device_info.ip);
        ret = -1;
    } else if (ret == RET_OK) {
        ret = restore_cps_info(device_info.ip);
        if (ret) {
            printf(gettext("restore device info faild!!"));
            ret = -1;
        }
        // printf(gettext("the device has been add by other cps!"));
        // ret = -1;
    } else {
        printf(gettext("add device faild!"));
        ret = -1;
    }

    if (ret) {
        // 删除数据库
        db_delete_device(device_info.device_name);
    } else {
        // 调用接口获取设备信息写入数据库
        ret = get_device_all_status(device_info.ip, device_info.device_name);
        if (ret) {
            printf(gettext("get device info faild!"));
        }

        // 查看影像上传地址是否设置,没有设置就调用接口设置影像上传地址
        char ima_addr[16] = {0};
        memset(ima_addr, '\0', 16);
        ret = cps_get_image_addr(ima_addr, 16);
        if (!ret) {
            if (strstr(ima_addr, ".") != NULL || strstr(ima_addr, ":") != NULL) {
                extern int cps_img_addr_set_at_add_device(char* ip);
                if (strlen(ima_addr) != 0) cps_img_addr_set_at_add_device(ima_addr);
            }
        } else {
            ret = 0;
        }

        extern int cps_cb_addr_check_at_add_device(void);
        if (cps_cb_addr_check_at_add_device()) {
            printf(gettext("device added successfully,invalid callback addr,please modify it!"));
            return -2;
        }
    }

    return ret;
}

int cps_device_del(int argc, char* argv[]) {
    int   ret    = 0;
    char* name   = argv[4];
    char  ip[32] = {0};

    // ret = cps_get_num_from_db(CPS_WORK_ORDER_TABLE, "host_name='%s' and approval_status=1", name);
    // if (ret) {
    //     printf(gettext("being used,can not delete!"));
    //     return ret;
    // }

    // 获取设备ip
    ret = cps_get_data_from_db(CPS_DEV_STATUS_TABLE, "device_name", (void*)name, "ip", ip, 32, false);
    if (ret) {
        printf(gettext("delete device faild!"));
    }

    ret = vsm_del_from_device(name);
    if (ret) {
        DEBUG_CPS_CLI(ERR_DEBUG, "vsm_del_from_device faild!");
        print_errmsg(ret);
        goto end;
    }

    ret = _reset_authpuk(ip);
    if (ret) {
        if (ret != 403) {
            printf(gettext("delete device faild!"));
            DEBUG_CPS_CLI(ERR_DEBUG, "reset public key faild!");
            goto end;
        }
    }
    ret = db_delete_device(name);
    if (ret) {
        printf(gettext("delete device faild!"));
    }
end:
    return ret;
}

int cps_get_sign_pub_key(int argc, char* argv[]) {
    int  ret           = -1;
    char sign_pub[65]  = {0};
    char base_pub[128] = {0};

    ret = get_sign_pub_key_without_base64(sign_pub);
    API_CHECK_FUNC(ret, "get_sign_pub_key_without_base64");
    sign_pub[0] = 4;
    base64_encode(sign_pub, base_pub, 65);
    printf("%s\n", base_pub);
out:
    return ret;
}

int cps_get_sign_pri_key(int argc, char* argv[]) {
    int  ret           = -1;
    char pri[32]       = {0};
    char base_pri[128] = {0};

    ret = get_sign_pri_key(pri);
    API_CHECK_FUNC(ret, "get_sign_pri_key");

    base64_encode(pri, base_pri, 32);
    printf("%s\n", base_pri);
out:
    return ret;
}
