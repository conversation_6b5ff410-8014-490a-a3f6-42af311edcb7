/*
 * Project: base
 * Moudle: cps
 * File: cps_tenant.c
 * Created Date: 2023-08-29 14:56:55
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include "cps_cluster_manage.h"
#include "cps_common.h"
#include "cps_image_manage.h"
#include "cps_srv_manage.h"
#include "cps_tenant.h"
#include "cps_vsm_manage.h"
#include "database_api.h"

/* ======================================================================================
 * macros
 */
#define SYSTEM_CONFIG_DB_FILE "/usr/local/conf/configs.db"
/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/* ======================================================================================
 * implementation
 */
int cps_tenant_info_del_all(char* tenant_name) {
    int ret = ERR_NONE;

    if (tenant_name == NULL) return ERR_PARAM;

    // 删除虚拟机
    ret = vsm_del_all(tenant_name);
    API_CHECK_FUNC(ret, "vsm_del_all");

    // 删除影像记录及影像文件
    ret = image_del_all(tenant_name);
    API_CHECK_FUNC(ret, "image_del_all");

    // 删除集群
    ret = cluster_del_all(tenant_name);
    API_CHECK_FUNC(ret, "cluster_del_all");

    // 删除服务
    ret = srv_del_all(tenant_name);
    API_CHECK_FUNC(ret, "srv_del_all");
out:
    return ret;
}

int cps_tenant_add(int argc, char* argv[]) {
    int           ret      = 0;
    int           rc       = 0;
    char*         errmsg   = NULL;
    sqlite3*      Db_Store = NULL;
    TENANT_INFO_T tenant;
    memset(&tenant, '\0', sizeof(TENANT_INFO_T));
    tenant.name       = argv[4];
    tenant.account    = argv[6];
    tenant.password   = argv[8];
    tenant.valid_time = atol(argv[10]);

    tenant.corporate_name = argv[12];

    if (!strcmp(argv[13], "telephone")) {
        tenant.telephone    = argv[14];
        tenant.phone_number = argv[16];
        tenant.mail         = argv[18];
        if (argc == 21) {
            tenant.remark = argv[20];
        } else {
            tenant.remark = "";
        }
    } else {
        tenant.phone_number = argv[14];
        tenant.mail         = argv[16];
        if (argc == 19) {
            tenant.remark = argv[18];
        } else {
            tenant.remark = "";
        }
    }

    char system_str[256] = {0};

    // 重名校验
    ret = cps_get_num_from_db(CPS_TENANT_INFO_TABLE, "name='%s' or account='%s'", tenant.name, tenant.account);
    if (ret) {
        printf(gettext("having the same name or account!"));
        return -1;
    }

    // 调用平台命令设置账户密码
    sprintf(
        system_str,
        "/usr/local/bin/admacct add name '%s' password '%s' vmfwid 0 max_err_num 5 valid_time 0 sleep_time 0 role "
        "user employed 1 local_auth on sim_auth off  > /dev/null 2>&1",
        tenant.account,
        tenant.password);

    system(system_str);

    if (sqlite3_open(SYSTEM_CONFIG_DB_FILE, &Db_Store) != SQLITE_OK) {
        return -1;
    }

    rc = sqlite3_exec_printf(
        Db_Store, "UPDATE accounts SET is_usrmngr='1' where account='%s'", 0, 0, &errmsg, tenant.account);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "fail to UPDATE!!");
        ret = -1;
        goto out;
    }

    // 写入信息到数据库
    char time_str[128] = {0};
    cps_get_time_now(time_str, 128);
    tenant.regs_date = time_str;
    ret              = db_tenant_add(&tenant);
    if (ret) {
        printf(gettext("add tenant failed!"));
    }
out:
    if (Db_Store != NULL) {
        sqlite3_close(Db_Store);
        Db_Store = NULL;
    }
    return ret;
}

int cps_tenant_set(int argc, char* argv[]) {
    int           ret    = 0;
    TENANT_INFO_T tenant = {0};
    memset(&tenant, 0, sizeof(TENANT_INFO_T));
    tenant.name          = argv[4];
    tenant.valid_time    = -1;
    char   account[32]   = {0};
    char   cmd[512]      = {0};
    int    employed_flag = 0;
    time_t now           = time(0);

    for (int j = 5; j < argc - 1; j += 2) {
        if (!strcmp(argv[j], "corporate_name")) {
            tenant.corporate_name = argv[j + 1];
        } else if (!strcmp(argv[j], "telephone")) {
            tenant.telephone = argv[j + 1];
        } else if (!strcmp(argv[j], "phone_number")) {
            tenant.phone_number = argv[j + 1];
        } else if (!strcmp(argv[j], "mail")) {
            tenant.mail = argv[j + 1];
        } else if (!strcmp(argv[j], "remark")) {
            tenant.remark = argv[j + 1];
        } else if (!strcmp(argv[j], "valid_time")) {
            tenant.valid_time = atoi(argv[j + 1]);
        }
    }

    // 写入信息到数据库
    ret = db_tenant_set(&tenant);
    if (ret) {
        printf(gettext("set tenant failed!"));
    }

    // 获取帐号名称
    cps_get_data_from_db(CPS_TENANT_INFO_TABLE, "name", (void*)tenant.name, "account", account, 32, false);

    if (tenant.valid_time == 0 || tenant.valid_time > now) {
        employed_flag = 1;

    } else {
        employed_flag = 0;
    }
    sprintf(
        cmd,
        "sqlite3 /usr/local/conf/configs.db \"UPDATE accounts SET employed=%d where account='%s'\" > /dev/null",
        employed_flag,
        account);
        
    system(cmd);

    return ret;
}

int cps_tenant_on_off(int argc, char* argv[]) {
    return db_tenant_on_off();
}

int cps_tenant_reset_bind_key(int argc, char* argv[]) {
    int ret = db_tenant_reset_bind_key(argv[4]);
    if (ret) {
        printf(gettext("reset admin faild!"));
    }
    return ret;
}

int cps_tenant_set_paswword(int argc, char* argv[]) {
    char sytem_str[128] = {0};

    sprintf(
        sytem_str,
        "/usr/local/bin/admacct set sname '%s' dname '%s' password 'lion@LL99' > /dev/null 2>&1",
        argv[4],
        argv[4]);

    system(sytem_str);
    return 0;
}

int cps_tenant_del(int argc, char* argv[]) {
    int force     = 0;
    int ret       = 0;
    int tenant_id = atoi(argv[4]);
    if (argc == 7) {
        force = atoi(argv[6]);
    }
    char   time_str[16] = {0};
    long   valid_time   = 0;
    time_t now_time     = time(NULL);

    // 判断当前租户是否失效
    cps_get_data_from_db(CPS_TENANT_INFO_TABLE, "tenant_id", (char*)&tenant_id, "valid_time", time_str, 16, true);
    valid_time = atol(time_str);
    if (valid_time != 0 && valid_time < now_time) {
        force = 1;
    }

    // 判断租户是否有资源在使用,如果有不允许删除
    if (!force) {
        ret = db_check_tenant_src(tenant_id);
        if (ret) {
            printf(gettext("tenant has virtual machines in use!"));
            return ret;
        }
    }

    // 删除租户
    ret = db_tenant_del(tenant_id);
    if (ret) {
        printf(gettext("delete tenant failed!"));
        return ret;
    }

    return 0;
}
