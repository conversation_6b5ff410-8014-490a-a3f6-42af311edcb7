#include "cps_common.h"
#include "key_operate.h"
#include "cps_srv_manage.h"
#include "cps_cluster_manage.h"
#include "cps_msg.h"
#include "cps_vsm_manage.h"

int srv_del_all(char *tenant_name)
{
    unsigned int ret = ERR_NONE;
    int nrow = 0, ncol = 0, i = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (tenant_name == NULL)
        return ERR_PARAM;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select srv_id from %s where tenant_name = \'%s\'", CPS_SRV_MANAGE_TABLE, tenant_name);
    sqlite3_busy_handler(cps_db, callback_db, NULL);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
	if ((ret != SQLITE_OK)) {
		DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
		ret = ERR_SQL_OPT;
        goto out;
	}

    for (i  = 0; i < nrow; i++) {
        ret = srv_conf_proc("del", tenant_name, result[i + 1]);
        API_CHECK_FUNC(ret, "srv_conf_proc");
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    //close_cps_db();
    return ret;
}

int srv_conf_check(char *srv_id, char *action)
{
    int ret = 0;
    int cluster_state = 0;
    char cluster_id[64];
    char tmp[16];

    if (srv_id == NULL || action == NULL)
        return ERR_PARAM;

    memset(tmp, 0, sizeof(tmp));
    //开启时需保证集群已开启
    if (strcmp(action, "start_up") == 0) {
        memset(cluster_id, 0, sizeof(cluster_id));
        ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "srv_id", srv_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
        API_CHECK_FUNC(ret, "cps_get_data_from_db");

        ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", cluster_id, "cluster_state", tmp, sizeof(tmp), false);
        cluster_state = atoi(tmp);
        if (cluster_state == 0)
            return ERR_CLUSTER_ENABLE;
    }

out:
    return ret;
}

int srv_conf_proc(char *action, char *tenant_name, char *srv_id)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int nrow = 0, ncol = 0, i = 0;
    int sign_len = 0;
    int state = 0;
	char sql[SQL_LEN_MAX] = {0};
	char request_id[16] = {0};
    char sign_data[1024] = {0};
    char data[DATA_MAX_LEN] = {0};
    char *data_base64 = NULL;
    char *remark = NULL;
    char **result = NULL;
    cluster_srv_info srv;
    msg_header_t *msg_header = NULL;

    //判断操作是否合法
    ret = srv_conf_check(srv_id, action);
    API_CHECK_FUNC(ret, "srv_conf_check");

    memset(&srv, 0, sizeof(srv));
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_id,srv_port,srv_http_port,load_mode,weight_policy from %s where srv_id=\'%s\'", CPS_SRV_MANAGE_TABLE, srv_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    srv.cluster_id = result[ncol];
    srv.srv_id = srv_id;
    srv.srv_port  = atoi(result[ncol + 1]);
    srv.srv_http_port = atoi(result[ncol + 2]);
    srv.load_mode = atoi(result[ncol + 3]);
    srv.weight_policy = atoi(result[ncol + 4]);
    //get interface_json
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select interface_name,cluster_ipv4,cluster_ipv6 from %s where srv_id=\'%s\'", CPS_SRV_INTERFACE_IP_TABLE, srv_id);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }
    cJSON *root = cJSON_CreateObject();
    cJSON *array = cJSON_CreateArray();
    for (int i = 0; i < nrow; i++) {
        //转成json字符串
        cJSON *obj = cJSON_CreateObject();
        cJSON_AddStringToObject(obj, "interfaceName", result[i*ncol+1]);
        cJSON_AddStringToObject(obj, "ipv4", result[i*ncol+2]);
        cJSON_AddStringToObject(obj, "ipv6", result[i*ncol+3]);
        cJSON_AddItemToArray(array, obj);
    }
    cJSON_AddItemToObject(root, "interface", array);
    char *str_root_json = cJSON_PrintUnformatted(root);
    cJSON_Delete(root);
    char pData[1024] = {0};
    srv.interface_json = NULL;
    if (str_root_json) {
        strcpy(pData, str_root_json);
        srv.interface_json = pData;
    }
    
    if (strcmp(action, "del") != 0) {
        state = (strcmp(action, "start_up") == 0)? 1 : 0;
        ret = srv_opt_data_comp(request_id, &srv, state, OPT_EDIT, data, sizeof(data));
        API_CHECK_FUNC(ret, "vsm_opt_data_comp");
        data_len = strlen(data);
    }else {
        ret = srv_del_data_comp(request_id, srv_id, data, sizeof(data));
        API_CHECK_FUNC(ret, "vsm_del_data_comp");
        data_len = strlen(data);
    }

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");


    //组tcp传输body数据
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, srv.cluster_id);
    if (result) {
        sqlite3_free_table(result);
        result = NULL;
    }
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    for (i = 0; i < nrow; i++) {
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)realloc(msg_header, sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, SERVICE_MAIN_TYPE, (strcmp(action, "delete") == 0)? CMD_DELETE : CMD_MODIFY);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");
    }
    //更新数据库
    if (strcmp(action, "del") == 0) {
        ret = srv_cluster_ref_update(cps_db, srv_id, OPT_DEL);
        API_CHECK_FUNC(ret, "srv_cluster_ref_update");
    }
    ret = srv_conf_db_update(cps_db, srv_id, tenant_name, action, state);
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    //close_cps_db();
    return ret;
}

int srv_opt_proc(cluster_srv_info *srv, int opt)
{
    unsigned int ret = ERR_NONE;
    unsigned int data_len = 0, body_len = 0;
    int nrow = 0, ncol = 0, i = 0;
    int sign_len = 0;
    int state = 0;
	char sql[SQL_LEN_MAX] = {0};
	char request_id[16] = {0};
    char sign_data[1024] = {0};
    char data[DATA_MAX_LEN] = {0};
    char *data_base64 = NULL;
    char *remark = NULL;
    char **result = NULL;
    msg_header_t *msg_header = NULL;

    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");

    //检测服务是否已存在
    ret = cps_srv_check(cps_db, srv->srv_id, srv->tenant_name);
    API_CHECK_FUNC(ret, "cps_srv_check");

    //生成8位随机数作为requestid
    ret = cps_generate_random(request_id, sizeof(request_id));
    API_CHECK_FUNC(ret, "cps_generate_random");
    char *str_opt = (opt == OPT_CREATE)? "create" : "modify";
    if (opt == OPT_CREATE)
    {
        srv->srv_id = request_id;
    }
    //parse interface_json
    net_config_t interface_cfg; 
    if (srv->interface_json != NULL && strlen(srv->interface_json) > 0) {
        memset(&interface_cfg, 0, sizeof(interface_cfg));
        ret = parse_interface_json(srv->interface_json, &interface_cfg);
        if (ret != ERR_NONE) {
            ret = ERR_PARAM;
            goto out;
        }
    }
    if (interface_cfg.interface_count > 0) {
        for (int i = 0; i < interface_cfg.interface_count; i++) {
            if (interface_cfg.interfaces[i].ipv4 && strlen(interface_cfg.interfaces[i].ipv4) > 0) {
                //查询vsm_ip_info表ip是否存在
                int num = cps_get_num_from_db(CPS_SRV_INTERFACE_IP_TABLE, "cluster_ipv4=\'%s\' and interface_name=\'%s\' and srv_id=\'%s\'", interface_cfg.interfaces[i].ipv4, interface_cfg.interfaces[i].interface_name, srv->srv_id);
                if (num <=0) {
                    //查询ip_info表ip是否存在
                    num = cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\' and use_status=1", interface_cfg.interfaces[i].ipv4);
                    if (num > 0) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "srv(%s) interface(%s) ipv4(%s) is used\n", srv->srv_id, interface_cfg.interfaces[i].interface_name, interface_cfg.interfaces[i].ipv4);
                        ret = ERR_IP_USED;
                        goto out;
                    }
                }
            }
            if (interface_cfg.interfaces[i].ipv6 && strlen(interface_cfg.interfaces[i].ipv6) > 0) {
                //查询vsm_ip_info表ip是否存在
                int num = cps_get_num_from_db(CPS_SRV_INTERFACE_IP_TABLE, "cluster_ipv6=\'%s\' and interface_name=\'%s\' and srv_id=\'%s\'", interface_cfg.interfaces[i].ipv6, interface_cfg.interfaces[i].interface_name, srv->srv_id);
                if (num <=0) {
                    //查询ip_info表ip是否存在
                    num = cps_get_num_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\' and use_status=1", interface_cfg.interfaces[i].ipv6);
                    if (num > 0) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "srv(%s) interface(%s) ipv6(%s) is used\n", srv->srv_id, interface_cfg.interfaces[i].interface_name, interface_cfg.interfaces[i].ipv6);
                        ret = ERR_IP_USED;
                        goto out;
                    }
                }
            }
        }
    }
    //组创建服务接口调用的json数据,即传入参数
    ret = srv_opt_data_comp(request_id, srv, (opt == OPT_CREATE)? 0 : state, opt, data, sizeof(data));
    API_CHECK_FUNC(ret, "vsm_opt_data_comp");
    data_len = strlen(data);

    //对数据进行base64编码
    data_base64 = (unsigned char *)malloc(strlen(data) * 2);
    if (!data_base64) {
        ret = ERR_MALLOC;
        goto out;
    }
    base64_encode(data, data_base64, data_len);

    //对json数据进行签名
    ret = sign_with_internal_key(data, data_len, sign_data, &sign_len);
    API_CHECK_FUNC(ret, "sign_with_internal_key");


    //组tcp传输body数据
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select host_ip from %s where cluster_id=\'%s\'", CPS_CLUSTER_HOST_TABLE, srv->cluster_id);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    ret = sqlite3_get_table(cps_db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
        goto out;
    }

    for (i = 0; i < nrow; i++) {
        memset(data, 0, sizeof(data));
        body_len = sizeof(data);
        ret = cps_msg_body_comp(result[i + 1], request_id, sign_data, data_base64, data_len, data, &body_len);

        //组tcp包
        msg_header = (msg_header_t *)realloc(msg_header, sizeof(msg_header_t) + body_len);
        if (!msg_header) {
            ret = ERR_MALLOC;
            goto out;
        }
        cps_request_init(msg_header, data, body_len, SERVICE_MAIN_TYPE, CMD_MODIFY);

        //发送tcp请求,并获取返回状态
        ret = cps_request_send(msg_header, callback_status_check, request_id);
        API_CHECK_FUNC(ret, "cps_request_send");
    }

    //更新数据库
    ret = srv_opt_db_update(srv, opt);
out:
    if(result){
        sqlite3_free_table(result);
    }
    if (data_base64)
        free(data_base64);
    if (msg_header)
        free(msg_header);
    //close_cps_db();
    return ret;
}

int srv_del_data_comp(char *request_id, char *srv_id, char *data, int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    cJSON *root = NULL;

    root = cJSON_CreateObject();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", "destroy");
	cJSON_AddStringToObject(root, "serviceId", srv_id);
	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

int srv_opt_data_comp(char *request_id, cluster_srv_info *srv, int state, int opt, char *data, int data_len)
{
    int ret = ERR_NONE;
    char *pData = NULL;
    char *lbAlg[] = {"rr", "sh", "wrr"};
    cJSON *root = NULL;
    cJSON *array = NULL;
    cJSON *obj = NULL;
    cJSON *obj_http = NULL;

    root = cJSON_CreateObject();
    array = cJSON_CreateArray();
	cJSON_AddStringToObject(root, "requestId", request_id);
	cJSON_AddStringToObject(root, "oprType", (opt == OPT_CREATE)? "create" : "modify");
    if (srv->cluster_id != NULL)
        cJSON_AddStringToObject(root, "clusterId", srv->cluster_id);
	cJSON_AddStringToObject(root, "serviceId", srv->srv_id);
	cJSON_AddNumberToObject(root, "status", state);

    obj = cJSON_CreateObject();
	cJSON_AddNumberToObject(obj, "realPort", 9000);
	cJSON_AddNumberToObject(obj, "directorPort", srv->srv_port);
	cJSON_AddStringToObject(obj, "lbAlg", lbAlg[srv->load_mode]);
	cJSON_AddNumberToObject(obj, "weightPolicy", srv->weight_policy);
    cJSON_AddItemToArray(array, obj);

    obj_http = cJSON_CreateObject();
	cJSON_AddNumberToObject(obj_http, "realPort", 9100);
	cJSON_AddNumberToObject(obj_http, "directorPort", srv->srv_http_port);
	cJSON_AddStringToObject(obj_http, "lbAlg", lbAlg[srv->load_mode]);
	cJSON_AddNumberToObject(obj_http, "weightPolicy", srv->weight_policy);
    cJSON_AddItemToArray(array, obj_http);
    cJSON_AddItemToObject(root, "apps", array);
 
    //parse interface_json
    if (srv->interface_json != NULL && strlen(srv->interface_json) > 0) {
        net_config_t cfg;
        memset(&cfg, 0, sizeof(cfg));
        ret = parse_interface_json(srv->interface_json, &cfg);
        API_CHECK_FUNC(ret, "parse_interface_json");
        if (cfg.interface_count > 0) {
            for (int i = 0; i < cfg.interface_count; i++) {
                if (cfg.interfaces[i].ipv4 && strlen(cfg.interfaces[i].ipv4) > 0) {
                    cJSON_AddStringToObject(root, "vip", cfg.interfaces[i].ipv4);
                    break;
                }
                if (cfg.interfaces[i].ipv6 && strlen(cfg.interfaces[i].ipv6) > 0) { 
                    cJSON_AddStringToObject(root, "vip", cfg.interfaces[i].ipv6);
                    break;
                }
                
            }
            //add interface to root
            cJSON_AddItemToObject(root, "interface", cfg.interface_array);
        }
    }

    //add interface to root

	pData = cJSON_PrintUnformatted(root);
    if (pData) {
        DEBUG_CPS_CLI(COMMON_DEBUG, "[%s %d]json data:%s\r\n",__func__,__LINE__,pData);
        if (strlen(pData) > data_len) {
            ret = ERR_PARAM;
            goto out;
        }
        memcpy(data, pData, strlen(pData));
        data[strlen(pData)] = '\0';
    }
out:
    if (pData)
        cJSON_free(pData);
    cJSON_Delete(root);

    return ret;
}

int cps_srv_check(sqlite3 *db, char *name, char *tenant_name)
{
    int ret = 0;
    int nrow = 0, ncol = 0;
    char **result = NULL;
	char sql[SQL_LEN_MAX] = {0};

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select * from %s where srv_name=\'%s\' and tenant_name=\'%s\'", CPS_SRV_MANAGE_TABLE, name, tenant_name);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return -1;
    }
    if(result){
        sqlite3_free_table(result);
    }
    return (nrow == 0)? 0 : -1;;
}

int srv_cluster_ref_update(sqlite3 *db, char *srv_id, int opt_type)
{
    int ret = 0;
    int nrow = 0, ncol = 0;
	char sql[SQL_LEN_MAX] = {0};
    char **result = NULL;

    if (db == NULL || srv_id == NULL)
        return ERR_PARAM;

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "select cluster_id from %s where srv_id=\'%s\'", CPS_SRV_MANAGE_TABLE, srv_id);
    ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
    if ((ret != SQLITE_OK) || (nrow <= 0)) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    memset(sql, 0, sizeof(sql));
    sprintf(sql, "update %s set ref_num=%d where cluster_id=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, (opt_type == OPT_ENABLE)? 1 : 0, result[1]);
    DEBUG_CPS_CLI(COMMON_DEBUG, "sql:%s\n", sql);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        return ERR_SQL_OPT;
    }

    if(result){
        sqlite3_free_table(result);
    }
    return ret;
}

int srv_conf_db_update(sqlite3 *db, char *srv_id, char *tenant_name, char *action, int state)
{
    int ret = 0;
    int nrow = 0, ncol = 0;
    char cluster_ip[64];
    char **result = NULL;
	char sql[SQL_LEN_MAX] = {0};

    memset(sql, 0, sizeof(sql));
    memset(cluster_ip, 0, sizeof(cluster_ip));
    if (strcmp(action, "del") == 0) {
        
        //更新引用计数
        memset(sql, 0, sizeof(sql));
        sprintf(sql, "select cluster_id from %s where srv_id=\'%s\'", CPS_SRV_MANAGE_TABLE, srv_id);
        ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
        if ((ret != SQLITE_OK) || (nrow <= 0)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        memset(sql, 0, sizeof(sql));
        //删除srv_id对应的数据
        sprintf(sql, "delete from %s where srv_id=\'%s\'", CPS_SRV_MANAGE_TABLE, srv_id);
        if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        memset(sql, 0, sizeof(sql));
        //删除集群虚ip
        sprintf(sql, "select cluster_ipv4,cluster_ipv6 from %s where srv_id=\'%s\'", CPS_SRV_INTERFACE_IP_TABLE, srv_id);
        ret = sqlite3_get_table(db, sql, &result, &nrow, &ncol, NULL);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql(%s)\n", sql);
        if ((ret != SQLITE_OK)) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            return ERR_SQL_OPT;
        }
        if (nrow > 0) {
             //更新ip使用数据库
            for (int i = 0; i < nrow; i++) {
                // 注意：sqlite3_get_table返回的结果格式是 [列名1, 列名2, 行1列1, 行1列2, 行2列1, 行2列2, ...]
                // 所以第i行的cluster_ipv4是result[(i+1)*ncol]，第i行的cluster_ipv6是result[(i+1)*ncol+1]
                if (result[(i+1)*ncol] && strlen(result[(i+1)*ncol]) > 0) {
                    memset(sql, 0, sizeof(sql));
                    sprintf(sql, "delete from %s where ip = \'%s\'", CPS_IP_INFO_TABLE, result[(i+1)*ncol]);  
                    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "delete cluster_ipv4 failed: %s", result[(i+1)*ncol]);
                    }
                }
                if (result[(i+1)*ncol+1] && strlen(result[(i+1)*ncol+1]) > 0) {
                    memset(sql, 0, sizeof(sql));
                    sprintf(sql, "delete from %s where ip = \'%s\'", CPS_IP_INFO_TABLE, result[(i+1)*ncol+1]);
                    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
                        DEBUG_CPS_CLI(ERR_DEBUG, "delete cluster_ipv6 failed: %s", result[(i+1)*ncol+1]);
                    }
                }
            }
        }
        memset(sql, 0, sizeof(sql));
        //删除 srv_interface_ip表中srv_id对应的数据
        sprintf(sql, "delete from %s where srv_id=\'%s\'", CPS_SRV_INTERFACE_IP_TABLE, srv_id);
        if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }

        memset(sql, 0, sizeof(sql));
        sprintf(sql, "update %s set ref_num=0 where cluster_id=\'%s\'", CPS_CLUSTER_MANAGE_TABLE, result[1]);
        DEBUG_CPS_CLI(COMMON_DEBUG, "sql:(%s)\n", sql);
        if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
            DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
            ret = ERR_SQL_OPT;
            goto out;
        }
        

        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }
       
    }else {
        sprintf(sql, "update %s set state=%d where srv_id=\'%s\'", CPS_SRV_MANAGE_TABLE, state, srv_id);
    }

    DEBUG_CPS_CLI(COMMON_DEBUG, "sql(%s)\n", sql);
    if (sqlite3_exec(db, sql, NULL, NULL, NULL) != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "sql(%s) exec failed\n", sql);
        ret = ERR_SQL_OPT;
    }

out:
    if(result){
        sqlite3_free_table(result);
    }
    print_errmsg(ret);
    return ret;
}


int cps_get_srv_interface_ip(char *srv_id, char *interface_name, char *ipv4, char *ipv6)
{   
    int ret = -1;
    char sql[256] = {0};
    sqlite3_stmt *stmt=NULL; 
    char str_query[256] = {0};
    sprintf(str_query, "select cluster_ipv4,cluster_ipv6 FROM %s where srv_id=\'%s\' and interface_name=\'%s\'", CPS_SRV_INTERFACE_IP_TABLE, srv_id, interface_name);
    ret = open_cps_db();
    API_CHECK_FUNC(ret, "open_cps_db");
    int rc = sqlite3_prepare_v2(cps_db, str_query, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
        DEBUG_CPS_CLI(ERR_DEBUG, "Failed to prepare statement: %s", sqlite3_errmsg(cps_db));
        ret = ERR_SQL_OPT;
        goto out;
    }
    if(sqlite3_step(stmt) == SQLITE_ROW) {  
        char *cluster_ipv4 = (char *)sqlite3_column_text(stmt, 0);
        char *cluster_ipv6 = (char *)sqlite3_column_text(stmt, 1);
        if(strlen(cluster_ipv4) > 0){
            strcpy(ipv4, cluster_ipv4);
        }
        if(strlen(cluster_ipv6) > 0){
            strcpy(ipv6, cluster_ipv6);
        }
        ret = ERR_NONE;
    }
    sqlite3_reset(stmt);
    sqlite3_finalize(stmt);
out:
    return ret;

}
int srv_opt_db_update(cluster_srv_info *srv, int opt)
{
    int ret = 0;
    int cluster_type = 0;
    char tmp[8] = {0};
	char old_ip[64] = {0};
	char cluster_ip[64] = {0};
	char ctime[64] = {0};

    //获取时间
    memset(ctime, 0, sizeof(ctime));
    cps_get_time_now(ctime, sizeof(ctime));

    //获取集群模式
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", srv->cluster_id, "cluster_type", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    cluster_type = atoi(tmp);
    DEBUG_CPS_CLI(COMMON_DEBUG, "opt:[%d]\n", opt);

    //parse interface_json
    net_config_t cfg;
    memset(&cfg, 0, sizeof(cfg));
    ret = parse_interface_json(srv->interface_json, &cfg);
    API_CHECK_FUNC(ret, "parse_interface_json");
    
    if (opt == OPT_CREATE) {
        ret = cps_insert_data_to_db(CPS_SRV_MANAGE_TABLE, "(srv_id,srv_name,cluster_name,cluster_id,srv_port,srv_http_port,tenant_name,create_time,state,load_mode,weight_policy,remark) values(\'%s\',\'%s\',\'%s\',\'%s\',%d,%d,\'%s\',\'%s\',%d,%d,%d,\'%s\')", srv->srv_id, srv->srv_name, srv->cluster_name, srv->cluster_id, srv->srv_port, srv->srv_http_port, srv->tenant_name, ctime, 0, srv->load_mode, srv->weight_policy, (srv->remark == NULL)? "" : srv->remark);
        API_CHECK_FUNC(ret, "cps_insert_data_to_db");
        for (int i = 0; i < cfg.interface_count; i++) {
            ret = cps_insert_data_to_db(CPS_SRV_INTERFACE_IP_TABLE, "(srv_id,cluster_id,interface_name,cluster_ipv4,cluster_ipv6,tenant_name) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", srv->srv_id, srv->cluster_id, cfg.interfaces[i].interface_name, (cfg.interfaces[i].ipv4 == NULL)? "" : cfg.interfaces[i].ipv4, (cfg.interfaces[i].ipv6 == NULL)? "" : cfg.interfaces[i].ipv6, srv->tenant_name);
            API_CHECK_FUNC(ret, "cps_insert_data_to_db");
            //插入成功更新ip使用数据库
            if (cfg.interfaces[i].ipv4 && strlen(cfg.interfaces[i].ipv4) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg.interfaces[i].ipv4);
                API_CHECK_FUNC(ret, "cps_insert_data_to_db");
            }
            if (cfg.interfaces[i].ipv6 && strlen(cfg.interfaces[i].ipv6) > 0) {
                ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg.interfaces[i].ipv6);
                API_CHECK_FUNC(ret, "cps_insert_data_to_db");
            }
        }

        //更新集群引用计数
        ret = cps_update_data_to_db(CPS_CLUSTER_MANAGE_TABLE, "ref_num=1 where cluster_id=\'%s\'", srv->cluster_id);
        //ret = srv_cluster_ref_update(db, srv->srv_id, OPT_ENABLE);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }else {
        //获取原集群ip
        for (int i = 0; i < cfg.interface_count; i++) {
            interface_cfg_t *cfg_t = &cfg.interfaces[i];
            char cluster_ipv4[64] = {0};
            char cluster_ipv6[64] = {0};
            memset(cluster_ipv4, 0, sizeof(cluster_ipv4));
            memset(cluster_ipv6, 0, sizeof(cluster_ipv6));
          
            int num = cps_get_num_from_db(CPS_SRV_INTERFACE_IP_TABLE, "srv_id=\'%s\' and interface_name=\'%s\'", srv->srv_id, cfg.interfaces[i].interface_name);
            if (num > 0) {
                //存在集群的网卡
                //ret = cps_get_data_from_db(CPS_SRV_INTERFACE_IP_TABLE, "srv_id=\'%s\' and interface_name=\'%s\'", srv->srv_id, cfg.interfaces[i].interface_name, "cluster_ipv4", cluster_ipv4, sizeof(cluster_ipv4), false);
                //API_CHECK_FUNC(ret, "cps_get_data_from_db");
                //ret = cps_get_data_from_db(CPS_SRV_INTERFACE_IP_TABLE, "srv_id=\'%s\' and interface_name=\'%s\'", srv->srv_id, cfg.interfaces[i].interface_name, "cluster_ipv6", cluster_ipv6, sizeof(cluster_ipv6), false);
                //API_CHECK_FUNC(ret, "cps_get_data_from_db");
                ret = cps_get_srv_interface_ip(srv->srv_id, cfg.interfaces[i].interface_name, cluster_ipv4, cluster_ipv6);
                if (ret != ERR_NONE) {
                    DEBUG_CPS_CLI(ERR_DEBUG, "cps_get_srv_interface_ip [%s] ,[%s],failed\n", srv->srv_id, cfg.interfaces[i].interface_name);
                    return ret;
                }
                if (cfg_t->ipv4 && strlen(cfg_t->ipv4) > 0) {
                    if (strcmp(cfg_t->ipv4, cluster_ipv4) != 0) {
                        ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'",cluster_ipv4);
                        API_CHECK_FUNC(ret, "cps_del_data_from_db");
                        ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg_t->ipv4);
                        API_CHECK_FUNC(ret, "cps_update_data_to_db");
                    }
                }
                if (cfg_t->ipv6 && strlen(cfg_t->ipv6) > 0) {
                    if (strcmp(cfg_t->ipv6, cluster_ipv6) != 0) {
                        ret = cps_del_data_from_db(CPS_IP_INFO_TABLE, "ip=\'%s\'",cluster_ipv6);
                        API_CHECK_FUNC(ret, "cps_del_data_from_db");
                        ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg_t->ipv6);
                        API_CHECK_FUNC(ret, "cps_update_data_to_db");
                    }
                }
                
            }else{
                //不存在则插入
                ret = cps_insert_data_to_db(CPS_SRV_INTERFACE_IP_TABLE, "(srv_id,cluster_id,interface_name,cluster_ipv4,cluster_ipv6,tenant_name) values(\'%s\',\'%s\',\'%s\',\'%s\',\'%s\',\'%s\')", srv->srv_id, srv->cluster_id, cfg.interfaces[i].interface_name, (cfg.interfaces[i].ipv4 == NULL)? "" : cfg.interfaces[i].ipv4, (cfg.interfaces[i].ipv6 == NULL)? "" : cfg.interfaces[i].ipv6, srv->tenant_name);
                API_CHECK_FUNC(ret, "cps_insert_data_to_db");
                if (cfg.interfaces[i].ipv4 && strlen(cfg.interfaces[i].ipv4) > 0) {
                    ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg.interfaces[i].ipv4);
                    API_CHECK_FUNC(ret, "cps_insert_data_to_db");
                }
                if (cfg.interfaces[i].ipv6 && strlen(cfg.interfaces[i].ipv6) > 0) {
                    ret = cps_insert_data_to_db(CPS_IP_INFO_TABLE, "(use_status,tenant_name,ip,use) values(1,\'%s\',\'%s\',1)", srv->tenant_name, cfg.interfaces[i].ipv6);
                    API_CHECK_FUNC(ret, "cps_insert_data_to_db");
                }
            }
            
        }
        ret = cps_update_data_to_db(CPS_SRV_MANAGE_TABLE, "srv_port=%d,srv_http_port=%d,load_mode=%d,weight_policy=%d,remark=\'%s\' where srv_id=\'%s\'", srv->srv_port, srv->srv_http_port, srv->load_mode, srv->weight_policy, (srv->remark == NULL)? "" : srv->remark, srv->srv_id);
        API_CHECK_FUNC(ret, "cps_update_data_to_db");
    }

    if (cluster_type == CLUSTER_MODE_MB) {
        //主备模式设置负载模式和权重为空
        ret = cps_update_data_to_db(CPS_SRV_MANAGE_TABLE, "load_mode=\'%s\',weight_policy=\'%s\' where srv_id=\'%s\'", "", "", srv->srv_id);

    }else if (srv->load_mode != WEIGHT_POLICY_WRR) {
        //不是加权轮询模式的话更新权重策略为空
        ret = cps_update_data_to_db(CPS_SRV_MANAGE_TABLE, "weight_policy=\'%s\' where srv_id=\'%s\'", "", srv->srv_id);
    }
    API_CHECK_FUNC(ret, "cps_update_data_to_db");

out:
    return ret;
}

int cps_srv_add(int argc, char *argv[])
{
    unsigned int ret = 0;
    unsigned int ref_num = 0;
    char cluster_name[128];
    char tmp[8];
    cluster_srv_info srv;

    memset(&srv, 0, sizeof(srv));
    srv.tenant_name = argv[4];
    srv.srv_name = argv[6];
    srv.cluster_id = argv[8];
    srv.interface_json = argv[10];
    srv.srv_port  = atoi(argv[12]);
    srv.srv_http_port = atoi(argv[14]);
    srv.load_mode = atoi(argv[16]);
    srv.weight_policy = atoi(argv[18]);
    srv.remark = (argc == 21)? argv[20] : NULL;

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    //判断ref_num是否大于0
    memset(tmp, 0, sizeof(tmp));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", srv.cluster_id, "ref_num", tmp, sizeof(tmp), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    ref_num = atoi(tmp);
    DEBUG_CPS_CLI(COMMON_DEBUG, "ref_num:%d", ref_num);
    if (ref_num > 0) {
        ret = ERR_CLUSTER_REF;
        goto out;
    }

    //获取cluster_name
    memset(cluster_name, 0, sizeof(cluster_name));
    ret = cps_get_data_from_db(CPS_CLUSTER_MANAGE_TABLE, "cluster_id", srv.cluster_id, "cluster_name", cluster_name, sizeof(cluster_name), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    srv.cluster_name = cluster_name;

    ret = srv_opt_proc(&srv, OPT_CREATE);
    API_CHECK_FUNC(ret, "srv_opt_proc");
out:
    print_errmsg(ret);
    return ret;
}

int cps_srv_edit(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char tenant[128] = {0};
    char cluster_id[16] = {0};
    cluster_srv_info srv;
    memset(&srv, 0, sizeof(srv));
    srv.tenant_name = argv[4];
    srv.srv_id = argv[6];
    srv.cluster_id = NULL;
    srv.interface_json = argv[8];
    srv.srv_port = atoi(argv[10]);
    srv.srv_http_port = atoi(argv[12]);
    srv.load_mode = atoi(argv[14]);
    srv.weight_policy = atoi(argv[16]);
    srv.remark = NULL;
    if (argc == 19)
        srv.remark = argv[18];

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "srv_id", srv.srv_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, srv.tenant_name) != 0) {
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s",srv.tenant_name, tenant);
        ret = ERR_SUPP_OPT;
        goto out;
    }

    memset(cluster_id, 0, sizeof(cluster_id));
    ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "srv_id", srv.srv_id, "cluster_id", cluster_id, sizeof(cluster_id), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    srv.cluster_id = cluster_id;

    ret = srv_opt_proc(&srv, OPT_EDIT);
    API_CHECK_FUNC(ret, "srv_opt_proc");
out:
    print_errmsg(ret);
    return ret;
}

int cps_srv_conf(int argc, char *argv[])
{
    unsigned int ret = ERR_NONE;
    char *opt = argv[3];
    char *tenant_name = argv[5];
    char *srv_id = argv[7];
    char tenant[128] = {0};

    DEBUG_CPS_CLI(COMMON_DEBUG, "%s start...\n",__func__);
    memset(tenant, 0, sizeof(tenant));
    ret = cps_get_data_from_db(CPS_SRV_MANAGE_TABLE, "srv_id", srv_id, "tenant_name", tenant, sizeof(tenant), false);
    API_CHECK_FUNC(ret, "cps_get_data_from_db");
    if (strcmp(tenant, tenant_name) != 0) {
        ret = ERR_SUPP_OPT;
        DEBUG_CPS_CLI(ERR_DEBUG, "tenant name:%s %s",tenant_name, tenant);
        goto out;
    }

    ret = srv_conf_proc(opt, tenant_name, srv_id);
out:
    print_errmsg(ret);
    return ret;
}
