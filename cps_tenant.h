/*
 * Project: base
 * Moudle: cps
 * File: cps_tenant.h
 * Created Date: 2023-08-29 14:55:26
 * Author: caohongfa
 * Description: caohongfa
 * -----
 * todo: modified
 * -----
 * Copyright (c) Inc
 */
#ifndef _cps_TENANT_H
#define _cps_TENANT_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
int cps_tenant_info_del_all(char *tenant_name);
int cps_tenant_add(int argc,char* argv[]);
int cps_tenant_set(int argc,char* argv[]);
int cps_tenant_on_off(int argc,char* argv[]);
int cps_tenant_reset_bind_key(int argc,char* argv[]);
int cps_tenant_set_paswword(int argc,char* argv[]);
int cps_tenant_del(int argc,char* argv[]);
#endif // _cps_TENANT_H
