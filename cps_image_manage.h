#ifndef __cps_IMAGE_MANAGE_H__
#define __cps_IMAGE_MANAGE_H__

enum
{
    EXPORT_START    = 0,    //影像导出中
    EXPORT_SUCCESS  = 1,    //导出成功
    EXPORT_FAIL     = 2     //导出失败
};

enum
{
    IMPORT_START    = 0,    //影像导入中
    IMPORT_SUCCESS  = 1,    //导入成功
    IMPORT_FAIL     = 2     //导入失败
};

int image_del_all(char *tenant_name);
int cps_image_export(int argc, char *argv[]);
int cps_image_import(int argc, char *argv[]);
int cps_image_del(int argc, char *argv[]);
int cps_image_opt_state(int argc, char *argv[]);
int asymm_image_import_proc(char *request_id, int state, char *resp_msg);
int asymm_image_export_proc(char *request_id, int state, char *resp_msg);
int cps_image_import_proc(char *tenant_name, char *image_name, char *id, char **req_id, int id_len, char *vsmid);
int cps_image_export_proc(char *tenant_name, char *vsm_id, char *req_id, int id_len, int drift_flag);
int cps_image_import_result_check(char **request_id, int vsm_num);
int cps_image_export_result_check(char *vsm_id, char *request_id);
int image_file_sign(char *file_name, char *sign_data, int *data_len);

#endif
