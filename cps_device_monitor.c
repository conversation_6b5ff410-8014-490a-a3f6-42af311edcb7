/*
 * Project: base
 * Moudle:
 * File: cps_device_monitor.c
 * Created Date: 2023-09-11 14:57:13
 * Author: CaoHongFa
 * Description: description
 * -----
 * todo: modified
 * -----
 * Copyright (c) 2023
 */

/* ======================================================================================
 * includes
 */
#include <stdlib.h>
#include <string.h>

#include "cps_common.h"
#include "cps_device.h"
#include "fwlog.h"
#include "sqlite3.h"

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */

/* ======================================================================================
 * globals
 */

/* ======================================================================================
 * helper
 */

/* ======================================================================================
 * private implementation
 */

/* ======================================================================================
 * implementation
 */

int cps_monitor(void) {
    int      ret    = 0;
    int      time   = 20;
    int      rc     = 0;
    char*    errmsg = NULL;
    char**   result = NULL;
    int      row, col;

    while (1) {
        // 检测密码卡状态

        ret = open_cps_db();
        if (ret){
            sleep(2);
            continue ;
        }
           

        rc = sqlite3_get_table_printf(cps_db, "select device_name,ip from device_status_info", &result, &row, &col, &errmsg);
        if (rc != SQLITE_OK) {
            if (cps_db) {
                sqlite3_close(cps_db);
                cps_db = NULL;
            }
            sleep(2);
            continue;
        }
        

        // 每次查询前清空全局结构体
        memset(g_device_list, 0, sizeof(g_device_list));
        g_device_count = 0;

        // 将查询结果存入全局结构体
        if (row > 0) {
            int idx = 0;
            for (int j = 1; j <= row && idx < MAX_DEVICE_NUM; j++, idx++) {
                strncpy(g_device_list[idx].device_name, result[col * j], sizeof(g_device_list[idx].device_name) - 1);
                g_device_list[idx].device_name[sizeof(g_device_list[idx].device_name) - 1] = '\0';
                strncpy(g_device_list[idx].ip, result[col * j + 1], sizeof(g_device_list[idx].ip) - 1);
                g_device_list[idx].ip[sizeof(g_device_list[idx].ip) - 1] = '\0';
            }
            g_device_count = idx;
        }
        if (row > 0) {
            for (int j = 1; j <= row; j++) {
                ret = get_device_all_status(result[col * j+1],result[col * j]);
                if (ret == ERR_SRV_CONNECT) {
                    rc = sqlite3_exec_printf(
                        cps_db,
                        "UPDATE device_status_info SET device_status='2' where ip='%s'",
                        0,
                        0,
                        &errmsg,
                        result[col * j+1]);

                        rc = sqlite3_exec_printf(
                        cps_db,
                        "UPDATE vsm_manage SET run_state=5 where host_ip='%s'",
                        0,
                        0,
                        &errmsg,
                        result[col * j+1]);
                        fw_log_write(2, LOG_WARNING, "dsp_msg=\"%s %s\"", gettext("device disconnected,ip is"),result[col * j+1]);
                }
            }
        }

        if (result) {
            sqlite3_free_table(result);
            result = NULL;
        }

        close_cps_db();
        sleep(time);
    }
}
