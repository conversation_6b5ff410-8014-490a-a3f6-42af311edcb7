
#ifndef _DATABASE_API_H
#define _DATABASE_API_H

typedef struct {
    int   tenant_id;
    int   type;
    char* name;
    int   id;
    char* sn;
    char* create_time;
    char* last_login;
    char* auth_info;
    int   login_status;
    char* login_ip;
    char* session_id;
    char* login_info;
    char* remark;
    int   login_type;
    char* pri_temp;
} USER_INFO_T;

typedef struct {
    char* name;
    char* account;
    char* password;
    long valid_time;
    char* create_time;
    char* corporate_name;
    char* telephone;
    char* phone_number;
    char* mail;
    char* regs_date;
    char* remark;
} TENANT_INFO_T;

typedef struct {
    char* device_name;
    int   device_type;
    char* ip;
    int   device_status;
    int   cpu_cores;
    float cpu_usage;
    int   remain_cpu;
    int   memory_size;
    float memory_usage;
    int   remain_mem;
    int   disk_size;
    float disk_usage;
    int   remain_disk;
    int   create_vsm_num;
    int   max_vsm_num;
    char* remark;
} DEVICE_INFO_T;

typedef struct {
    int flavor;
    int cpu;
    int mem;
    int disk;
} FLAVOR_INFO_T;


int _Close_DbFile(void);
int db_get_internal_algid(int stand_index);
int Db_DeleteAllKey(void);

/*获取设备dn值*/
int Db_getDeviceSn(char* device_sn);

/*权限管理*/
int db_check_user_info(char* sn, char* name, int type, int tenant_id);
int db_add_user_info(USER_INFO_T* admin);
int db_update_login_info(USER_INFO_T* admin);
int db_update_login_random(char* random, long time);
int db_update_check_random(char* random);

int db_get_manager_id(int type);
int db_select_manager_id_and_sn(char* name, int tenant_id, int* type, int* id, char* sn);
int db_delete_user(char* name, int type, int tenant_id);
int db_set_login_info_log_out(char* name, char* log_info, int tenant_id);
int db_get_login_info(char* name, int tenant_id, char* log_info);

int db_get_user_name_and_type(char* sn, char* name, int* type);
int db_get_user_type_and_id(char* sn, int* type, int* id);
int db_check_user_name_and_sn(char* sn, char* name);
int db_get_manager_num(int* admin_num, int* opera_num, int* audit_num);
int db_get_manager_num_by_ip_and_type(char* ip, char* session_id, int type);
int db_get_manager_num_with_login(int type);
int db_set_set_user_remark(char* name, int login_type, char* pri_temp, int tenant_id, char* remark);
int db_check_is_administrator(char* ip, char* session_id);

/*设备初始化*/
int db_set_device_init_status(int status);
int db_get_device_init_status(void);
int db_add_device(DEVICE_INFO_T device_info);
int db_set_device(DEVICE_INFO_T device_info);
int db_check_device_ip_and_name(char* name,char* ip);
int db_set_pub_key_fingp(char* ip, char* fingp);
int db_delete_device(char* name);
int db_set_flavor_info(FLAVOR_INFO_T flavor_info);

/*策略管理*/
int db_check_addr_info(char* start_ip, char* end_ip,char* subnet);
int db_set_addr_image(char* url,char* err_msg, int (*cb)(char* ip, char* signature, char* url));
int db_check_cb_addr(char* ip, char* err_msg, int (*cb)(char* ip, char* cb_ip));
int db_check_vlan_id(char *ovs_int_name, char* vlan_id, char* tenant_name);
int check_vlan_conflict(const char* new_vlan_ids, const char* existing_range);
/*租户管理*/
// 通过session与ip获取租户id
int db_get_tenant_id_by_session_ip(char* session_id, char* ip);
int db_get_tenant_name(int tenant_id, char* tenant_name);
int db_tenant_add(TENANT_INFO_T* tenant);
int db_tenant_set(TENANT_INFO_T* tenant);
int db_tenant_reset_bind_key(char* tenant_name);
int db_tenant_del(int tenant_id);
int db_check_tenant_src(int tenant_id);
int db_tenant_on_off(void);

/*工单管理*/
int db_work_order_check_vsm(char* host_name, int vsm_cpu,int vsm_mem);
int db_work_order_get_tenant_src(char* host_name,char* tenant_name,int* allocate_vsm_cpu, int* allocate_vsm_mem);
int db_work_order_get_tenant(int wo_index, char* tenant_name, char* tenant_id);
int db_set_work_order_approval(
    int   wo_index,
    int   opinion,
    char* session_id,
    char* annotations,
    char* time_str,
    int   vsm_num);
int db_work_order_invalid(char* tenant_name);
int db_work_order_update_ip_info(char* tenant_name,char* vsm_id);

/*ip管理*/
int db_allot_idle_ip(int num, char* out_addr, char* tenant_name);

/*common*/
int db_del_data(char* table_name, char* format, ...);
int db_insert_data(char* table_name, char* format, ...);
int db_get_num(char* table_name, char* format, ...);
#endif
