/*
 * Project: base
 * Moudle:cps
 * File: cps_rights.h
 * Created Date: 2023-08-29 14:46:22
 * Author: caohongfa
 * Description: caohongfa
 * -----
 * todo: modified
 * -----
 * Copyright (c) Inc
 */
#ifndef _cps_RIGHTS_H
#define _cps_RIGHTS_H

/* ======================================================================================
 * includes
 */

/* ======================================================================================
 * macros
 */

/* ======================================================================================
 * types
 */

/* ======================================================================================
 * declaration
 */
int cps_rights_list_ukey(int argc,char* argv[]);
int cps_rights_check_user(int argc,char* argv[]);
int cps_rights_add_user(int argc,char* argv[]);
int cps_rights_remote_add_user(int argc,char* argv[]);
int cps_rights_add_user_bind_ukey(int argc,char* argv[]);
int cps_rights_remote_add_user_bind_ukey(int argc,char* argv[]);
int cps_rights_add_user_special(int argc,char* argv[]);
int cps_rights_remote_add_user_special(int argc,char* argv[]);
int cps_rights_del_user(int argc,char* argv[]);
int cps_rights_login(int argc,char* argv[]);
int cps_rights_remote_login(int argc,char* argv[]);
int cps_rights_get_login_random(int argc,char* argv[]);
int cps_rights_logout(int argc,char* argv[]);
int cps_rights_logout_by_ip(int argc,char* argv[]);
int cps_rights_get_status(int argc,char* argv[]);
int cps_rights_check_privileage(int argc,char* argv[]);
int cps_rights_set_pin(int argc,char* argv[]);
int cps_rights_set_user_info(int argc,char* argv[]);
#endif // _cps_RIGHTS_H